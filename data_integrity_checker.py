"""
Data Integrity Checker
Detects missing 15-minute intervals and fetches missing data
Ensures complete trading data for all symbols
"""
import datetime
import sqlite3
import logging
import time
from typing import List, Dict, Tu<PERSON>, Set
import pyotp
from SmartApi import SmartConnect
from config import Config, DATA_FETCH_CONFIG
from symbol_manager import SymbolManager
from database_setup import DatabaseManager

class DataIntegrityChecker:
    def __init__(self, config_file: str = "angelone_smartapi_config.json"):
        self.config = Config(config_file)
        self.smart_api = None
        self.symbol_manager = SymbolManager()
        self.db_manager = DatabaseManager()
        self.logger = self._setup_logging()
        
        # Trading hours configuration
        self.MARKET_OPEN_HOUR = 9
        self.MARKET_OPEN_MINUTE = 15
        self.MARKET_CLOSE_HOUR = 15
        self.MARKET_CLOSE_MINUTE = 15
        
        # API rate limit - More conservative for data integrity checks
        self.REQUEST_DELAY_SECONDS = 2.0  # Increased from 1.0 to 2.0 seconds for startup
        self.MAX_RETRIES = 2  # Reduced retries to avoid long delays
        self.RETRY_DELAYS = [5.0, 15.0]  # Longer backoff delays
        
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        # Create logs directory if it doesn't exist
        import os
        os.makedirs('logs', exist_ok=True)

        # Configure logging with UTF-8 encoding
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/data_integrity_checker.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    def connect_to_api(self) -> bool:
        """Connect to Angel One SmartAPI"""
        try:
            credentials = self.config.api_credentials
            
            # Generate TOTP
            totp = pyotp.TOTP(credentials["totp_secret"])
            totp_code = totp.now()
            
            # Initialize SmartConnect
            self.smart_api = SmartConnect(api_key=credentials["api_key"])
            
            # Login
            data = self.smart_api.generateSession(
                clientCode=credentials["username"],
                password=credentials["password"],
                totp=totp_code
            )
            
            if data and data.get('status'):
                self.logger.info("Connected to Angel One API for data integrity check")
                return True
            else:
                self.logger.error(f"API connection failed: {data}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error connecting to API: {e}")
            return False
    
    def get_expected_intervals(self, trading_date: datetime.date) -> List[datetime.datetime]:
        """Get list of expected 15-minute intervals for a trading day (FIXED FOR TRADING HOURS)"""
        intervals = []

        # Start from market open
        current_time = datetime.datetime.combine(
            trading_date,
            datetime.time(self.MARKET_OPEN_HOUR, self.MARKET_OPEN_MINUTE)
        )

        # End at market close
        market_close = datetime.datetime.combine(
            trading_date,
            datetime.time(self.MARKET_CLOSE_HOUR, self.MARKET_CLOSE_MINUTE)
        )

        # Get current time
        now = datetime.datetime.now()

        # FIXED LOGIC: Only check intervals that have already occurred
        if trading_date == datetime.date.today():
            # For today, only check intervals up to current time (not future intervals)
            if now.time() >= datetime.time(9, 15):  # Market has started today
                # Only check intervals up to current time, not future ones
                current_interval_time = datetime.datetime.combine(trading_date, now.time())
                # Round down to the nearest 15-minute interval
                minutes = (current_interval_time.minute // 15) * 15
                current_interval_time = current_interval_time.replace(minute=minutes, second=0, microsecond=0)

                # Don't go beyond market close
                end_time = min(current_interval_time, market_close)
                self.logger.info(f"📊 TRADING DAY: Checking intervals from 9:15 to {end_time.strftime('%H:%M')} (current time)")
            else:
                # Before market open, no intervals expected yet
                self.logger.info(f"📊 BEFORE MARKET: No intervals expected yet")
                return []
        else:
            # For past dates, include all intervals
            end_time = market_close
            self.logger.info(f"📊 PAST DATE: Checking all intervals for {trading_date}")

        # Generate ALL 15-minute intervals for the trading day
        while current_time <= end_time:
            intervals.append(current_time)
            current_time += datetime.timedelta(minutes=15)

        self.logger.info(f"📊 Expected intervals for {trading_date}: {len(intervals)} intervals")
        self.logger.info(f"📊 From {intervals[0].strftime('%H:%M')} to {intervals[-1].strftime('%H:%M')}")

        return intervals
    
    def get_existing_intervals_for_symbol(self, symbol: str, trading_date: datetime.date) -> Set[datetime.datetime]:
        """Get existing intervals for a symbol from database with improved duplicate detection"""
        try:
            # Use direct SQLite connection for better control
            conn = sqlite3.connect('Data/trading_data.db')
            cursor = conn.cursor()

            # More precise query to get exact intervals
            query = """
            SELECT DISTINCT timestamp
            FROM trading_data
            WHERE symbol = ? AND DATE(timestamp) = ?
            ORDER BY timestamp
            """

            cursor.execute(query, (symbol, trading_date.strftime('%Y-%m-%d')))
            results = cursor.fetchall()
            conn.close()

            # Convert to datetime objects with robust parsing
            existing_intervals = set()
            for row in results:
                timestamp_str = row[0]
                try:
                    # Handle various timestamp formats more robustly
                    if 'T' in timestamp_str:
                        # ISO format with T separator
                        timestamp = datetime.datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                        timestamp = timestamp.replace(tzinfo=None)
                    elif '.' in timestamp_str:
                        # Format with microseconds
                        timestamp = datetime.datetime.strptime(timestamp_str.split('.')[0], '%Y-%m-%d %H:%M:%S')
                    else:
                        # Standard format
                        timestamp = datetime.datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')

                    # Normalize to remove microseconds for comparison
                    timestamp = timestamp.replace(microsecond=0)
                    existing_intervals.add(timestamp)

                except Exception as e:
                    self.logger.warning(f"Failed to parse timestamp '{timestamp_str}' for {symbol}: {e}")
                    continue

            self.logger.debug(f"📊 Found {len(existing_intervals)} existing intervals for {symbol} on {trading_date}")
            return existing_intervals

        except Exception as e:
            self.logger.error(f"Error getting existing intervals for {symbol}: {e}")
            return set()
    
    def find_missing_intervals(self, symbol: str, trading_date: datetime.date) -> List[datetime.datetime]:
        """Find missing intervals for a symbol with enhanced duplicate prevention"""
        expected_intervals = set(self.get_expected_intervals(trading_date))
        existing_intervals = self.get_existing_intervals_for_symbol(symbol, trading_date)

        # Enhanced logging for debugging
        self.logger.debug(f"📊 {symbol}: Expected {len(expected_intervals)} intervals, Found {len(existing_intervals)} existing")

        missing_intervals = expected_intervals - existing_intervals

        # Sort missing intervals chronologically
        missing_list = sorted(list(missing_intervals))

        if missing_list:
            self.logger.info(f"📊 MISSING DATA: {symbol} needs {len(missing_list)}/{len(expected_intervals)} intervals on {trading_date}")
            # Only log first few missing intervals to avoid spam
            for i, interval in enumerate(missing_list[:3]):
                self.logger.info(f"  Missing: {interval.strftime('%H:%M')}")
            if len(missing_list) > 3:
                self.logger.info(f"  ... and {len(missing_list) - 3} more intervals")
        else:
            self.logger.debug(f"✅ COMPLETE: {symbol} has all {len(existing_intervals)} intervals on {trading_date}")

        return missing_list
    
    def fetch_missing_candle(self, symbol: str, token: str, missing_time: datetime.datetime) -> Dict:
        """Fetch a specific missing candle for a symbol with improved retry logic and duplicate prevention"""

        # PRE-CHECK: Verify this interval is actually missing before making API call
        trading_date = missing_time.date()
        existing_intervals = self.get_existing_intervals_for_symbol(symbol, trading_date)

        if missing_time in existing_intervals:
            self.logger.info(f"⚠️ SKIP: {symbol} at {missing_time.strftime('%H:%M')} already exists in database")
            return None

        for attempt in range(self.MAX_RETRIES):
            try:
                if not self.smart_api:
                    if not self.connect_to_api():
                        self.logger.error(f"Failed to connect to API for {symbol}")
                        return None

                # Create time range for the specific interval
                from_time = missing_time
                to_time = missing_time + datetime.timedelta(minutes=15)

                from_date = from_time.strftime("%Y-%m-%d %H:%M")
                to_date = to_time.strftime("%Y-%m-%d %H:%M")

                # Fetch candle data
                params = {
                    "exchange": DATA_FETCH_CONFIG["exchange"],
                    "symboltoken": token,
                    "interval": DATA_FETCH_CONFIG["interval"],
                    "fromdate": from_date,
                    "todate": to_date
                }

                response = self.smart_api.getCandleData(params)

                # Check for API errors
                if response and not response.get('status'):
                    error_msg = response.get('message', 'Unknown error')

                    # Handle specific error cases
                    if 'invalid token' in error_msg.lower() or 'ag8001' in error_msg.lower():
                        self.logger.warning(f"Invalid token detected for {symbol}, attempting to reconnect...")
                        self.smart_api = None  # Force reconnection
                        if attempt < self.MAX_RETRIES - 1:
                            time.sleep(self.RETRY_DELAYS[attempt])
                            continue
                        else:
                            self.logger.error(f"Failed to reconnect after {self.MAX_RETRIES} attempts for {symbol}")
                            return None

                    # Handle rate limiting
                    elif any(keyword in error_msg.lower() for keyword in ['rate limit', 'access denied', 'exceeding access rate']):
                        if attempt < self.MAX_RETRIES - 1:
                            backoff_delay = self.RETRY_DELAYS[attempt]
                            self.logger.warning(f"Rate limit hit for {symbol} (attempt {attempt + 1}). Backing off for {backoff_delay}s...")
                            time.sleep(backoff_delay)
                            continue
                        else:
                            self.logger.error(f"Rate limit exceeded for {symbol} after {self.MAX_RETRIES} attempts")
                            return None
                    else:
                        self.logger.error(f"API error for {symbol}: {error_msg}")
                        return None

                if response and response.get('status') and response.get('data'):
                    candles = response['data']

                    # Find the candle that matches our missing time
                    for candle in candles:
                        candle_time_str = candle[0].replace('Z', '+00:00')
                        candle_time = datetime.datetime.fromisoformat(candle_time_str)
                        candle_time = candle_time.replace(tzinfo=None)

                        # Check if this candle matches our missing interval (within 1 minute tolerance)
                        time_diff = abs((candle_time - missing_time).total_seconds())
                        if time_diff <= 60:  # Within 1 minute
                            return {
                                "symbol": symbol,
                                "token": token,
                                "exchange": DATA_FETCH_CONFIG["exchange"],
                                "timestamp": candle_time,
                                "open_price": float(candle[1]),
                                "high_price": float(candle[2]),
                                "low_price": float(candle[3]),
                                "close_price": float(candle[4]),
                                "volume": int(candle[5]),
                                "interval_type": DATA_FETCH_CONFIG["interval"]
                            }

                self.logger.warning(f"No candle data found for {symbol} at {missing_time.strftime('%H:%M')}")
                return None

            except Exception as e:
                error_msg = str(e).lower()

                # Handle rate limiting exceptions
                if any(keyword in error_msg for keyword in ['rate limit', 'access denied', 'exceeding access rate']):
                    if attempt < self.MAX_RETRIES - 1:
                        backoff_delay = self.RETRY_DELAYS[attempt]
                        self.logger.warning(f"Rate limit exception for {symbol} (attempt {attempt + 1}). Backing off for {backoff_delay}s...")
                        time.sleep(backoff_delay)
                        continue
                    else:
                        self.logger.error(f"Rate limit exceeded for {symbol} after {self.MAX_RETRIES} attempts: {e}")
                        return None
                else:
                    self.logger.error(f"Exception fetching missing candle for {symbol} at {missing_time}: {e}")
                    if attempt < self.MAX_RETRIES - 1:
                        time.sleep(self.RETRY_DELAYS[attempt])
                        continue
                    return None

        return None
    
    def fill_missing_intervals_for_symbol(self, symbol: str, token: str, trading_date: datetime.date) -> int:
        """Fill missing intervals for a single symbol"""
        try:
            missing_intervals = self.find_missing_intervals(symbol, trading_date)
            
            if not missing_intervals:
                return 0
            
            filled_count = 0
            collected_data = []
            
            for missing_time in missing_intervals:
                self.logger.info(f"Fetching missing data for {symbol} at {missing_time.strftime('%H:%M')}")
                
                candle_data = self.fetch_missing_candle(symbol, token, missing_time)
                
                if candle_data:
                    collected_data.append(candle_data)
                    filled_count += 1
                    self.logger.info(f"[SUCCESS] Filled {symbol} at {missing_time.strftime('%H:%M')}: Close={candle_data['close_price']:.2f}")
                else:
                    self.logger.error(f"[FAILED] Failed to fill {symbol} at {missing_time.strftime('%H:%M')}")
                
                # Enhanced rate limiting with exponential backoff for failures
                if candle_data is None:
                    # If we failed to get data, increase delay to avoid hitting rate limits
                    backoff_delay = self.REQUEST_DELAY_SECONDS * 3  # Increased multiplier
                    self.logger.warning(f"Failed to fetch data, increasing delay to {backoff_delay}s")
                    time.sleep(backoff_delay)
                else:
                    # Normal rate limiting - use the conservative delay
                    time.sleep(self.REQUEST_DELAY_SECONDS)
            
            # Store all collected data in database
            if collected_data:
                self.db_manager.connect()
                success = self.db_manager.insert_trading_data(collected_data)
                if success:
                    self.logger.info(f"Stored {len(collected_data)} missing intervals for {symbol}")
                else:
                    self.logger.error(f"Failed to store missing data for {symbol}")
            
            return filled_count
            
        except Exception as e:
            self.logger.error(f"Error filling missing intervals for {symbol}: {e}")
            return 0

    def check_symbol_last_intervals(self, symbol: str, last_n_intervals: int, trading_date: datetime.date = None) -> Dict:
        """Check and fill missing data for last N intervals of a specific symbol"""
        try:
            if trading_date is None:
                trading_date = datetime.date.today()

            # Get token for the symbol
            symbols_with_tokens = self.symbol_manager.get_symbols_with_tokens()
            token = None
            for sym, tok in symbols_with_tokens:
                if sym == symbol:
                    token = tok
                    break

            if not token:
                self.logger.error(f"Token not found for symbol {symbol}")
                return {"symbol": symbol, "filled": 0, "error": "Token not found"}

            # Get all expected intervals for the trading date (only past/current intervals)
            all_expected_intervals = self.get_expected_intervals(trading_date)

            # Filter out future intervals for today - FIXED LOGIC
            if trading_date == datetime.date.today():
                now = datetime.datetime.now()

                # Only filter if we're during trading hours
                if 9 <= now.hour <= 15:
                    # Round down to the nearest 15-minute interval that has completed
                    minutes = (now.minute // 15) * 15
                    current_interval_time = now.replace(minute=minutes, second=0, microsecond=0)

                    # Only include intervals that have already occurred
                    all_expected_intervals = [interval for interval in all_expected_intervals if interval <= current_interval_time]
                    self.logger.info(f"📊 Filtered to {len(all_expected_intervals)} intervals up to {current_interval_time.strftime('%H:%M')} (current time: {now.strftime('%H:%M')})")
                else:
                    # Outside trading hours - include all intervals for today
                    self.logger.info(f"📊 Outside trading hours - checking all {len(all_expected_intervals)} intervals for today")

            # Get the last N intervals (most recent ones that have occurred)
            if len(all_expected_intervals) >= last_n_intervals:
                last_intervals = all_expected_intervals[-last_n_intervals:]
            else:
                last_intervals = all_expected_intervals

            self.logger.info(f"📊 Checking last {len(last_intervals)} intervals for {symbol}")

            # Get existing intervals for this symbol
            existing_intervals = self.get_existing_intervals_for_symbol(symbol, trading_date)

            # Find missing intervals from the last N
            missing_intervals = []
            for interval in last_intervals:
                if interval not in existing_intervals:
                    missing_intervals.append(interval)

            if not missing_intervals:
                return {"symbol": symbol, "filled": 0, "message": "No missing intervals"}

            # Fill missing intervals
            filled_count = 0
            collected_data = []

            for missing_time in missing_intervals:
                self.logger.info(f"Fetching missing data for {symbol} at {missing_time.strftime('%H:%M')}")

                candle_data = self.fetch_missing_candle(symbol, token, missing_time)

                if candle_data:
                    collected_data.append(candle_data)
                    filled_count += 1
                    self.logger.info(f"[SUCCESS] Filled {symbol} at {missing_time.strftime('%H:%M')}: Close={candle_data['close_price']:.2f}")
                else:
                    self.logger.error(f"[FAILED] Failed to fill {symbol} at {missing_time.strftime('%H:%M')}")

                # Rate limiting
                time.sleep(self.REQUEST_DELAY_SECONDS)

            # Store collected data
            if collected_data:
                self.db_manager.connect()
                success = self.db_manager.insert_trading_data(collected_data)
                if success:
                    self.logger.info(f"Stored {len(collected_data)} missing intervals for {symbol}")
                else:
                    self.logger.error(f"Failed to store missing data for {symbol}")

            return {
                "symbol": symbol,
                "filled": filled_count,
                "checked_intervals": len(last_intervals),
                "missing_found": len(missing_intervals),
                "success": True
            }

        except Exception as e:
            self.logger.error(f"Error checking last intervals for {symbol}: {e}")
            return {"symbol": symbol, "filled": 0, "error": str(e), "success": False}

    def check_symbol_specific_intervals(self, symbol: str, target_intervals: List[datetime.datetime]) -> Dict:
        """Check and fill missing data for specific intervals of a symbol (across multiple days)"""
        try:
            # Get token for the symbol
            symbols_with_tokens = self.symbol_manager.get_symbols_with_tokens()
            token = None
            for sym, tok in symbols_with_tokens:
                if sym == symbol:
                    token = tok
                    break

            if not token:
                self.logger.error(f"Token not found for symbol {symbol}")
                return {"symbol": symbol, "filled": 0, "error": "Token not found"}

            self.logger.info(f"📊 Checking {len(target_intervals)} specific intervals for {symbol}")
            if target_intervals:
                self.logger.info(f"📅 From: {target_intervals[-1].strftime('%Y-%m-%d %H:%M')} to {target_intervals[0].strftime('%Y-%m-%d %H:%M')}")

            # Get existing intervals for this symbol across all dates
            existing_intervals = set()

            # Group intervals by date to check efficiently
            intervals_by_date = {}
            for interval in target_intervals:
                date_key = interval.date()
                if date_key not in intervals_by_date:
                    intervals_by_date[date_key] = []
                intervals_by_date[date_key].append(interval)

            # Get existing intervals for each date
            for trading_date, date_intervals in intervals_by_date.items():
                date_existing = self.get_existing_intervals_for_symbol(symbol, trading_date)
                existing_intervals.update(date_existing)

            # Find missing intervals from the target list
            missing_intervals = []
            for interval in target_intervals:
                if interval not in existing_intervals:
                    missing_intervals.append(interval)

            if not missing_intervals:
                return {"symbol": symbol, "filled": 0, "message": "No missing intervals"}

            self.logger.info(f"📊 Found {len(missing_intervals)} missing intervals for {symbol}")

            # Fill missing intervals
            filled_count = 0
            collected_data = []

            for missing_time in missing_intervals:
                self.logger.info(f"Fetching missing data for {symbol} at {missing_time.strftime('%Y-%m-%d %H:%M')}")

                candle_data = self.fetch_missing_candle(symbol, token, missing_time)

                if candle_data:
                    collected_data.append(candle_data)
                    filled_count += 1
                    self.logger.info(f"[SUCCESS] Filled {symbol} at {missing_time.strftime('%Y-%m-%d %H:%M')}: Close={candle_data['close_price']:.2f}")
                else:
                    self.logger.error(f"[FAILED] Failed to fill {symbol} at {missing_time.strftime('%Y-%m-%d %H:%M')}")

                # Rate limiting
                time.sleep(self.REQUEST_DELAY_SECONDS)

            # Store collected data
            if collected_data:
                self.db_manager.connect()
                success = self.db_manager.insert_trading_data(collected_data)
                if success:
                    self.logger.info(f"Stored {len(collected_data)} missing intervals for {symbol}")
                else:
                    self.logger.error(f"Failed to store missing data for {symbol}")

            return {
                "symbol": symbol,
                "filled": filled_count,
                "checked_intervals": len(target_intervals),
                "missing_found": len(missing_intervals),
                "success": True
            }

        except Exception as e:
            self.logger.error(f"Error checking specific intervals for {symbol}: {e}")
            return {"symbol": symbol, "filled": 0, "error": str(e), "success": False}

    def check_and_fill_missing_data(self, trading_date: datetime.date = None, skip_complete: bool = True, startup_mode: bool = False) -> Dict:
        """Check and fill missing data for all symbols"""
        try:
            if trading_date is None:
                trading_date = datetime.date.today()

            if startup_mode:
                self.logger.info(f"[STARTUP MODE] Starting limited data integrity check for {trading_date}")
            else:
                self.logger.info(f"[INTEGRITY CHECK] Starting data integrity check for {trading_date}")

            # Get all symbols with tokens (ALPHABETICAL ORDER for data integrity)
            symbols_with_tokens = self.symbol_manager.get_symbols_with_tokens()

            if not symbols_with_tokens:
                self.logger.error("No symbols with tokens found")
                return {"total": 0, "checked": 0, "filled": 0, "skipped": 0}

            # In startup mode, limit to first 10 symbols to avoid API rate limits
            if startup_mode:
                symbols_with_tokens = symbols_with_tokens[:10]
                self.logger.info(f"[STARTUP MODE] Limited to first {len(symbols_with_tokens)} symbols to avoid API rate limits")

            total_symbols = len(symbols_with_tokens)
            checked_symbols = 0
            skipped_symbols = 0
            total_filled = 0

            # Get expected interval count for quick comparison
            expected_intervals = self.get_expected_intervals(trading_date)
            expected_count = len(expected_intervals)

            for symbol, token in symbols_with_tokens:
                # Enhanced skip logic with better logging
                if skip_complete:
                    existing_intervals = self.get_existing_intervals_for_symbol(symbol, trading_date)
                    if len(existing_intervals) >= expected_count:
                        skipped_symbols += 1
                        self.logger.info(f"✅ SKIP: {symbol} already has complete data ({len(existing_intervals)}/{expected_count}) - No API calls needed")
                        continue
                    elif len(existing_intervals) > 0:
                        self.logger.info(f"📊 PARTIAL: {symbol} has {len(existing_intervals)}/{expected_count} intervals - will fill missing")
                    else:
                        self.logger.info(f"🆕 NEW: {symbol} has no data - will fetch all {expected_count} intervals")

                self.logger.info(f"🔍 Processing {symbol} for missing intervals...")

                filled_count = self.fill_missing_intervals_for_symbol(symbol, token, trading_date)

                checked_symbols += 1
                total_filled += filled_count

                if filled_count > 0:
                    self.logger.info(f"✅ COMPLETE: {symbol} filled {filled_count} missing intervals")
                else:
                    self.logger.info(f"✅ COMPLETE: {symbol} no missing intervals found")

            result = {
                "total": total_symbols,
                "checked": checked_symbols,
                "skipped": skipped_symbols,
                "filled": total_filled,
                "date": trading_date.isoformat(),  # Convert date to string for JSON serialization
                "success": True  # Add success flag
            }

            self.logger.info(f"[SUMMARY] Data integrity check completed:")
            self.logger.info(f"  Total symbols: {total_symbols}")
            self.logger.info(f"  Symbols checked: {checked_symbols}")
            self.logger.info(f"  Symbols skipped (complete): {skipped_symbols}")
            self.logger.info(f"  Total intervals filled: {total_filled}")

            # 🚀 TRIGGER TRADING ANALYSIS AFTER DATA INTEGRITY CHECK COMPLETES
            if total_filled > 0:
                self.logger.info(f"✅ Data integrity check filled {total_filled} intervals - triggering trading analysis...")
                self.trigger_trading_analysis_after_integrity_check()
            else:
                self.logger.info("ℹ️ No data filled during integrity check - skipping trading analysis trigger")

            return result

        except Exception as e:
            self.logger.error(f"Error in data integrity check: {e}")
            return {"total": 0, "checked": 0, "filled": 0, "skipped": 0, "success": False, "error": str(e)}



    def trigger_trading_analysis_after_integrity_check(self):
        """Trigger trading analysis after data integrity check completes"""
        try:
            self.logger.info("⚡ DATA INTEGRITY CHECK COMPLETED - TRIGGERING TRADING ANALYSIS...")

            # Import here to avoid circular imports
            import flask_app

            # Get the global trading engine instance
            if hasattr(flask_app, 'trading_engine') and flask_app.trading_engine:
                self.logger.info("🎯 Found trading engine - starting comprehensive analysis...")

                # Check if trading is active
                if hasattr(flask_app, 'trading_active') and flask_app.trading_active:
                    # Trigger immediate analysis in a separate thread for speed
                    import threading
                    analysis_thread = threading.Thread(
                        target=flask_app.trading_engine.process_immediate_analysis,
                        daemon=True
                    )
                    analysis_thread.start()
                    self.logger.info("✅ TRADING ANALYSIS TRIGGERED - Processing all 222 symbols with detailed logs...")
                else:
                    self.logger.warning("⚠️ Trading engine found but trading is not active - skipping analysis")
            else:
                self.logger.error("❌ Trading engine not available for analysis")

        except Exception as e:
            self.logger.error(f"❌ Error triggering trading analysis: {e}")
            import traceback
            self.logger.error(f"Full traceback: {traceback.format_exc()}")

    def get_data_completeness_report(self, trading_date: datetime.date = None) -> Dict:
        """Generate a completeness report for all symbols"""
        try:
            if trading_date is None:
                trading_date = datetime.date.today()
            
            expected_intervals = self.get_expected_intervals(trading_date)
            expected_count = len(expected_intervals)
            
            symbols_with_tokens = self.symbol_manager.get_symbols_with_tokens()
            
            report = {
                "date": trading_date,
                "expected_intervals": expected_count,
                "symbols": {},
                "summary": {
                    "total_symbols": len(symbols_with_tokens),
                    "complete_symbols": 0,
                    "incomplete_symbols": 0,
                    "total_missing": 0
                }
            }
            
            for symbol, token in symbols_with_tokens:
                existing_intervals = self.get_existing_intervals_for_symbol(symbol, trading_date)
                missing_count = expected_count - len(existing_intervals)
                
                report["symbols"][symbol] = {
                    "existing": len(existing_intervals),
                    "missing": missing_count,
                    "completeness": (len(existing_intervals) / expected_count) * 100
                }
                
                if missing_count == 0:
                    report["summary"]["complete_symbols"] += 1
                else:
                    report["summary"]["incomplete_symbols"] += 1
                    report["summary"]["total_missing"] += missing_count
            
            return report
            
        except Exception as e:
            self.logger.error(f"Error generating completeness report: {e}")
            return {}

def main():
    """Main function for data integrity checking"""
    import argparse

    parser = argparse.ArgumentParser(description='Data Integrity Checker')
    parser.add_argument('--date', help='Trading date (YYYY-MM-DD), default: today')
    parser.add_argument('--report', action='store_true', help='Generate completeness report only')
    parser.add_argument('--symbol', help='Check specific symbol only')
    parser.add_argument('--no-skip', action='store_true', help='Do not skip symbols with complete data')

    args = parser.parse_args()
    
    checker = DataIntegrityChecker()
    
    # Parse date
    if args.date:
        trading_date = datetime.datetime.strptime(args.date, '%Y-%m-%d').date()
    else:
        trading_date = datetime.date.today()
    
    if args.report:
        # Generate report only
        print("📊 DATA COMPLETENESS REPORT")
        print("=" * 50)
        
        report = checker.get_data_completeness_report(trading_date)
        
        if report:
            print(f"Date: {report['date']}")
            print(f"Expected intervals per symbol: {report['expected_intervals']}")
            print(f"Total symbols: {report['summary']['total_symbols']}")
            print(f"Complete symbols: {report['summary']['complete_symbols']}")
            print(f"Incomplete symbols: {report['summary']['incomplete_symbols']}")
            print(f"Total missing intervals: {report['summary']['total_missing']}")
            print()
            
            # Show incomplete symbols
            if report['summary']['incomplete_symbols'] > 0:
                print("Incomplete symbols:")
                for symbol, data in report['symbols'].items():
                    if data['missing'] > 0:
                        print(f"  {symbol}: {data['existing']}/{report['expected_intervals']} ({data['completeness']:.1f}%) - Missing: {data['missing']}")
    
    else:
        # Run integrity check
        print("🔍 DATA INTEGRITY CHECKER")
        print("=" * 50)
        print(f"Checking data integrity for {trading_date}")
        print("This will detect and fill missing 15-minute intervals")
        print()
        
        skip_complete = not args.no_skip
        result = checker.check_and_fill_missing_data(trading_date, skip_complete=skip_complete)

        print(f"✅ Integrity check completed:")
        print(f"  Total symbols: {result.get('total', 0)}")
        print(f"  Symbols checked: {result.get('checked', 0)}")
        print(f"  Symbols skipped: {result.get('skipped', 0)}")
        print(f"  Intervals filled: {result.get('filled', 0)}")

        if result.get('filled', 0) > 0:
            print(f"🎉 Successfully filled {result['filled']} missing intervals!")
        else:
            print("✅ No missing intervals found - data is complete!")

if __name__ == "__main__":
    main()
