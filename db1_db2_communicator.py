#!/usr/bin/env python3
"""
DB1-DB2 Millisecond Communication System
Handles high-speed signal transmission between databases
"""

import queue
import threading
import time
import json
import logging
from datetime import datetime
from dataclasses import dataclass
from typing import Optional, Dict, Any

@dataclass
class TradingSignal:
    """Trading signal data structure"""
    symbol: str
    signal_type: str  # 'BUY' or 'SELL'
    price: float
    timestamp_ns: int
    pattern_info: Dict[str, Any]
    source: str = 'DB1'
    
    def to_dict(self):
        return {
            'symbol': self.symbol,
            'signal_type': self.signal_type,
            'price': self.price,
            'timestamp_ns': self.timestamp_ns,
            'pattern_info': self.pattern_info,
            'source': self.source
        }

@dataclass
class ActivePosition:
    """Active position data structure"""
    symbol: str
    buy_price: float
    shares_quantity: int
    investment: float
    target_price: float
    buy_time: datetime
    current_profit: float
    status: str
    timestamp_ns: int
    
    def to_dict(self):
        return {
            'symbol': self.symbol,
            'buy_price': self.buy_price,
            'shares_quantity': self.shares_quantity,
            'investment': self.investment,
            'target_price': self.target_price,
            'buy_time': self.buy_time.isoformat(),
            'current_profit': self.current_profit,
            'status': self.status,
            'timestamp_ns': self.timestamp_ns
        }

class DB1_DB2_Communicator:
    """
    High-speed communication system between DB1 and DB2
    Achieves millisecond-level signal transmission
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # High-speed queues for communication
        self.db1_to_db2_signals = queue.Queue(maxsize=1000)
        self.db2_to_db1_positions = queue.Queue(maxsize=1000)
        self.db2_to_db1_confirmations = queue.Queue(maxsize=1000)
        
        # Performance tracking
        self.transmission_times = []
        self.is_running = True
        
        # Start communication monitoring threads
        self.start_communication_threads()
        
        self.logger.info("✅ DB1-DB2 Communicator initialized with millisecond communication")
    
    def start_communication_threads(self):
        """Start background threads for communication monitoring"""
        # Thread to monitor queue sizes and performance
        monitor_thread = threading.Thread(target=self._monitor_performance, daemon=True)
        monitor_thread.start()
        
        self.logger.info("🔄 Communication monitoring threads started")
    
    def send_signal_to_db2(self, signal: TradingSignal) -> bool:
        """
        Send trading signal from DB1 to DB2 with millisecond precision
        Returns: True if sent successfully, False otherwise
        """
        try:
            start_time = time.time_ns()
            
            # Add nanosecond timestamp
            signal.timestamp_ns = start_time
            
            # Put signal in queue (non-blocking)
            self.db1_to_db2_signals.put_nowait(signal)
            
            # Calculate transmission time
            transmission_time_ms = (time.time_ns() - start_time) / 1_000_000
            self.transmission_times.append(transmission_time_ms)
            
            self.logger.info(f"📤 DB1→DB2 SIGNAL: {signal.signal_type} {signal.symbol} @ ₹{signal.price:.2f} "
                           f"(Transmission: {transmission_time_ms:.3f}ms)")
            
            return True
            
        except queue.Full:
            self.logger.error(f"❌ DB1→DB2 queue full! Signal dropped: {signal.symbol}")
            return False
        except Exception as e:
            self.logger.error(f"❌ Error sending signal to DB2: {e}")
            return False
    
    def receive_signal_from_db1(self, timeout_ms: float = 1.0) -> Optional[TradingSignal]:
        """
        Receive trading signal in DB2 from DB1
        Args: timeout_ms - timeout in milliseconds
        Returns: TradingSignal or None if no signal available
        """
        try:
            timeout_seconds = timeout_ms / 1000.0
            signal = self.db1_to_db2_signals.get(timeout=timeout_seconds)
            
            # Calculate processing delay
            processing_delay_ms = (time.time_ns() - signal.timestamp_ns) / 1_000_000
            
            self.logger.info(f"📥 DB2←DB1 SIGNAL: {signal.signal_type} {signal.symbol} "
                           f"(Processing delay: {processing_delay_ms:.3f}ms)")
            
            return signal
            
        except queue.Empty:
            return None
        except Exception as e:
            self.logger.error(f"❌ Error receiving signal from DB1: {e}")
            return None
    
    def send_position_to_db1(self, position: ActivePosition) -> bool:
        """
        Send active position from DB2 to DB1 for monitoring
        Returns: True if sent successfully, False otherwise
        """
        try:
            start_time = time.time_ns()
            position.timestamp_ns = start_time
            
            self.db2_to_db1_positions.put_nowait(position)
            
            transmission_time_ms = (time.time_ns() - start_time) / 1_000_000
            
            self.logger.info(f"📤 DB2→DB1 POSITION: {position.symbol} "
                           f"(Profit: ₹{position.current_profit:.0f}, "
                           f"Transmission: {transmission_time_ms:.3f}ms)")
            
            return True
            
        except queue.Full:
            self.logger.error(f"❌ DB2→DB1 position queue full! Position dropped: {position.symbol}")
            return False
        except Exception as e:
            self.logger.error(f"❌ Error sending position to DB1: {e}")
            return False
    
    def receive_position_from_db2(self, timeout_ms: float = 1.0) -> Optional[ActivePosition]:
        """
        Receive active position in DB1 from DB2
        Args: timeout_ms - timeout in milliseconds
        Returns: ActivePosition or None if no position available
        """
        try:
            timeout_seconds = timeout_ms / 1000.0
            position = self.db2_to_db1_positions.get(timeout=timeout_seconds)
            
            processing_delay_ms = (time.time_ns() - position.timestamp_ns) / 1_000_000
            
            self.logger.info(f"📥 DB1←DB2 POSITION: {position.symbol} "
                           f"(Processing delay: {processing_delay_ms:.3f}ms)")
            
            return position
            
        except queue.Empty:
            return None
        except Exception as e:
            self.logger.error(f"❌ Error receiving position from DB2: {e}")
            return None
    
    def send_confirmation_to_db1(self, symbol: str, confirmation_type: str, details: Dict[str, Any]) -> bool:
        """
        Send confirmation status from DB2 to DB1
        """
        try:
            confirmation = {
                'symbol': symbol,
                'type': confirmation_type,
                'details': details,
                'timestamp_ns': time.time_ns()
            }
            
            self.db2_to_db1_confirmations.put_nowait(confirmation)
            
            self.logger.info(f"📤 DB2→DB1 CONFIRMATION: {confirmation_type} for {symbol}")
            return True
            
        except queue.Full:
            self.logger.error(f"❌ DB2→DB1 confirmation queue full!")
            return False
        except Exception as e:
            self.logger.error(f"❌ Error sending confirmation to DB1: {e}")
            return False
    
    def receive_confirmation_from_db2(self, timeout_ms: float = 1.0) -> Optional[Dict[str, Any]]:
        """
        Receive confirmation in DB1 from DB2
        """
        try:
            timeout_seconds = timeout_ms / 1000.0
            confirmation = self.db2_to_db1_confirmations.get(timeout=timeout_seconds)
            return confirmation
            
        except queue.Empty:
            return None
        except Exception as e:
            self.logger.error(f"❌ Error receiving confirmation from DB2: {e}")
            return None
    
    def get_queue_status(self) -> Dict[str, int]:
        """Get current queue sizes for monitoring"""
        return {
            'db1_to_db2_signals': self.db1_to_db2_signals.qsize(),
            'db2_to_db1_positions': self.db2_to_db1_positions.qsize(),
            'db2_to_db1_confirmations': self.db2_to_db1_confirmations.qsize()
        }
    
    def get_performance_stats(self) -> Dict[str, float]:
        """Get communication performance statistics"""
        if not self.transmission_times:
            return {'avg_transmission_ms': 0, 'max_transmission_ms': 0, 'min_transmission_ms': 0}
        
        return {
            'avg_transmission_ms': sum(self.transmission_times) / len(self.transmission_times),
            'max_transmission_ms': max(self.transmission_times),
            'min_transmission_ms': min(self.transmission_times),
            'total_transmissions': len(self.transmission_times)
        }
    
    def _monitor_performance(self):
        """Background thread to monitor communication performance"""
        while self.is_running:
            try:
                queue_status = self.get_queue_status()
                perf_stats = self.get_performance_stats()
                
                # Log performance every 60 seconds
                if len(self.transmission_times) > 0:
                    self.logger.info(f"📊 COMMUNICATION PERFORMANCE: "
                                   f"Avg: {perf_stats['avg_transmission_ms']:.3f}ms, "
                                   f"Max: {perf_stats['max_transmission_ms']:.3f}ms, "
                                   f"Queues: {queue_status}")
                
                # Keep only last 1000 transmission times for memory efficiency
                if len(self.transmission_times) > 1000:
                    self.transmission_times = self.transmission_times[-1000:]
                
                time.sleep(60)  # Monitor every minute
                
            except Exception as e:
                self.logger.error(f"❌ Error in performance monitoring: {e}")
                time.sleep(60)
    
    def shutdown(self):
        """Gracefully shutdown the communicator"""
        self.is_running = False
        self.logger.info("🛑 DB1-DB2 Communicator shutdown")

# Global instance for system-wide use
communicator = DB1_DB2_Communicator()

def get_communicator() -> DB1_DB2_Communicator:
    """Get the global communicator instance"""
    return communicator
