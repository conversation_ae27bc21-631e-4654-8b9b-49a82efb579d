#!/usr/bin/env python3
"""
DB1 Signal Generator - Real-time Pattern Detection and Signal Generation
Works alongside data_integrity_checker.py and realtime_data_fetcher.py
Triggers immediately when data is fetched - NO STANDALONE LOOPS
"""

import sqlite3
import pandas as pd
import logging
import time
import threading
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Optional, Tuple
from db1_db2_communicator import TradingSignal, ActivePosition, get_communicator

class DB1_SignalGenerator:
    """
    DB1 Signal Generator - Real-time Pattern Detection & Position Management

    NEW RESPONSIBILITIES:
    1. Work alongside data_integrity_checker.py and realtime_data_fetcher.py
    2. Trigger immediately when data is fetched (no standalone loops)
    3. Implement rolling window pattern detection (FFFFR)
    4. Handle GOLD/SILVER/BRONZE priority categories
    5. Manage active positions and portfolio (moved from trading_engine.py)
    6. Generate BUY/SELL signals and send to DB2 via db1_db2_communicator.py

    DOES NOT EXECUTE TRADES - Only generates signals
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db_path = 'Data/trading_data.db'
        self.trading_db_path = 'Data/trading_operations.db'
        self.communicator = get_communicator()

        # Portfolio Management (moved from trading_engine.py)
        self.total_pot = 22200000.0  # ₹2.22 Cr
        self.investment_per_symbol = 100000.0  # ₹1 Lakh per symbol
        self.vault_amount = 0.0
        self.active_positions = {}
        self.completed_trades = []

        # Pattern detection parameters
        self.min_drop_percentage = 0.5  # Minimum 0.5% drop required
        self.profit_target = 800  # ₹800 profit target

        # Rolling window tracking for each symbol
        self.symbol_patterns = {}  # Track F/R patterns for each symbol

        # Priority categories
        self.priority_categories = {
            'GOLD': [],    # Active positions - highest priority
            'SILVER': [],  # Symbols close to patterns
            'BRONZE': [],  # Symbols with recent activity
            'REMAINING': []  # All other symbols
        }

        # Initialize database tables
        self._initialize_database_tables()

        # Load existing positions
        self._load_active_positions_from_database()

        self.logger.info("✅ DB1 Signal Generator initialized - Real-time pattern detection & position management")
    
    def trigger_analysis_after_data_fetch(self, source: str = "data_fetch", symbols_updated: List[str] = None):
        """
        🎯 MAIN TRIGGER METHOD - Called immediately after data is fetched
        Works alongside data_integrity_checker.py and realtime_data_fetcher.py

        Args:
            source: "data_integrity" or "realtime_fetch"
            symbols_updated: List of symbols that got new data
        """
        try:
            start_time = time.time()

            self.logger.info("=" * 80)
            self.logger.info(f"🎯 DB1 ANALYSIS TRIGGERED by {source.upper()} - {datetime.now().strftime('%H:%M:%S')}")
            self.logger.info("=" * 80)

            # Step 1: Update priority categories (GOLD/SILVER/BRONZE)
            self._update_priority_categories()

            # Step 2: Update active positions from DB2
            self._update_active_positions_from_db2()

            # Step 3: Analyze patterns for updated symbols (or all if None)
            target_symbols = symbols_updated if symbols_updated else self._get_all_symbols()
            buy_signals_generated = self._analyze_patterns_and_generate_buy_signals(target_symbols)

            # Step 4: Monitor active positions and generate SELL signals
            sell_signals_generated = self._monitor_positions_and_generate_sell_signals()

            # Step 5: Update rolling window patterns
            self._update_rolling_window_patterns(target_symbols)

            # Step 6: Log cycle summary
            cycle_time = time.time() - start_time
            self.logger.info(f"📊 DB1 ANALYSIS COMPLETE: {buy_signals_generated} BUY signals, "
                           f"{sell_signals_generated} SELL signals generated in {cycle_time:.2f}s")
            self.logger.info(f"🎯 Analyzed {len(target_symbols)} symbols triggered by {source}")

            return {
                'success': True,
                'buy_signals': buy_signals_generated,
                'sell_signals': sell_signals_generated,
                'symbols_analyzed': len(target_symbols),
                'cycle_time': cycle_time
            }

        except Exception as e:
            self.logger.error(f"❌ Error in DB1 analysis trigger: {e}")
            return {'success': False, 'error': str(e)}

    def _initialize_database_tables(self):
        """Initialize database tables for position management"""
        try:
            conn = sqlite3.connect(self.trading_db_path)
            cursor = conn.cursor()

            # Create trading positions table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS trading_positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                buy_price REAL NOT NULL,
                shares_quantity INTEGER NOT NULL,
                investment REAL NOT NULL,
                target_value REAL NOT NULL,
                target_price REAL NOT NULL,
                buy_time DATETIME NOT NULL,
                sell_time DATETIME,
                sell_price REAL,
                actual_profit REAL,
                status TEXT NOT NULL DEFAULT 'ACTIVE',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # Create portfolio status table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS portfolio_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                total_pot REAL NOT NULL DEFAULT 22200000.0,
                vault_amount REAL NOT NULL DEFAULT 0.0,
                active_positions INTEGER NOT NULL DEFAULT 0,
                completed_trades INTEGER NOT NULL DEFAULT 0,
                total_profit REAL NOT NULL DEFAULT 0.0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            conn.commit()
            conn.close()
            self.logger.info("✅ Database tables initialized for position management")

        except Exception as e:
            self.logger.error(f"❌ Error initializing database tables: {e}")

    def _load_active_positions_from_database(self):
        """Load existing active positions from database"""
        try:
            conn = sqlite3.connect(self.trading_db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT symbol, buy_price, shares_quantity, investment, target_price, buy_time
                FROM trading_positions
                WHERE status = 'ACTIVE'
            ''')

            positions = cursor.fetchall()
            conn.close()

            for position in positions:
                symbol, buy_price, quantity, investment, target_price, buy_time = position
                self.active_positions[symbol] = {
                    'symbol': symbol,
                    'buy_price': buy_price,
                    'quantity': quantity,
                    'investment_amount': investment,
                    'target_price': target_price,
                    'buy_time': buy_time,
                    'status': 'ACTIVE'
                }

            self.logger.info(f"✅ Loaded {len(self.active_positions)} active positions from database")

        except Exception as e:
            self.logger.error(f"❌ Error loading active positions: {e}")

    def _update_active_positions_from_db2(self):
        """Update active positions received from DB2"""
        try:
            updated_count = 0
            
            # Check for position updates from DB2
            while True:
                position = self.communicator.receive_position_from_db2(timeout_ms=10)
                if not position:
                    break
                
                self.active_positions[position.symbol] = position
                updated_count += 1
                
                self.logger.info(f"📥 POSITION UPDATE: {position.symbol} - "
                               f"Profit: ₹{position.current_profit:.0f}")
            
            if updated_count > 0:
                self.logger.info(f"📊 Updated {updated_count} active positions from DB2")
            
            self.logger.info(f"📈 ACTIVE POSITIONS: {len(self.active_positions)} symbols being monitored")

        except Exception as e:
            self.logger.error(f"❌ Error updating active positions from DB2: {e}")

    def _update_priority_categories(self):
        """Update GOLD/SILVER/BRONZE priority categories"""
        try:
            # Reset categories
            self.priority_categories = {'GOLD': [], 'SILVER': [], 'BRONZE': [], 'REMAINING': []}

            # Get all symbols
            all_symbols = self._get_all_symbols()

            # GOLD: Active positions (highest priority)
            for symbol in self.active_positions.keys():
                if symbol in all_symbols:
                    self.priority_categories['GOLD'].append(symbol)
                    all_symbols.remove(symbol)

            # SILVER: Symbols close to 4F+1R pattern (3F or 2F already detected)
            silver_symbols = []
            for symbol in all_symbols[:]:
                pattern_data = self.symbol_patterns.get(symbol, {})
                consecutive_falls = pattern_data.get('consecutive_falls', 0)
                if consecutive_falls >= 2:  # 2 or 3 falls already detected
                    silver_symbols.append(symbol)
                    all_symbols.remove(symbol)

            self.priority_categories['SILVER'] = silver_symbols[:11]  # Limit to 11 symbols

            # BRONZE: Symbols with recent activity or volatility
            bronze_symbols = all_symbols[:5]  # Take first 5 remaining symbols
            self.priority_categories['BRONZE'] = bronze_symbols
            for symbol in bronze_symbols:
                all_symbols.remove(symbol)

            # REMAINING: All other symbols
            self.priority_categories['REMAINING'] = all_symbols

            self.logger.info(f"📊 PRIORITY CATEGORIES UPDATED:")
            self.logger.info(f"🥇 GOLD: {len(self.priority_categories['GOLD'])} symbols (Active positions)")
            self.logger.info(f"🥈 SILVER: {len(self.priority_categories['SILVER'])} symbols (Close to pattern)")
            self.logger.info(f"🥉 BRONZE: {len(self.priority_categories['BRONZE'])} symbols (Recent activity)")
            self.logger.info(f"⚪ REMAINING: {len(self.priority_categories['REMAINING'])} symbols")

        except Exception as e:
            self.logger.error(f"❌ Error updating priority categories: {e}")

    def _get_all_symbols(self) -> List[str]:
        """Get all symbols from database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT DISTINCT symbol FROM trading_data ORDER BY symbol")
            symbols = [row[0] for row in cursor.fetchall()]
            conn.close()

            return symbols

        except Exception as e:
            self.logger.error(f"❌ Error getting symbols: {e}")
            return []

    def _update_rolling_window_patterns(self, symbols: List[str]):
        """Update rolling window patterns for symbols (F/R tracking)"""
        try:
            for symbol in symbols:
                # Get last 2 data points to calculate trend
                latest_data = self._get_latest_data_for_symbol(symbol, limit=2)

                if len(latest_data) >= 2:
                    # Calculate trend between last 2 points
                    current_price = latest_data[0]['close_price']
                    previous_price = latest_data[1]['close_price']

                    if symbol not in self.symbol_patterns:
                        self.symbol_patterns[symbol] = {
                            'consecutive_falls': 0,
                            'consecutive_rises': 0,
                            'last_trend': None,
                            'pattern_history': []
                        }

                    pattern_data = self.symbol_patterns[symbol]

                    if current_price < previous_price:
                        # FALL detected
                        trend = 'F'
                        if pattern_data['last_trend'] == 'F':
                            pattern_data['consecutive_falls'] += 1
                        else:
                            pattern_data['consecutive_falls'] = 1
                        pattern_data['consecutive_rises'] = 0
                    elif current_price > previous_price:
                        # RISE detected
                        trend = 'R'
                        if pattern_data['last_trend'] == 'R':
                            pattern_data['consecutive_rises'] += 1
                        else:
                            pattern_data['consecutive_rises'] = 1
                        pattern_data['consecutive_falls'] = 0
                    else:
                        # FLAT - no change
                        trend = 'FLAT'

                    pattern_data['last_trend'] = trend
                    pattern_data['pattern_history'].append({
                        'trend': trend,
                        'price': current_price,
                        'timestamp': datetime.now().isoformat()
                    })

                    # Keep only last 10 patterns
                    if len(pattern_data['pattern_history']) > 10:
                        pattern_data['pattern_history'] = pattern_data['pattern_history'][-10:]

                    # Log significant patterns
                    if pattern_data['consecutive_falls'] >= 3:
                        self.logger.info(f"📉 {symbol}: {pattern_data['consecutive_falls']} consecutive FALLS detected")
                    elif pattern_data['consecutive_falls'] == 4 and trend == 'R':
                        self.logger.info(f"🎯 {symbol}: 4F+1R PATTERN DETECTED! Ready for BUY signal analysis")

        except Exception as e:
            self.logger.error(f"❌ Error updating rolling window patterns: {e}")

    def _analyze_patterns_and_generate_buy_signals(self, target_symbols: List[str] = None) -> int:
        """
        Analyze 15-minute data for 4F+1R patterns and generate BUY signals
        Returns: Number of BUY signals generated
        """
        try:
            signals_generated = 0
            analyzed_count = 0

            # Determine symbols to analyze based on priority
            if target_symbols:
                symbols_to_analyze = target_symbols
                self.logger.info(f"📊 ANALYZING {len(symbols_to_analyze)} TARGET SYMBOLS for 4F+1R patterns...")
            else:
                # Use priority order: GOLD -> SILVER -> BRONZE -> REMAINING
                symbols_to_analyze = []
                symbols_to_analyze.extend(self.priority_categories['GOLD'])
                symbols_to_analyze.extend(self.priority_categories['SILVER'])
                symbols_to_analyze.extend(self.priority_categories['BRONZE'])
                symbols_to_analyze.extend(self.priority_categories['REMAINING'])

                self.logger.info(f"📊 ANALYZING ALL SYMBOLS IN PRIORITY ORDER:")
                self.logger.info(f"🥇 GOLD: {len(self.priority_categories['GOLD'])} symbols first")
                self.logger.info(f"🥈 SILVER: {len(self.priority_categories['SILVER'])} symbols second")
                self.logger.info(f"🥉 BRONZE: {len(self.priority_categories['BRONZE'])} symbols third")
                self.logger.info(f"⚪ REMAINING: {len(self.priority_categories['REMAINING'])} symbols last")

            for symbol in symbols_to_analyze:
                try:
                    analyzed_count += 1

                    # Log every 10th symbol to show progress
                    if analyzed_count % 10 == 0 or analyzed_count <= 5:
                        self.logger.info(f"🔍 Analyzing symbol {analyzed_count}/{len(symbols_to_analyze)}: {symbol}")

                    # Skip symbols with active positions (except GOLD category for monitoring)
                    if symbol in self.active_positions and symbol not in self.priority_categories['GOLD']:
                        self.logger.info(f"⏭️ SKIP {symbol}: Already has active position")
                        continue
                    
                    # Get latest 5 data points for 4F+1R pattern (or whatever is available)
                    latest_data = self._get_latest_data_points(symbol, count=5)

                    # Log data availability for debugging
                    if len(latest_data) < 5:
                        self.logger.info(f"📊 {symbol}: Only {len(latest_data)}/5 intervals available")

                    if len(latest_data) >= 3:  # Reduced requirement for testing
                        # Check for pattern with available data
                        pattern_result = self._detect_4fall_1rise_pattern_with_logging(symbol, latest_data)
                        if pattern_result:
                            current_price = latest_data[0]['close_price']

                            # Generate BUY signal
                            signal = self._create_buy_signal(symbol, current_price, latest_data)

                            if self.communicator.send_signal_to_db2(signal):
                                signals_generated += 1
                                self.logger.info(f"🎯 BUY SIGNAL GENERATED: {symbol} @ ₹{current_price:.2f}")
                                self.logger.info(f"📤 Signal sent to DB2 for R+R confirmation")

                                # Create pending position (will be activated by DB2 confirmation)
                                self._create_pending_position(symbol, current_price)
                            else:
                                self.logger.error(f"❌ Failed to send BUY signal for {symbol}")
                        else:
                            # Log why pattern was rejected (every 10th symbol to avoid spam)
                            if hash(symbol) % 10 == 0:
                                self.logger.info(f"⏭️ {symbol}: No 4F+1R pattern (latest: ₹{latest_data[0]['close_price']:.2f})")
                
                except Exception as e:
                    self.logger.error(f"❌ Error analyzing {symbol}: {e}")
                    continue
            
            self.logger.info(f"📊 BUY SIGNAL ANALYSIS COMPLETE: {analyzed_count} symbols analyzed, {signals_generated} signals generated")
            return signals_generated
            
        except Exception as e:
            self.logger.error(f"❌ Error in pattern analysis: {e}")
            return 0
    
    def _monitor_positions_and_generate_sell_signals(self) -> int:
        """
        Monitor active positions for ₹800 profit target and generate SELL signals
        Returns: Number of SELL signals generated
        """
        try:
            if not self.active_positions:
                self.logger.info("📊 No active positions to monitor for SELL signals")
                return 0
            
            self.logger.info(f"💰 MONITORING {len(self.active_positions)} POSITIONS FOR PROFIT TARGET...")
            
            signals_generated = 0
            
            for symbol, position in self.active_positions.items():
                try:
                    # Get current price
                    current_price = self._get_current_price(symbol)
                    
                    if current_price:
                        # Calculate current profit
                        current_profit = (current_price * position.shares_quantity) - position.investment
                        
                        self.logger.info(f"📊 {symbol}: Current ₹{current_price:.2f}, "
                                       f"Profit ₹{current_profit:.0f} (Target: ₹{self.profit_target})")
                        
                        # Check if profit target reached
                        if current_profit >= self.profit_target:
                            # Generate SELL signal
                            signal = self._create_sell_signal(symbol, current_price, position, current_profit)
                            
                            if self.communicator.send_signal_to_db2(signal):
                                signals_generated += 1
                                self.logger.info(f"🎯 SELL SIGNAL GENERATED: {symbol} @ ₹{current_price:.2f} "
                                               f"(Profit: ₹{current_profit:.0f})")
                            else:
                                self.logger.error(f"❌ Failed to send SELL signal for {symbol}")
                
                except Exception as e:
                    self.logger.error(f"❌ Error monitoring {symbol}: {e}")
                    continue
            
            self.logger.info(f"📊 SELL SIGNAL MONITORING COMPLETE: {signals_generated} signals generated")
            return signals_generated
            
        except Exception as e:
            self.logger.error(f"❌ Error in position monitoring: {e}")
            return 0
    
    def _detect_4fall_1rise_pattern(self, data_points: List[Dict]) -> bool:
        """
        Detect 4-FALL + 1-RISE pattern with 0.5% drop condition
        Args: data_points - List of 5 data points (most recent first)
        Returns: True if pattern detected, False otherwise
        """
        try:
            if len(data_points) < 5:
                return False
            
            # Extract prices in chronological order (reverse the list)
            prices = [point['close_price'] for point in reversed(data_points)]
            
            # Calculate trends from the 5 data points (4 trends)
            trends = []
            for i in range(1, len(prices)):
                if prices[i] > prices[i-1]:
                    trends.append("RISE")
                elif prices[i] < prices[i-1]:
                    trends.append("FALL")
                else:
                    trends.append("FLAT")
            
            # Check for 4-FALL + 1-RISE pattern (exactly 4 trends)
            if len(trends) == 4:
                expected_pattern = ["FALL", "FALL", "FALL", "FALL"]
                
                # Check if first 4 trends are FALL
                if trends == expected_pattern:
                    # Check if the 5th point (latest) is a RISE from the 4th point
                    if prices[4] > prices[3]:  # Latest price > previous price
                        
                        # Check 0.5% drop condition
                        start_price = prices[0]  # First price
                        lowest_price = min(prices[:-1])  # Lowest price before the final rise
                        
                        # Calculate percentage drop
                        percentage_drop = ((start_price - lowest_price) / start_price) * 100
                        
                        if percentage_drop >= self.min_drop_percentage:
                            self.logger.info(f"✅ 4F+1R Pattern + {percentage_drop:.2f}% Drop DETECTED!")
                            return True
                        else:
                            self.logger.debug(f"❌ 4F+1R Pattern found but insufficient drop: "
                                            f"{percentage_drop:.2f}% (need ≥{self.min_drop_percentage}%)")
            
            return False

        except Exception as e:
            self.logger.error(f"❌ Error in pattern detection: {e}")
            return False

    def _detect_4fall_1rise_pattern_with_logging(self, symbol: str, data_points: List[Dict]) -> bool:
        """
        Detect 4-FALL + 1-RISE pattern with detailed logging for debugging
        Args: symbol, data_points - List of 5 data points (most recent first)
        Returns: True if pattern detected, False otherwise
        """
        try:
            if len(data_points) < 3:
                self.logger.info(f"⏭️ {symbol}: Insufficient data ({len(data_points)}/3 intervals minimum)")
                return False

            # Adjust pattern based on available data
            if len(data_points) < 5:
                self.logger.info(f"📊 {symbol}: Using simplified pattern with {len(data_points)} intervals")

            # Extract prices in chronological order (reverse the list)
            prices = [point['close_price'] for point in reversed(data_points)]

            # Log price sequence for analysis
            price_str = " → ".join([f"₹{p:.2f}" for p in prices])
            self.logger.info(f"🔍 {symbol}: Analyzing pattern: {price_str}")

            # Calculate trends from the 5 data points (4 trends)
            trends = []
            for i in range(1, len(prices)):
                if prices[i] > prices[i-1]:
                    trends.append("RISE")
                elif prices[i] < prices[i-1]:
                    trends.append("FALL")
                else:
                    trends.append("FLAT")

            trend_str = " → ".join(trends)
            self.logger.info(f"📊 {symbol}: Trend pattern: {trend_str}")

            # Check for pattern based on available data
            if len(data_points) == 5 and len(trends) == 4:
                # Full 4F+1R pattern
                expected_pattern = ["FALL", "FALL", "FALL", "FALL"]
                if trends == expected_pattern:
                    self.logger.info(f"🔍 {symbol}: Checking full 4F+1R pattern")
            elif len(data_points) == 4 and len(trends) == 3:
                # Simplified 3F+1R pattern
                expected_pattern = ["FALL", "FALL", "FALL"]
                if trends == expected_pattern:
                    self.logger.info(f"🔍 {symbol}: Checking simplified 3F+1R pattern")
            elif len(data_points) == 3 and len(trends) == 2:
                # Basic 2F+1R pattern
                expected_pattern = ["FALL", "FALL"]
                if trends == expected_pattern:
                    self.logger.info(f"🔍 {symbol}: Checking basic 2F+1R pattern")
            else:
                self.logger.info(f"❌ {symbol}: Unsupported data length: {len(data_points)} points, {len(trends)} trends")
                return False

            # Check if pattern matches
            if trends[:len(expected_pattern)] == expected_pattern:
                # Check if the latest point is a RISE from the previous point
                latest_idx = len(prices) - 1
                prev_idx = latest_idx - 1

                if prices[latest_idx] > prices[prev_idx]:  # Latest price > previous price

                    # Check 0.5% drop condition
                    start_price = prices[0]  # First price
                    lowest_price = min(prices[:-1])  # Lowest price before the final rise

                    # Calculate percentage drop
                    percentage_drop = ((start_price - lowest_price) / start_price) * 100

                    self.logger.info(f"📉 {symbol}: Drop analysis: ₹{start_price:.2f} → ₹{lowest_price:.2f} = {percentage_drop:.2f}%")

                    # Reduce drop requirement for testing with limited data
                    min_drop = self.min_drop_percentage if len(data_points) >= 5 else 0.2  # Reduced requirement

                    if percentage_drop >= min_drop:
                        pattern_name = f"{len(expected_pattern)}F+1R"
                        self.logger.info(f"✅ {symbol}: {pattern_name} PATTERN CONFIRMED! Drop: {percentage_drop:.2f}% ≥ {min_drop}%")
                        return True
                    else:
                        self.logger.info(f"❌ {symbol}: Pattern found but insufficient drop: {percentage_drop:.2f}% < {min_drop}%")
                else:
                    self.logger.info(f"❌ {symbol}: No final RISE detected (₹{prices[prev_idx]:.2f} → ₹{prices[latest_idx]:.2f})")
            else:
                self.logger.info(f"❌ {symbol}: Pattern mismatch. Expected: {expected_pattern}, Got: {trend_str}")

            return False

        except Exception as e:
            self.logger.error(f"❌ Error in pattern detection for {symbol}: {e}")
            return False

    def _create_buy_signal(self, symbol: str, price: float, data_points: List[Dict]) -> TradingSignal:
        """Create BUY signal with pattern information"""
        pattern_info = {
            'pattern_type': '4F+1R',
            'drop_percentage': self._calculate_drop_percentage(data_points),
            'data_points': len(data_points),
            'detection_time': datetime.now().isoformat()
        }
        
        return TradingSignal(
            symbol=symbol,
            signal_type='BUY',
            price=price,
            timestamp_ns=time.time_ns(),
            pattern_info=pattern_info,
            source='DB1'
        )
    
    def _create_sell_signal(self, symbol: str, price: float, position: ActivePosition, profit: float) -> TradingSignal:
        """Create SELL signal with position information"""
        pattern_info = {
            'pattern_type': 'PROFIT_TARGET',
            'current_profit': profit,
            'target_profit': self.profit_target,
            'buy_price': position.buy_price,
            'profit_percentage': (profit / position.investment) * 100,
            'detection_time': datetime.now().isoformat()
        }
        
        return TradingSignal(
            symbol=symbol,
            signal_type='SELL',
            price=price,
            timestamp_ns=time.time_ns(),
            pattern_info=pattern_info,
            source='DB1'
        )
    
    def _calculate_drop_percentage(self, data_points: List[Dict]) -> float:
        """Calculate percentage drop from start to lowest point"""
        prices = [point['close_price'] for point in reversed(data_points)]
        start_price = prices[0]
        lowest_price = min(prices[:-1])
        return ((start_price - lowest_price) / start_price) * 100
    
    def _get_all_symbols(self) -> List[str]:
        """Get all symbols for analysis"""
        try:
            from symbol_manager import SymbolManager
            symbol_manager = SymbolManager()
            return symbol_manager.get_all_symbols()
        except Exception as e:
            self.logger.error(f"❌ Error getting symbols: {e}")
            return []
    
    def _get_latest_data_points(self, symbol: str, count: int = 5) -> List[Dict]:
        """Get latest N data points for a symbol"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            query = '''
            SELECT close_price, timestamp, open_price, high_price, low_price, volume
            FROM trading_data 
            WHERE symbol = ? 
            ORDER BY timestamp DESC 
            LIMIT ?
            '''
            
            df = pd.read_sql_query(query, conn, params=(symbol, count))
            conn.close()
            
            if df.empty:
                return []
            
            # Convert to list of dictionaries
            return df.to_dict('records')
            
        except Exception as e:
            self.logger.error(f"❌ Error getting data for {symbol}: {e}")
            return []
    
    def _get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for a symbol"""
        try:
            latest_data = self._get_latest_data_points(symbol, count=1)
            if latest_data:
                return latest_data[0]['close_price']
            return None
        except Exception as e:
            self.logger.error(f"❌ Error getting current price for {symbol}: {e}")
            return None
    
    def _create_pending_position(self, symbol: str, price: float):
        """Create pending position entry for BUY signal"""
        try:
            # Calculate position details
            quantity = int(self.investment_per_symbol / price)
            actual_investment = quantity * price
            target_price = price + (self.profit_target / quantity)

            # Store in database as PENDING (will be activated by DB2)
            conn = sqlite3.connect(self.trading_db_path)
            cursor = conn.cursor()

            cursor.execute('''
            INSERT INTO trading_positions
            (symbol, buy_price, shares_quantity, investment, target_value, target_price, buy_time, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (symbol, price, quantity, actual_investment,
                  actual_investment + self.profit_target, target_price,
                  datetime.now().isoformat(), 'PENDING'))

            conn.commit()
            conn.close()

            self.logger.info(f"💾 Created pending position: {symbol} @ ₹{price:.2f} (Qty: {quantity})")

        except Exception as e:
            self.logger.error(f"❌ Error creating pending position for {symbol}: {e}")

    def _detect_4fall_1rise_pattern_with_rolling_window(self, symbol: str, data_points: List[Dict]) -> bool:
        """
        Detect 4F+1R pattern using rolling window data
        Uses symbol_patterns tracking for better accuracy
        """
        try:
            if len(data_points) < 3:
                return False

            # Get rolling window pattern data
            pattern_data = self.symbol_patterns.get(symbol, {})
            consecutive_falls = pattern_data.get('consecutive_falls', 0)
            last_trend = pattern_data.get('last_trend', None)

            # Get current and previous prices
            current_price = data_points[0]['close_price']
            previous_price = data_points[1]['close_price'] if len(data_points) > 1 else current_price

            # Determine current trend
            if current_price > previous_price:
                current_trend = 'R'
            elif current_price < previous_price:
                current_trend = 'F'
            else:
                current_trend = 'FLAT'

            # Check for 4F+1R pattern
            if consecutive_falls >= 4 and current_trend == 'R':
                # Check 0.5% drop condition using available data
                prices = [point['close_price'] for point in reversed(data_points)]
                start_price = prices[0]
                lowest_price = min(prices[:-1])
                percentage_drop = ((start_price - lowest_price) / start_price) * 100

                if percentage_drop >= self.min_drop_percentage:
                    self.logger.info(f"✅ {symbol}: 4F+1R PATTERN CONFIRMED using rolling window!")
                    self.logger.info(f"📊 {symbol}: {consecutive_falls} falls + 1 rise, {percentage_drop:.2f}% drop")
                    return True
                else:
                    self.logger.info(f"❌ {symbol}: 4F+1R pattern but insufficient drop: {percentage_drop:.2f}%")

            return False

        except Exception as e:
            self.logger.error(f"❌ Error in rolling window pattern detection for {symbol}: {e}")
            return False

    def _get_latest_data_for_symbol(self, symbol: str, limit: int = 5) -> List[Dict]:
        """Get latest data for symbol (alias for compatibility)"""
        return self._get_latest_data_points(symbol, count=limit)

    def get_status(self) -> Dict[str, any]:
        """Get current status of DB1 signal generator"""
        return {
            'active_positions': len(self.active_positions),
            'priority_categories': {k: len(v) for k, v in self.priority_categories.items()},
            'symbol_patterns_tracked': len(self.symbol_patterns),
            'communicator_status': self.communicator.get_queue_status(),
            'performance_stats': self.communicator.get_performance_stats(),
            'last_update': datetime.now().isoformat()
        }


# Global instance for easy access
_db1_signal_generator = None

def get_db1_signal_generator():
    """Get global DB1 signal generator instance"""
    global _db1_signal_generator
    if _db1_signal_generator is None:
        _db1_signal_generator = DB1_SignalGenerator()
    return _db1_signal_generator
