#!/usr/bin/env python3
"""
DB1 Signal Generator - Pattern Detection and Signal Generation Only
Responsible for 15-minute data analysis and signal generation (NO EXECUTION)
"""

import sqlite3
import pandas as pd
import logging
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from db1_db2_communicator import TradingSignal, ActivePosition, get_communicator

class DB1_SignalGenerator:
    """
    DB1 Signal Generator - Core Pattern Detection Engine
    
    Responsibilities:
    1. Analyze 15-minute data for 4F+1R patterns
    2. Generate BUY signals when pattern detected
    3. Monitor active positions for ₹800 profit target
    4. Generate SELL signals when profit target reached
    5. Send all signals to DB2 for confirmation and execution
    
    DOES NOT EXECUTE TRADES - Only generates signals
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db_path = 'Data/trading_data.db'
        self.communicator = get_communicator()
        
        # Active positions received from DB2
        self.active_positions = {}
        
        # Pattern detection parameters
        self.min_drop_percentage = 0.5  # Minimum 0.5% drop required
        self.profit_target = 800  # ₹800 profit target
        
        self.logger.info("✅ DB1 Signal Generator initialized - Pattern detection only")
    
    def run_signal_generation_loop(self):
        """
        Main DB1 loop - runs every 15 minutes
        1. Analyze patterns and generate BUY signals
        2. Monitor active positions and generate SELL signals
        3. Update active positions from DB2
        """
        self.logger.info("🚀 Starting DB1 Signal Generation Loop (15-minute intervals)")
        
        while True:
            try:
                start_time = time.time()
                
                self.logger.info("=" * 60)
                self.logger.info(f"🔍 DB1 ANALYSIS CYCLE - {datetime.now().strftime('%H:%M:%S')}")
                self.logger.info("=" * 60)
                
                # Step 1: Update active positions from DB2
                self._update_active_positions_from_db2()
                
                # Step 2: Analyze patterns and generate BUY signals
                buy_signals_generated = self._analyze_patterns_and_generate_buy_signals()
                
                # Step 3: Monitor active positions and generate SELL signals
                sell_signals_generated = self._monitor_positions_and_generate_sell_signals()
                
                # Step 4: Log cycle summary
                cycle_time = time.time() - start_time
                self.logger.info(f"📊 DB1 CYCLE COMPLETE: {buy_signals_generated} BUY signals, "
                               f"{sell_signals_generated} SELL signals generated in {cycle_time:.2f}s")
                
                # Wait for next 15-minute interval
                self.logger.info("⏰ Waiting for next 15-minute interval...")
                time.sleep(900)  # 15 minutes = 900 seconds
                
            except Exception as e:
                self.logger.error(f"❌ Error in DB1 signal generation loop: {e}")
                time.sleep(60)  # Wait 1 minute before retrying
    
    def _update_active_positions_from_db2(self):
        """Update active positions received from DB2"""
        try:
            updated_count = 0
            
            # Check for position updates from DB2
            while True:
                position = self.communicator.receive_position_from_db2(timeout_ms=10)
                if not position:
                    break
                
                self.active_positions[position.symbol] = position
                updated_count += 1
                
                self.logger.info(f"📥 POSITION UPDATE: {position.symbol} - "
                               f"Profit: ₹{position.current_profit:.0f}")
            
            if updated_count > 0:
                self.logger.info(f"📊 Updated {updated_count} active positions from DB2")
            
            self.logger.info(f"📈 ACTIVE POSITIONS: {len(self.active_positions)} symbols being monitored")
            
        except Exception as e:
            self.logger.error(f"❌ Error updating active positions from DB2: {e}")
    
    def _analyze_patterns_and_generate_buy_signals(self) -> int:
        """
        Analyze 15-minute data for 4F+1R patterns and generate BUY signals
        Returns: Number of BUY signals generated
        """
        try:
            self.logger.info("🔍 ANALYZING PATTERNS FOR BUY SIGNALS...")
            
            # Get all symbols for analysis
            symbols = self._get_all_symbols()
            signals_generated = 0
            
            for symbol in symbols:
                try:
                    # Skip symbols with active positions (can't buy again)
                    if symbol in self.active_positions:
                        continue
                    
                    # Get latest 5 data points for 4F+1R pattern
                    latest_data = self._get_latest_data_points(symbol, count=5)
                    
                    if len(latest_data) >= 5:
                        # Check for 4F+1R pattern with 0.5% drop
                        if self._detect_4fall_1rise_pattern(latest_data):
                            current_price = latest_data[0]['close_price']
                            
                            # Generate BUY signal
                            signal = self._create_buy_signal(symbol, current_price, latest_data)
                            
                            if self.communicator.send_signal_to_db2(signal):
                                signals_generated += 1
                                self.logger.info(f"🎯 BUY SIGNAL GENERATED: {symbol} @ ₹{current_price:.2f}")
                            else:
                                self.logger.error(f"❌ Failed to send BUY signal for {symbol}")
                
                except Exception as e:
                    self.logger.error(f"❌ Error analyzing {symbol}: {e}")
                    continue
            
            self.logger.info(f"📊 BUY SIGNAL ANALYSIS COMPLETE: {signals_generated} signals generated")
            return signals_generated
            
        except Exception as e:
            self.logger.error(f"❌ Error in pattern analysis: {e}")
            return 0
    
    def _monitor_positions_and_generate_sell_signals(self) -> int:
        """
        Monitor active positions for ₹800 profit target and generate SELL signals
        Returns: Number of SELL signals generated
        """
        try:
            if not self.active_positions:
                self.logger.info("📊 No active positions to monitor for SELL signals")
                return 0
            
            self.logger.info(f"💰 MONITORING {len(self.active_positions)} POSITIONS FOR PROFIT TARGET...")
            
            signals_generated = 0
            
            for symbol, position in self.active_positions.items():
                try:
                    # Get current price
                    current_price = self._get_current_price(symbol)
                    
                    if current_price:
                        # Calculate current profit
                        current_profit = (current_price * position.shares_quantity) - position.investment
                        
                        self.logger.info(f"📊 {symbol}: Current ₹{current_price:.2f}, "
                                       f"Profit ₹{current_profit:.0f} (Target: ₹{self.profit_target})")
                        
                        # Check if profit target reached
                        if current_profit >= self.profit_target:
                            # Generate SELL signal
                            signal = self._create_sell_signal(symbol, current_price, position, current_profit)
                            
                            if self.communicator.send_signal_to_db2(signal):
                                signals_generated += 1
                                self.logger.info(f"🎯 SELL SIGNAL GENERATED: {symbol} @ ₹{current_price:.2f} "
                                               f"(Profit: ₹{current_profit:.0f})")
                            else:
                                self.logger.error(f"❌ Failed to send SELL signal for {symbol}")
                
                except Exception as e:
                    self.logger.error(f"❌ Error monitoring {symbol}: {e}")
                    continue
            
            self.logger.info(f"📊 SELL SIGNAL MONITORING COMPLETE: {signals_generated} signals generated")
            return signals_generated
            
        except Exception as e:
            self.logger.error(f"❌ Error in position monitoring: {e}")
            return 0
    
    def _detect_4fall_1rise_pattern(self, data_points: List[Dict]) -> bool:
        """
        Detect 4-FALL + 1-RISE pattern with 0.5% drop condition
        Args: data_points - List of 5 data points (most recent first)
        Returns: True if pattern detected, False otherwise
        """
        try:
            if len(data_points) < 5:
                return False
            
            # Extract prices in chronological order (reverse the list)
            prices = [point['close_price'] for point in reversed(data_points)]
            
            # Calculate trends from the 5 data points (4 trends)
            trends = []
            for i in range(1, len(prices)):
                if prices[i] > prices[i-1]:
                    trends.append("RISE")
                elif prices[i] < prices[i-1]:
                    trends.append("FALL")
                else:
                    trends.append("FLAT")
            
            # Check for 4-FALL + 1-RISE pattern (exactly 4 trends)
            if len(trends) == 4:
                expected_pattern = ["FALL", "FALL", "FALL", "FALL"]
                
                # Check if first 4 trends are FALL
                if trends == expected_pattern:
                    # Check if the 5th point (latest) is a RISE from the 4th point
                    if prices[4] > prices[3]:  # Latest price > previous price
                        
                        # Check 0.5% drop condition
                        start_price = prices[0]  # First price
                        lowest_price = min(prices[:-1])  # Lowest price before the final rise
                        
                        # Calculate percentage drop
                        percentage_drop = ((start_price - lowest_price) / start_price) * 100
                        
                        if percentage_drop >= self.min_drop_percentage:
                            self.logger.info(f"✅ 4F+1R Pattern + {percentage_drop:.2f}% Drop DETECTED!")
                            return True
                        else:
                            self.logger.debug(f"❌ 4F+1R Pattern found but insufficient drop: "
                                            f"{percentage_drop:.2f}% (need ≥{self.min_drop_percentage}%)")
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Error in pattern detection: {e}")
            return False
    
    def _create_buy_signal(self, symbol: str, price: float, data_points: List[Dict]) -> TradingSignal:
        """Create BUY signal with pattern information"""
        pattern_info = {
            'pattern_type': '4F+1R',
            'drop_percentage': self._calculate_drop_percentage(data_points),
            'data_points': len(data_points),
            'detection_time': datetime.now().isoformat()
        }
        
        return TradingSignal(
            symbol=symbol,
            signal_type='BUY',
            price=price,
            timestamp_ns=time.time_ns(),
            pattern_info=pattern_info,
            source='DB1'
        )
    
    def _create_sell_signal(self, symbol: str, price: float, position: ActivePosition, profit: float) -> TradingSignal:
        """Create SELL signal with position information"""
        pattern_info = {
            'pattern_type': 'PROFIT_TARGET',
            'current_profit': profit,
            'target_profit': self.profit_target,
            'buy_price': position.buy_price,
            'profit_percentage': (profit / position.investment) * 100,
            'detection_time': datetime.now().isoformat()
        }
        
        return TradingSignal(
            symbol=symbol,
            signal_type='SELL',
            price=price,
            timestamp_ns=time.time_ns(),
            pattern_info=pattern_info,
            source='DB1'
        )
    
    def _calculate_drop_percentage(self, data_points: List[Dict]) -> float:
        """Calculate percentage drop from start to lowest point"""
        prices = [point['close_price'] for point in reversed(data_points)]
        start_price = prices[0]
        lowest_price = min(prices[:-1])
        return ((start_price - lowest_price) / start_price) * 100
    
    def _get_all_symbols(self) -> List[str]:
        """Get all symbols for analysis"""
        try:
            from symbol_manager import SymbolManager
            symbol_manager = SymbolManager()
            return symbol_manager.get_all_symbols()
        except Exception as e:
            self.logger.error(f"❌ Error getting symbols: {e}")
            return []
    
    def _get_latest_data_points(self, symbol: str, count: int = 5) -> List[Dict]:
        """Get latest N data points for a symbol"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            query = '''
            SELECT close_price, timestamp, open_price, high_price, low_price, volume
            FROM trading_data 
            WHERE symbol = ? 
            ORDER BY timestamp DESC 
            LIMIT ?
            '''
            
            df = pd.read_sql_query(query, conn, params=(symbol, count))
            conn.close()
            
            if df.empty:
                return []
            
            # Convert to list of dictionaries
            return df.to_dict('records')
            
        except Exception as e:
            self.logger.error(f"❌ Error getting data for {symbol}: {e}")
            return []
    
    def _get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for a symbol"""
        try:
            latest_data = self._get_latest_data_points(symbol, count=1)
            if latest_data:
                return latest_data[0]['close_price']
            return None
        except Exception as e:
            self.logger.error(f"❌ Error getting current price for {symbol}: {e}")
            return None
    
    def get_status(self) -> Dict[str, any]:
        """Get current status of DB1 signal generator"""
        return {
            'active_positions': len(self.active_positions),
            'communicator_status': self.communicator.get_queue_status(),
            'performance_stats': self.communicator.get_performance_stats(),
            'last_update': datetime.now().isoformat()
        }
