#!/usr/bin/env python3
"""
DB2 Trade Executor - Signal Confirmation and Trade Execution
Responsible for receiving signals from DB1, confirming with rolling windows, and executing trades
"""

import sqlite3
import logging
import time
import threading
from datetime import datetime
from typing import Dict, List, Optional
from db1_db2_communicator import TradingSignal, ActivePosition, get_communicator
from rolling_window_monitor import get_rolling_window_manager

class DB2_TradeExecutor:
    """
    DB2 Trade Executor - Signal Confirmation and Execution Engine
    
    Responsibilities:
    1. Receive signals from DB1 via millisecond communication
    2. Start rolling window confirmations (R+R for BUY, F+F for SELL)
    3. Execute trades immediately upon confirmation
    4. Manage active positions and send updates to DB1
    5. Handle paper trading with complete audit trail
    
    ONLY EXECUTES TRADES - Does not generate signals
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db_path = 'Data/trading_operations.db'
        self.communicator = get_communicator()
        self.rolling_window_manager = get_rolling_window_manager()
        
        # Active positions managed by DB2
        self.active_positions = {}
        
        # Trading parameters
        self.investment_per_symbol = 10000  # ₹10,000 per symbol
        self.profit_target = 800  # ₹800 profit target
        
        # Initialize database
        self._init_db2_tables()
        
        self.logger.info("✅ DB2 Trade Executor initialized - Signal confirmation and execution only")
    
    def run_trade_execution_loop(self):
        """
        Main DB2 loop - runs continuously
        1. Receive signals from DB1
        2. Start rolling window confirmations
        3. Execute trades upon confirmation
        4. Send position updates to DB1
        """
        self.logger.info("🚀 Starting DB2 Trade Execution Loop (Continuous)")
        
        while True:
            try:
                # Step 1: Check for signals from DB1 (high frequency)
                signal = self.communicator.receive_signal_from_db1(timeout_ms=100)
                
                if signal:
                    self.logger.info(f"📥 SIGNAL RECEIVED FROM DB1: {signal.signal_type} {signal.symbol} @ ₹{signal.price:.2f}")
                    self._process_signal_from_db1(signal)
                
                # Step 2: Update active positions and send to DB1
                self._update_and_send_positions_to_db1()
                
                # Step 3: Cleanup completed rolling windows
                self.rolling_window_manager.cleanup_completed_monitors()
                
                # Small delay to prevent excessive CPU usage
                time.sleep(0.1)  # 100ms
                
            except Exception as e:
                self.logger.error(f"❌ Error in DB2 execution loop: {e}")
                time.sleep(1)  # Wait 1 second before retrying
    
    def _process_signal_from_db1(self, signal: TradingSignal):
        """Process signal received from DB1 and start rolling window confirmation"""
        try:
            if signal.signal_type == 'BUY':
                self._process_buy_signal(signal)
            elif signal.signal_type == 'SELL':
                self._process_sell_signal(signal)
            else:
                self.logger.error(f"❌ Unknown signal type: {signal.signal_type}")
        
        except Exception as e:
            self.logger.error(f"❌ Error processing signal from DB1: {e}")
    
    def _process_buy_signal(self, signal: TradingSignal):
        """Process BUY signal and start R+R rolling window confirmation"""
        try:
            # Check if symbol already has active position
            if signal.symbol in self.active_positions:
                self.logger.warning(f"⚠️ BUY signal ignored - {signal.symbol} already has active position")
                return
            
            self.logger.info(f"🔄 Starting R+R rolling window confirmation for {signal.symbol}")
            
            # Start rolling window monitor for R+R confirmation
            success = self.rolling_window_manager.start_monitor(
                symbol=signal.symbol,
                signal=signal,
                on_confirmation_callback=self._on_buy_confirmation
            )
            
            if success:
                self.logger.info(f"✅ R+R rolling window started for {signal.symbol}")
                
                # Send confirmation status to DB1
                self.communicator.send_confirmation_to_db1(
                    signal.symbol, 
                    'BUY_CONFIRMATION_STARTED',
                    {'base_price': signal.price, 'pattern_required': 'R+R'}
                )
            else:
                self.logger.error(f"❌ Failed to start R+R rolling window for {signal.symbol}")
        
        except Exception as e:
            self.logger.error(f"❌ Error processing BUY signal: {e}")
    
    def _process_sell_signal(self, signal: TradingSignal):
        """Process SELL signal and start F+F rolling window confirmation"""
        try:
            # Check if symbol has active position
            if signal.symbol not in self.active_positions:
                self.logger.warning(f"⚠️ SELL signal ignored - {signal.symbol} has no active position")
                return
            
            self.logger.info(f"🔄 Starting F+F rolling window confirmation for {signal.symbol}")
            
            # Start rolling window monitor for F+F confirmation
            success = self.rolling_window_manager.start_monitor(
                symbol=signal.symbol,
                signal=signal,
                on_confirmation_callback=self._on_sell_confirmation
            )
            
            if success:
                self.logger.info(f"✅ F+F rolling window started for {signal.symbol}")
                
                # Send confirmation status to DB1
                self.communicator.send_confirmation_to_db1(
                    signal.symbol,
                    'SELL_CONFIRMATION_STARTED', 
                    {'base_price': signal.price, 'pattern_required': 'F+F'}
                )
            else:
                self.logger.error(f"❌ Failed to start F+F rolling window for {signal.symbol}")
        
        except Exception as e:
            self.logger.error(f"❌ Error processing SELL signal: {e}")
    
    def _on_buy_confirmation(self, symbol: str, signal: TradingSignal, confirmation_price: float, 
                           confirmation_time: datetime, data_points: List):
        """Callback when R+R pattern confirmed - Execute BUY trade immediately"""
        try:
            self.logger.info(f"🎯 R+R CONFIRMED: Executing BUY trade for {symbol} @ ₹{confirmation_price:.2f}")
            
            # Calculate trade parameters
            shares_quantity = int(self.investment_per_symbol / confirmation_price)
            actual_investment = shares_quantity * confirmation_price
            target_price = confirmation_price + (self.profit_target / shares_quantity)
            
            # Create active position
            position = ActivePosition(
                symbol=symbol,
                buy_price=confirmation_price,
                shares_quantity=shares_quantity,
                investment=actual_investment,
                target_price=target_price,
                buy_time=confirmation_time,
                current_profit=0.0,
                status='ACTIVE',
                timestamp_ns=time.time_ns()
            )
            
            # Store position in DB2
            if self._store_position_in_db2(position):
                # Add to active positions
                self.active_positions[symbol] = position
                
                # Log paper trade
                self._log_paper_trade(symbol, confirmation_price, 'BUY', shares_quantity, actual_investment)
                
                # Send position to DB1 for monitoring
                self.communicator.send_position_to_db1(position)
                
                # Send confirmation to DB1
                self.communicator.send_confirmation_to_db1(
                    symbol,
                    'BUY_EXECUTED',
                    {
                        'execution_price': confirmation_price,
                        'shares_quantity': shares_quantity,
                        'investment': actual_investment,
                        'target_price': target_price
                    }
                )
                
                self.logger.info(f"✅ BUY EXECUTED: {symbol} - {shares_quantity} shares @ ₹{confirmation_price:.2f}")
                self.logger.info(f"💰 Investment: ₹{actual_investment:.0f}, Target: ₹{target_price:.2f}")
            
            else:
                self.logger.error(f"❌ Failed to store position for {symbol}")
        
        except Exception as e:
            self.logger.error(f"❌ Error executing BUY trade: {e}")
    
    def _on_sell_confirmation(self, symbol: str, signal: TradingSignal, confirmation_price: float,
                            confirmation_time: datetime, data_points: List):
        """Callback when F+F pattern confirmed - Execute SELL trade immediately"""
        try:
            self.logger.info(f"🎯 F+F CONFIRMED: Executing SELL trade for {symbol} @ ₹{confirmation_price:.2f}")
            
            # Get active position
            position = self.active_positions.get(symbol)
            if not position:
                self.logger.error(f"❌ No active position found for {symbol}")
                return
            
            # Calculate profit
            total_value = position.shares_quantity * confirmation_price
            actual_profit = total_value - position.investment
            
            # Update position in DB2
            if self._close_position_in_db2(symbol, confirmation_price, actual_profit, confirmation_time):
                # Log paper trade
                self._log_paper_trade(symbol, confirmation_price, 'SELL', position.shares_quantity, total_value)
                
                # Remove from active positions
                del self.active_positions[symbol]
                
                # Send confirmation to DB1
                self.communicator.send_confirmation_to_db1(
                    symbol,
                    'SELL_EXECUTED',
                    {
                        'execution_price': confirmation_price,
                        'shares_quantity': position.shares_quantity,
                        'total_value': total_value,
                        'actual_profit': actual_profit,
                        'profit_percentage': (actual_profit / position.investment) * 100
                    }
                )
                
                self.logger.info(f"✅ SELL EXECUTED: {symbol} - {position.shares_quantity} shares @ ₹{confirmation_price:.2f}")
                self.logger.info(f"💰 Profit: ₹{actual_profit:.0f} ({(actual_profit/position.investment)*100:.1f}%)")
            
            else:
                self.logger.error(f"❌ Failed to close position for {symbol}")
        
        except Exception as e:
            self.logger.error(f"❌ Error executing SELL trade: {e}")
    
    def _update_and_send_positions_to_db1(self):
        """Update active positions with current prices and send to DB1"""
        try:
            if not self.active_positions:
                return
            
            for symbol, position in self.active_positions.items():
                try:
                    # Get current price (simulate for now)
                    current_price = self._get_current_price(symbol)
                    
                    if current_price:
                        # Update current profit
                        current_value = position.shares_quantity * current_price
                        position.current_profit = current_value - position.investment
                        position.timestamp_ns = time.time_ns()
                        
                        # Send updated position to DB1
                        self.communicator.send_position_to_db1(position)
                
                except Exception as e:
                    self.logger.error(f"❌ Error updating position for {symbol}: {e}")
        
        except Exception as e:
            self.logger.error(f"❌ Error updating positions: {e}")
    
    def _get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for a symbol (simulate for now)"""
        try:
            # TODO: Replace with actual real-time price API
            # For now, get latest price from trading_data
            conn = sqlite3.connect('Data/trading_data.db')
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT close_price FROM trading_data 
            WHERE symbol = ? 
            ORDER BY timestamp DESC 
            LIMIT 1
            ''', (symbol,))
            
            result = cursor.fetchone()
            conn.close()
            
            return result[0] if result else None
        
        except Exception as e:
            self.logger.error(f"❌ Error getting current price for {symbol}: {e}")
            return None
    
    def _store_position_in_db2(self, position: ActivePosition) -> bool:
        """Store active position in DB2 database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
            INSERT INTO trading_positions 
            (symbol, buy_price, shares_quantity, investment, target_price, buy_time, status)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                position.symbol,
                position.buy_price,
                position.shares_quantity,
                position.investment,
                position.target_price,
                position.buy_time,
                position.status
            ))
            
            conn.commit()
            conn.close()
            return True
        
        except Exception as e:
            self.logger.error(f"❌ Error storing position in DB2: {e}")
            return False
    
    def _close_position_in_db2(self, symbol: str, sell_price: float, profit: float, sell_time: datetime) -> bool:
        """Close position in DB2 database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
            UPDATE trading_positions 
            SET sell_price = ?, actual_profit = ?, sell_time = ?, status = 'CLOSED'
            WHERE symbol = ? AND status = 'ACTIVE'
            ''', (sell_price, profit, sell_time, symbol))
            
            conn.commit()
            conn.close()
            return True
        
        except Exception as e:
            self.logger.error(f"❌ Error closing position in DB2: {e}")
            return False
    
    def _log_paper_trade(self, symbol: str, price: float, action: str, quantity: int, amount: float):
        """Log paper trade to CSV file"""
        try:
            import csv
            import os
            
            csv_file = 'Data/csv_files/paper_trade.csv'
            
            # Ensure directory exists
            os.makedirs(os.path.dirname(csv_file), exist_ok=True)
            
            # Check if file exists to write header
            file_exists = os.path.exists(csv_file)
            
            with open(csv_file, 'a', newline='') as file:
                writer = csv.writer(file)
                
                if not file_exists:
                    writer.writerow(['Timestamp', 'Symbol', 'Action', 'Price', 'Quantity', 'Amount', 'API_Call'])
                
                api_call = f"smart_api.placeOrder(symbol='{symbol}', transactiontype='{action}', quantity={quantity}, price={price}, ordertype='MARKET', producttype='INTRADAY')"
                
                writer.writerow([
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    symbol,
                    action,
                    f"{price:.2f}",
                    quantity,
                    f"{amount:.2f}",
                    api_call
                ])
            
            self.logger.info(f"📝 Paper trade logged: {action} {symbol} @ ₹{price:.2f}")
        
        except Exception as e:
            self.logger.error(f"❌ Error logging paper trade: {e}")
    
    def _init_db2_tables(self):
        """Initialize DB2 database tables"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Trading positions table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS trading_positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                buy_price REAL NOT NULL,
                sell_price REAL,
                shares_quantity INTEGER NOT NULL,
                investment REAL NOT NULL,
                target_price REAL NOT NULL,
                buy_time DATETIME NOT NULL,
                sell_time DATETIME,
                actual_profit REAL,
                status TEXT DEFAULT 'ACTIVE',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            conn.commit()
            conn.close()
            
            self.logger.info("✅ DB2 tables initialized")
        
        except Exception as e:
            self.logger.error(f"❌ Error initializing DB2 tables: {e}")
    
    def get_status(self) -> Dict[str, any]:
        """Get current status of DB2 trade executor"""
        return {
            'active_positions': len(self.active_positions),
            'active_monitors': len(self.rolling_window_manager.get_active_monitors()),
            'communicator_status': self.communicator.get_queue_status(),
            'performance_stats': self.communicator.get_performance_stats(),
            'last_update': datetime.now().isoformat()
        }
