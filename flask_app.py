"""
Flask Trading Application
Modern web-based trading system with real-time capabilities
Migrated from Tkinter GUI to Flask web framework
"""
import os
import logging
import csv
from datetime import datetime
from flask import Flask, render_template, request, jsonify, session, send_file
from flask_sqlalchemy import SQLAlchemy
from flask_socketio import SocketIO, emit, join_room, leave_room
from flask_cors import CORS
import threading
import time
import json

# Import existing trading components
from trading_engine import TradingEngine
from realtime_data_fetcher import RealtimeDataFetcher
from data_integrity_checker import DataIntegrityChecker
from duplicate_remover import DuplicateRemover
from symbol_manager import SymbolManager
from config import *

# Database paths
MARKET_DB_PATH = "Data/trading_data.db"      # DB1: 15-minute market data
TRADING_DB_PATH = "Data/trading_operations.db"  # DB2: Layer 2 + paper trading

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-change-in-production'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///Data/trading_data.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)
socketio = SocketIO(app, cors_allowed_origins="*")
CORS(app)

# Ensure database tables exist before initializing components
def ensure_database_tables():
    """Ensure all required database tables exist"""
    try:
        import os
        import sqlite3
        import logging

        # Setup basic logging for this function
        logging.basicConfig(level=logging.INFO)
        temp_logger = logging.getLogger(__name__)

        # Create Data directory if it doesn't exist
        os.makedirs('Data', exist_ok=True)

        # Create trading_data.db and required tables
        conn = sqlite3.connect('Data/trading_data.db')
        cursor = conn.cursor()

        # Create trading_data table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS trading_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            token TEXT NOT NULL,
            exchange TEXT DEFAULT 'NSE',
            timestamp TEXT NOT NULL,
            open_price REAL NOT NULL,
            high_price REAL NOT NULL,
            low_price REAL NOT NULL,
            close_price REAL NOT NULL,
            volume INTEGER NOT NULL,
            interval_type TEXT DEFAULT 'FIFTEEN_MINUTE',
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, timestamp)
        )
        ''')

        conn.commit()
        conn.close()

        temp_logger.info("✅ Database tables ensured successfully")

    except Exception as e:
        import logging
        temp_logger = logging.getLogger(__name__)
        temp_logger.error(f"❌ Error ensuring database tables: {e}")

# Ensure database tables exist first
ensure_database_tables()

# Initialize trading components
trading_engine = TradingEngine()
realtime_fetcher = RealtimeDataFetcher()
data_integrity_checker = DataIntegrityChecker()
duplicate_remover = DuplicateRemover()
symbol_manager = SymbolManager()

# Configure logging first
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/flask_app.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialize dual-database system
dual_system_started = trading_engine.start_dual_database_system()
if dual_system_started:
    logger.info("🚀 DUAL-DATABASE TRADING SYSTEM STARTED")
    logger.info("📊 DB1: Pattern detection and signal generation")
    logger.info("💰 DB2: Rolling window confirmation and trade execution")
    logger.info("⚡ Communication: Millisecond-level signal transmission")
else:
    logger.warning("⚠️ Dual-database system failed to start. Using legacy system.")

# Initialize confirmation engine for manual trading (even when trading is not active)
try:
    trading_engine.initialize_confirmation_engine()
    print("✅ Confirmation engine initialized for manual trading")
except Exception as e:
    print(f"❌ Failed to initialize confirmation engine: {e}")

# Note: Old trading_coordinator removed - using new dual-database system

# Global variables for real-time updates
active_connections = set()
trading_active = False
data_fetching_active = False

@app.route('/')
def dashboard():
    """Main trading dashboard"""
    return render_template('dashboard.html')

@app.route('/data-integrity-check')
def data_integrity_check_page():
    """Data Integrity Check page"""
    return render_template('data_integrity_check.html')

@app.route('/api/symbols')
def get_symbols():
    """Get all available symbols"""
    try:
        symbols = symbol_manager.get_all_symbols()
        return jsonify({
            'success': True,
            'symbols': symbols,
            'count': len(symbols)
        })
    except Exception as e:
        logger.error(f"Error fetching symbols: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/trading-data/<symbol>')
def get_trading_data(symbol):
    """Get trading data for a specific symbol"""
    try:
        date = request.args.get('date', datetime.now().strftime('%Y-%m-%d'))
        
        # Query database for trading data
        import sqlite3
        conn = sqlite3.connect('Data/trading_data.db')
        cursor = conn.cursor()
        
        query = """
        SELECT timestamp, open_price, high_price, low_price, close_price, volume
        FROM trading_data 
        WHERE symbol = ? AND DATE(timestamp) = ?
        ORDER BY timestamp
        """
        
        cursor.execute(query, (symbol, date))
        rows = cursor.fetchall()
        conn.close()
        
        data = []
        for row in rows:
            data.append({
                'timestamp': row[0],
                'open': row[1],
                'high': row[2],
                'low': row[3],
                'close': row[4],
                'volume': row[5]
            })
        
        return jsonify({
            'success': True,
            'symbol': symbol,
            'date': date,
            'data': data,
            'count': len(data)
        })
        
    except Exception as e:
        logger.error(f"Error fetching trading data for {symbol}: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/portfolio-status')
def get_portfolio_status():
    """Get current portfolio status with correct pot calculations from database"""
    try:
        import sqlite3

        # Get active positions from database (DB2)
        conn = sqlite3.connect(TRADING_DB_PATH)
        cursor = conn.cursor()

        # Get active positions
        cursor.execute('''
        SELECT symbol, buy_price, shares_quantity, investment, target_price, buy_time
        FROM trading_positions
        WHERE status = 'ACTIVE'
        ''')
        active_positions = cursor.fetchall()

        # Get vault amount from database
        cursor.execute('SELECT vault_amount FROM portfolio_status ORDER BY id DESC LIMIT 1')
        vault_result = cursor.fetchone()
        vault_amount = vault_result[0] if vault_result else 0.0

        # Get completed trades count
        cursor.execute('SELECT COUNT(*) FROM trading_positions WHERE status = "SOLD"')
        completed_trades_count = cursor.fetchone()[0]

        conn.close()

        # Calculate used funds from active positions
        used_funds = sum(pos[3] for pos in active_positions)  # pos[3] is investment
        total_pot = 22200000.0  # ₹2.22 Cr (222 symbols × ₹1L each)
        available_pot = total_pot - used_funds

        # Get portfolio data
        status = {
            'total_pot': total_pot,
            'available_pot': available_pot,  # This should decrease when BUY happens
            'used_funds': used_funds,
            'vault_amount': vault_amount,
            'active_positions': len(active_positions),
            'completed_trades': completed_trades_count,
            'total_profit': vault_amount,  # Vault amount IS the total profit
            'last_updated': datetime.now().isoformat()
        }

        # Debug logging
        print(f"🔍 API Response Debug (from Database):")
        print(f"   Total Pot: ₹{status['total_pot']:,.2f}")
        print(f"   Available Pot: ₹{status['available_pot']:,.2f}")
        print(f"   Used Funds: ₹{status['used_funds']:,.2f}")
        print(f"   Vault Amount: ₹{status['vault_amount']:,.2f}")
        print(f"   Active Positions: {status['active_positions']}")
        print(f"   Total Profit: ₹{status['total_profit']:,.2f}")

        # Additional debugging for active positions
        print(f"🔍 Active Positions Debug (from Database):")
        for pos in active_positions:
            symbol, buy_price, shares_qty, investment, target_price, buy_time = pos
            print(f"   {symbol}: ₹{buy_price:.2f} x {shares_qty} = ₹{investment:.2f}")
        print(f"🔍 Active Positions Count: {len(active_positions)}")

        return jsonify({
            'success': True,
            'portfolio': status
        })

    except Exception as e:
        logger.error(f"Error fetching portfolio status: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/active-positions')
def get_active_positions():
    """Get all active trading positions from DB1 (fast response)"""
    try:
        import sqlite3

        # Use DB2 for active positions (where trades are executed)
        conn = sqlite3.connect('Data/trading_operations.db')
        cursor = conn.cursor()

        # Get active positions from DB2 with optimized query
        cursor.execute('''
        SELECT symbol, buy_price, shares_quantity, investment, target_price,
               buy_time, status,
               (target_price * shares_quantity) as target_value
        FROM trading_positions
        WHERE status = 'ACTIVE'
        ORDER BY buy_time DESC
        LIMIT 100
        ''')

        positions = []
        for row in cursor.fetchall():
            symbol, buy_price, shares_qty, investment, target_price, buy_time, status, target_val = row

            positions.append({
                'symbol': symbol,
                'buy_price': f"₹{buy_price:.2f}",
                'shares_quantity': shares_qty,
                'investment': f"₹{investment:.0f}",
                'target_price': f"₹{target_price:.2f}",
                'target_value': f"₹{target_val:.0f}",
                'buy_time': buy_time,
                'status': status,
                'expected_profit': f"₹{target_val - investment:.0f}"
            })

        conn.close()

        return jsonify({
            'success': True,
            'positions': positions,
            'count': len(positions),
            'source': 'DB2_ACTIVE_POSITIONS',
            'timestamp': datetime.now().strftime('%H:%M:%S')
        })

    except Exception as e:
        logger.error(f"Error fetching active positions: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/start-trading', methods=['POST'])
def start_trading():
    """Start the dual-database trading system"""
    global trading_active
    try:
        if not trading_active:
            trading_active = True
            # Make trading_active accessible as module attribute for data_integrity_checker
            globals()['trading_active'] = True

            # Initialize and start confirmation engine (legacy support)
            trading_engine.initialize_confirmation_engine()
            if trading_engine.confirmation_engine:
                trading_engine.confirmation_engine.start_confirmation_engine()
                logger.info("🎯 LEGACY CONFIRMATION ENGINE STATUS: ACTIVE")
            else:
                logger.error("❌ LEGACY CONFIRMATION ENGINE STATUS: FAILED TO INITIALIZE")

            # Start dual-database system (new architecture)
            dual_started = trading_engine.start_dual_database_system()
            if dual_started:
                logger.info("🚀 DUAL-DATABASE SYSTEM STATUS: ACTIVE")
                logger.info("📊 DB1: Signal generation and position monitoring")
                logger.info("💰 DB2: Rolling window confirmation and trade execution")
                logger.info("⚡ Communication: Millisecond-level signal transmission")
            else:
                logger.error("❌ DUAL-DATABASE SYSTEM STATUS: FAILED TO START")

            trading_engine.start_trading()

            # Emit status update to all connected clients
            socketio.emit('trading_status', {
                'active': True,
                'message': 'Dual-database trading system started: DB1 signal generation → DB2 confirmation & execution'
            })

            logger.info("Complete trading system started via web interface")
            return jsonify({'success': True, 'message': 'Dual-database trading system started successfully'})
        else:
            return jsonify({'success': False, 'message': 'Trading already active'})

    except Exception as e:
        logger.error(f"Error starting trading: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/stop-trading', methods=['POST'])
def stop_trading():
    """Stop the dual-database trading system"""
    global trading_active
    try:
        if trading_active:
            trading_active = False
            # Update module attribute as well
            globals()['trading_active'] = False

            # Note: Dual-database system components will stop automatically
            # as they run in daemon threads
            logger.info("🛑 DUAL-DATABASE SYSTEM STOPPING")

            trading_engine.stop_trading()

            # Emit status update to all connected clients
            socketio.emit('trading_status', {
                'active': False,
                'message': 'Dual-database trading system stopped'
            })

            logger.info("Complete trading system stopped via web interface")
            return jsonify({'success': True, 'message': 'Complete trading system stopped'})
        else:
            return jsonify({'success': False, 'message': 'Trading not active'})

    except Exception as e:
        logger.error(f"Error stopping trading: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/backfill-missing-data', methods=['POST'])
def backfill_missing_data():
    """Backfill missing data for today to ensure 8+ consecutive data points"""
    global realtime_fetcher

    try:
        if realtime_fetcher is None:
            realtime_fetcher = RealtimeDataFetcher()

        # Connect to API
        if not realtime_fetcher.connect_to_api():
            return jsonify({
                'success': False,
                'message': 'Failed to connect to Angel One API'
            })

        # Get current time and calculate missing intervals
        from datetime import datetime, timedelta
        current_time = datetime.now()

        # Calculate missing intervals for today
        missing_intervals = []
        market_start = current_time.replace(hour=9, minute=15, second=0, microsecond=0)

        # Generate 15-minute intervals from 9:15 AM until current time
        interval_time = market_start
        while interval_time < current_time:
            missing_intervals.append(interval_time)
            interval_time += timedelta(minutes=15)

        logger.info(f"Backfilling {len(missing_intervals)} missing intervals for today")

        # Backfill data for each missing interval
        total_fetched = 0
        for interval_time in missing_intervals:
            result = realtime_fetcher.fetch_all_symbols_realtime(interval_time)
            total_fetched += result.get('success', 0)
            logger.info(f"Backfilled interval {interval_time.strftime('%H:%M')}: {result.get('success', 0)} symbols")

        return jsonify({
            'success': True,
            'message': f'Backfilled {len(missing_intervals)} intervals, fetched {total_fetched} data points',
            'intervals_backfilled': len(missing_intervals),
            'total_data_points': total_fetched
        })

    except Exception as e:
        logger.error(f"Error backfilling missing data: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/start-data-fetch', methods=['POST'])
def start_data_fetch():
    """Start real-time data fetching"""
    global data_fetching_active
    try:
        if not data_fetching_active:
            data_fetching_active = True

            # Start data fetching in background thread
            def fetch_data_background():
                realtime_fetcher.run_realtime_loop()

            fetch_thread = threading.Thread(target=fetch_data_background, daemon=True)
            fetch_thread.start()

            # Emit status update
            socketio.emit('data_fetch_status', {
                'active': True,
                'message': 'Real-time data fetching started'
            })

            logger.info("Data fetching started via web interface")
            return jsonify({'success': True, 'message': 'Data fetching started'})
        else:
            return jsonify({'success': False, 'message': 'Data fetching already active'})

    except Exception as e:
        logger.error(f"Error starting data fetch: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/stop-data-fetch', methods=['POST'])
def stop_data_fetch():
    """Stop real-time data fetching"""
    global data_fetching_active
    try:
        if data_fetching_active:
            data_fetching_active = False

            # Emit status update
            socketio.emit('data_fetch_status', {
                'active': False,
                'message': 'Real-time data fetching stopped'
            })

            logger.info("Data fetching stopped via web interface")
            return jsonify({'success': True, 'message': 'Data fetching stopped'})
        else:
            return jsonify({'success': False, 'message': 'Data fetching not active'})

    except Exception as e:
        logger.error(f"Error stopping data fetch: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/database-stats')
def get_database_stats():
    """Get database statistics and overview"""
    try:
        import sqlite3
        conn = sqlite3.connect(MARKET_DB_PATH)  # Use DB1 for market data
        cursor = conn.cursor()

        # Get trading data stats
        cursor.execute("SELECT COUNT(*) FROM trading_data")
        total_records = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(DISTINCT symbol) FROM trading_data")
        unique_symbols = cursor.fetchone()[0]

        cursor.execute("SELECT MIN(timestamp), MAX(timestamp) FROM trading_data")
        date_range = cursor.fetchone()

        # Get recent data
        cursor.execute("""
            SELECT symbol, COUNT(*) as record_count, MAX(timestamp) as latest_update
            FROM trading_data
            GROUP BY symbol
            ORDER BY latest_update DESC
            LIMIT 10
        """)
        recent_symbols = cursor.fetchall()

        conn.close()

        # Get DB2 (trading operations) stats
        try:
            conn2 = sqlite3.connect(TRADING_DB_PATH)
            cursor2 = conn2.cursor()

            # Get trading positions
            cursor2.execute("SELECT COUNT(*) FROM trading_positions WHERE status = 'ACTIVE'")
            active_positions = cursor2.fetchone()[0]

            cursor2.execute("SELECT COUNT(*) FROM trading_positions WHERE status = 'SOLD'")
            completed_trades = cursor2.fetchone()[0]

            # Get pending confirmations
            cursor2.execute("SELECT COUNT(*) FROM pending_confirmations WHERE status = 'PENDING'")
            pending_confirmations = cursor2.fetchone()[0]

            # Get portfolio status
            cursor2.execute("SELECT total_pot, available_pot, vault_amount FROM portfolio_status WHERE id = 1")
            portfolio = cursor2.fetchone()

            conn2.close()

            db2_stats = {
                'active_positions': active_positions,
                'completed_trades': completed_trades,
                'pending_confirmations': pending_confirmations,
                'portfolio': {
                    'total_pot': portfolio[0] if portfolio else 0,
                    'available_pot': portfolio[1] if portfolio else 0,
                    'vault_amount': portfolio[2] if portfolio else 0
                } if portfolio else None
            }
        except Exception as e:
            logger.error(f"Error getting DB2 stats: {e}")
            db2_stats = {'error': str(e)}

        stats = {
            'db1_market_data': {
                'total_records': total_records,
                'unique_symbols': unique_symbols,
                'date_range': {
                    'start': date_range[0] if date_range[0] else None,
                    'end': date_range[1] if date_range[1] else None
                },
                'recent_symbols': [
                    {
                        'symbol': row[0],
                        'record_count': row[1],
                        'latest_update': row[2]
                    } for row in recent_symbols
                ]
            },
            'db2_trading_operations': db2_stats,
            'summary': {
                'total_databases': 2,
                'market_data_records': total_records,
                'active_trading_positions': db2_stats.get('active_positions', 0),
                'pending_confirmations': db2_stats.get('pending_confirmations', 0)
            }
        }

        return jsonify({
            'success': True,
            'stats': stats
        })

    except Exception as e:
        logger.error(f"Error fetching database stats: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/recent-signals')
def get_recent_signals():
    """Get recent trading signals"""
    try:
        import sqlite3
        conn = sqlite3.connect('Data/trading_data.db')
        cursor = conn.cursor()

        cursor.execute("""
            SELECT symbol, signal_type, price, timestamp, pattern_sequence, executed
            FROM trading_signals
            ORDER BY timestamp DESC
            LIMIT 20
        """)

        signals = []
        for row in cursor.fetchall():
            signals.append({
                'symbol': row[0],
                'signal_type': row[1],
                'price': row[2],
                'timestamp': row[3],
                'pattern_sequence': row[4],
                'executed': bool(row[5])
            })

        conn.close()

        return jsonify({
            'success': True,
            'signals': signals,
            'count': len(signals)
        })

    except Exception as e:
        logger.error(f"Error fetching recent signals: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/sql-query', methods=['POST'])
def execute_sql_query():
    """Execute custom SQL query"""
    try:
        data = request.get_json()
        query = data.get('query', '').strip()

        if not query:
            return jsonify({'success': False, 'error': 'No query provided'}), 400

        # Security check - only allow SELECT statements
        query_upper = query.upper().strip()
        if not query_upper.startswith('SELECT'):
            return jsonify({
                'success': False,
                'error': 'Only SELECT queries are allowed for security reasons'
            }), 400

        # Additional security checks - only block actual SQL commands, not column names
        dangerous_patterns = [
            r'\bDROP\s+TABLE\b', r'\bDROP\s+DATABASE\b', r'\bDROP\s+INDEX\b',
            r'\bDELETE\s+FROM\b', r'\bDELETE\s+\*\b',
            r'\bUPDATE\s+\w+\s+SET\b',
            r'\bINSERT\s+INTO\b',
            r'\bALTER\s+TABLE\b', r'\bALTER\s+DATABASE\b',
            r'\bCREATE\s+TABLE\b', r'\bCREATE\s+DATABASE\b', r'\bCREATE\s+INDEX\b',
            r'\bTRUNCATE\s+TABLE\b'
        ]

        import re
        for pattern in dangerous_patterns:
            if re.search(pattern, query_upper):
                return jsonify({
                    'success': False,
                    'error': f'Query contains dangerous SQL command pattern'
                }), 400

        import sqlite3
        conn = sqlite3.connect('Data/trading_data.db')
        cursor = conn.cursor()

        # Execute query with timeout
        cursor.execute(query)

        # Get column names
        columns = [description[0] for description in cursor.description] if cursor.description else []

        # Fetch results (limit to 1000 rows for performance)
        rows = cursor.fetchmany(1000)

        # Check if there are more rows
        has_more = len(cursor.fetchmany(1)) > 0

        conn.close()

        # Convert rows to list of dictionaries
        results = []
        for row in rows:
            row_dict = {}
            for i, value in enumerate(row):
                row_dict[columns[i]] = value
            results.append(row_dict)

        return jsonify({
            'success': True,
            'columns': columns,
            'data': results,
            'row_count': len(results),
            'has_more': has_more,
            'query': query
        })

    except sqlite3.Error as e:
        logger.error(f"SQL query error: {e}")
        return jsonify({'success': False, 'error': f'SQL Error: {str(e)}'}), 400

    except Exception as e:
        logger.error(f"Error executing SQL query: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/sql-schema')
def get_database_schema():
    """Get database schema information"""
    try:
        import sqlite3
        conn = sqlite3.connect('Data/trading_data.db')
        cursor = conn.cursor()

        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = [row[0] for row in cursor.fetchall()]

        schema_info = {}

        for table in tables:
            # Get column information for each table
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()

            # Get row count
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            row_count = cursor.fetchone()[0]

            schema_info[table] = {
                'columns': [
                    {
                        'name': col[1],
                        'type': col[2],
                        'not_null': bool(col[3]),
                        'primary_key': bool(col[5])
                    } for col in columns
                ],
                'row_count': row_count
            }

        conn.close()

        return jsonify({
            'success': True,
            'tables': tables,
            'schema': schema_info
        })

    except Exception as e:
        logger.error(f"Error fetching database schema: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/sql-templates')
def get_sql_templates():
    """Get predefined SQL query templates with auto-fetch status and performance analytics"""
    templates = {
        'auto_fetch_status': [
            {
                'name': 'Auto-Fetch Status Check',
                'description': 'Check if 15-minute auto-fetch is running and last execution time',
                'query': '''
                SELECT
                    'AUTO_FETCH_STATUS' as check_type,
                    CASE
                        WHEN MAX(timestamp) >= datetime('now', '-20 minutes') THEN 'ACTIVE'
                        ELSE 'INACTIVE'
                    END as status,
                    MAX(timestamp) as last_fetch_time,
                    COUNT(DISTINCT symbol) as symbols_updated,
                    COUNT(*) as total_records_last_hour
                FROM trading_data
                WHERE timestamp >= datetime('now', '-1 hour')
                '''
            },
            {
                'name': 'Last Auto-Fetch Timestamp',
                'description': 'Get exact timestamp of last auto-fetch execution',
                'query': '''
                SELECT
                    symbol,
                    MAX(timestamp) as last_fetch_time,
                    COUNT(*) as records_today,
                    ROUND((julianday('now') - julianday(MAX(timestamp))) * 24 * 60, 2) as minutes_since_last_fetch
                FROM trading_data
                WHERE DATE(timestamp) = DATE('now')
                GROUP BY symbol
                ORDER BY last_fetch_time DESC
                LIMIT 10
                '''
            },
            {
                'name': 'Auto-Fetch Performance',
                'description': 'Monitor auto-fetch intervals and consistency',
                'query': '''
                SELECT
                    DATE(timestamp) as fetch_date,
                    COUNT(DISTINCT symbol) as symbols_fetched,
                    COUNT(*) as total_intervals,
                    MIN(timestamp) as first_fetch,
                    MAX(timestamp) as last_fetch,
                    ROUND(COUNT(*) / COUNT(DISTINCT symbol), 1) as avg_intervals_per_symbol
                FROM trading_data
                WHERE timestamp >= datetime('now', '-7 days')
                GROUP BY DATE(timestamp)
                ORDER BY fetch_date DESC
                '''
            }
        ],
        'performance_analytics': [
            {
                'name': 'Top Performing Symbols',
                'description': 'Best symbols by profit percentage and success rate',
                'query': '''
                SELECT
                    tp.symbol,
                    COUNT(*) as total_trades,
                    SUM(CASE WHEN tp.actual_profit > 0 THEN 1 ELSE 0 END) as profitable_trades,
                    ROUND(AVG(tp.actual_profit), 2) as avg_profit,
                    ROUND(SUM(tp.actual_profit), 2) as total_profit,
                    ROUND((SUM(CASE WHEN tp.actual_profit > 0 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 1) as success_rate_percent,
                    ROUND(AVG((julianday(tp.sell_time) - julianday(tp.buy_time)) * 24 * 60), 1) as avg_trade_duration_minutes
                FROM trading_positions tp
                WHERE tp.status = 'COMPLETED' AND tp.sell_time IS NOT NULL
                GROUP BY tp.symbol
                HAVING COUNT(*) >= 1
                ORDER BY total_profit DESC
                LIMIT 15
                '''
            },
            {
                'name': 'Worst Performing Symbols',
                'description': 'Symbols with lowest profits and success rates',
                'query': '''
                SELECT
                    tp.symbol,
                    COUNT(*) as total_trades,
                    SUM(CASE WHEN tp.actual_profit > 0 THEN 1 ELSE 0 END) as profitable_trades,
                    ROUND(AVG(tp.actual_profit), 2) as avg_profit,
                    ROUND(SUM(tp.actual_profit), 2) as total_profit,
                    ROUND((SUM(CASE WHEN tp.actual_profit > 0 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 1) as success_rate_percent,
                    ROUND(AVG((julianday(tp.sell_time) - julianday(tp.buy_time)) * 24 * 60), 1) as avg_trade_duration_minutes
                FROM trading_positions tp
                WHERE tp.status = 'COMPLETED' AND tp.sell_time IS NOT NULL
                GROUP BY tp.symbol
                HAVING COUNT(*) >= 1
                ORDER BY total_profit ASC
                LIMIT 15
                '''
            },
            {
                'name': 'Trading Time Analysis',
                'description': 'Average time per symbol per trade and amount analysis',
                'query': '''
                SELECT
                    tp.symbol,
                    COUNT(*) as total_trades,
                    ROUND(AVG(tp.investment), 2) as avg_investment_amount,
                    ROUND(AVG((julianday(tp.sell_time) - julianday(tp.buy_time)) * 24 * 60), 1) as avg_minutes_per_trade,
                    ROUND(MIN((julianday(tp.sell_time) - julianday(tp.buy_time)) * 24 * 60), 1) as fastest_trade_minutes,
                    ROUND(MAX((julianday(tp.sell_time) - julianday(tp.buy_time)) * 24 * 60), 1) as slowest_trade_minutes,
                    ROUND(AVG(tp.actual_profit), 2) as avg_profit_per_trade,
                    ROUND(SUM(tp.actual_profit), 2) as total_profit
                FROM trading_positions tp
                WHERE tp.status = 'COMPLETED' AND tp.sell_time IS NOT NULL AND tp.buy_time IS NOT NULL
                GROUP BY tp.symbol
                HAVING COUNT(*) >= 1
                ORDER BY avg_minutes_per_trade ASC
                LIMIT 20
                '''
            },
            {
                'name': 'Success Rate & Profit Analytics',
                'description': 'Comprehensive success rate, profit percentage, and trading efficiency',
                'query': '''
                SELECT
                    tp.symbol,
                    COUNT(*) as total_trades,
                    SUM(CASE WHEN tp.actual_profit > 0 THEN 1 ELSE 0 END) as wins,
                    SUM(CASE WHEN tp.actual_profit < 0 THEN 1 ELSE 0 END) as losses,
                    ROUND((SUM(CASE WHEN tp.actual_profit > 0 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 1) as win_rate_percent,
                    ROUND(AVG(tp.actual_profit), 2) as avg_profit,
                    ROUND(MAX(tp.actual_profit), 2) as best_trade,
                    ROUND(MIN(tp.actual_profit), 2) as worst_trade,
                    ROUND(SUM(tp.actual_profit), 2) as total_profit,
                    ROUND((SUM(tp.actual_profit) / SUM(tp.investment)) * 100, 2) as roi_percent,
                    ROUND(AVG(tp.investment), 2) as avg_investment
                FROM trading_positions tp
                WHERE tp.status = 'COMPLETED'
                GROUP BY tp.symbol
                HAVING COUNT(*) >= 1
                ORDER BY win_rate_percent DESC, total_profit DESC
                LIMIT 25
                '''
            }
        ],
        'basic_queries': [
            {
                'name': 'Latest Trading Data',
                'description': 'Get latest 10 trading records',
                'query': 'SELECT symbol, timestamp, open_price, high_price, low_price, close_price, volume FROM trading_data ORDER BY timestamp DESC LIMIT 10'
            },
            {
                'name': 'Symbol Summary',
                'description': 'Get record count per symbol',
                'query': 'SELECT symbol, COUNT(*) as record_count, MIN(timestamp) as first_record, MAX(timestamp) as latest_record FROM trading_data GROUP BY symbol ORDER BY record_count DESC'
            },
            {
                'name': 'Daily Volume Leaders',
                'description': 'Top 10 symbols by volume today',
                'query': "SELECT symbol, SUM(volume) as total_volume, AVG(close_price) as avg_price FROM trading_data WHERE DATE(timestamp) = DATE('now') GROUP BY symbol ORDER BY total_volume DESC LIMIT 10"
            },
            {
                'name': 'Data Fetch Status Today',
                'description': 'Check which symbols have data today',
                'query': "SELECT symbol, COUNT(*) as intervals_today, MIN(timestamp) as first_fetch, MAX(timestamp) as last_fetch FROM trading_data WHERE DATE(timestamp) = DATE('now') GROUP BY symbol ORDER BY intervals_today DESC"
            },
            {
                'name': 'Symbol Master List',
                'description': 'View all symbols with tokens',
                'query': 'SELECT symbol, token, company_name, industry, series, exchange FROM symbol_master WHERE is_active = 1 ORDER BY symbol LIMIT 20'
            }
        ],
        'trading_analysis': [
            {
                'name': 'Price Range Analysis',
                'description': 'Daily high-low range for symbols',
                'query': "SELECT symbol, DATE(timestamp) as date, MIN(low_price) as day_low, MAX(high_price) as day_high, (MAX(high_price) - MIN(low_price)) as range_points FROM trading_data WHERE DATE(timestamp) >= DATE('now', '-7 days') GROUP BY symbol, DATE(timestamp) ORDER BY range_points DESC"
            },
            {
                'name': 'Trading Signals',
                'description': 'Recent trading signals',
                'query': 'SELECT symbol, signal_type, price, timestamp, executed FROM trading_signals ORDER BY timestamp DESC LIMIT 20'
            },
            {
                'name': 'Active Positions',
                'description': 'Current active trading positions',
                'query': 'SELECT symbol, buy_price, shares_quantity, investment, target_price, buy_time FROM trading_positions WHERE status = "ACTIVE" ORDER BY buy_time DESC'
            }
        ]
    }

    return jsonify({
        'success': True,
        'templates': templates
    })

@app.route('/api/active-positions/realtime')
def get_realtime_active_positions():
    """Get active positions with real-time P&L calculations"""
    try:
        logger.info("🔄 Fetching real-time active positions with P&L calculations")

        import sqlite3

        # Connect to trading operations database
        conn = sqlite3.connect('Data/trading_operations.db')
        cursor = conn.cursor()

        # Get active positions
        cursor.execute('''
        SELECT symbol, buy_price, shares_quantity, investment, target_price, buy_time, status
        FROM trading_positions
        WHERE status = 'ACTIVE'
        ORDER BY buy_time DESC
        ''')

        active_positions = cursor.fetchall()

        if not active_positions:
            conn.close()
            return jsonify({
                'success': True,
                'positions': [],
                'message': 'No active positions found'
            })

        # Get latest prices for all active symbols
        conn_data = sqlite3.connect('Data/trading_data.db')
        cursor_data = conn_data.cursor()

        positions_with_pnl = []

        for position in active_positions:
            symbol, buy_price, quantity, investment, target_price, buy_time, status = position

            # Get latest price for this symbol
            cursor_data.execute('''
            SELECT close_price, timestamp
            FROM trading_data
            WHERE symbol = ?
            ORDER BY timestamp DESC
            LIMIT 1
            ''', (symbol,))

            latest_data = cursor_data.fetchone()

            if latest_data:
                current_price, last_update = latest_data

                # Calculate P&L
                current_value = current_price * quantity
                pnl = current_value - investment
                pnl_percentage = (pnl / investment) * 100 if investment > 0 else 0

                # Check if target achieved
                target_achieved = current_price >= target_price if target_price else False

                positions_with_pnl.append({
                    'symbol': symbol,
                    'buy_price': round(buy_price, 2),
                    'current_price': round(current_price, 2),
                    'quantity': quantity,
                    'investment': round(investment, 2),
                    'current_value': round(current_value, 2),
                    'target_price': round(target_price, 2) if target_price else 800,
                    'pnl': round(pnl, 2),
                    'pnl_percentage': round(pnl_percentage, 2),
                    'target_achieved': target_achieved,
                    'buy_time': buy_time,
                    'last_update': last_update,
                    'status': status
                })
            else:
                # No current price data available
                positions_with_pnl.append({
                    'symbol': symbol,
                    'buy_price': round(buy_price, 2),
                    'current_price': 0,
                    'quantity': quantity,
                    'investment': round(investment, 2),
                    'current_value': 0,
                    'target_price': round(target_price, 2) if target_price else 800,
                    'pnl': 0,
                    'pnl_percentage': 0,
                    'target_achieved': False,
                    'buy_time': buy_time,
                    'last_update': 'No data',
                    'status': status
                })

        conn.close()
        conn_data.close()

        logger.info(f"✅ Retrieved {len(positions_with_pnl)} active positions with real-time P&L")

        return jsonify({
            'success': True,
            'positions': positions_with_pnl,
            'total_positions': len(positions_with_pnl),
            'last_updated': datetime.now().strftime('%H:%M:%S')
        })

    except Exception as e:
        logger.error(f"Error fetching real-time active positions: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/paper-trading/records')
def get_paper_trading_records():
    """Get paper trading records from DB2 with fast response (optimized)"""
    try:
        import sqlite3

        # Connect to DB2 (trade execution database) - optimized query
        conn = sqlite3.connect('Data/trading_operations.db')
        cursor = conn.cursor()

        # Get paper trading records with optimized query and formatting
        cursor.execute('''
        SELECT symbol, buy_price, shares_quantity, investment, target_price,
               buy_time, sell_time, sell_price, actual_profit, status,
               CASE
                   WHEN status = 'ACTIVE' THEN 'MONITORING'
                   WHEN status = 'COMPLETED' THEN 'PROFIT_TAKEN'
                   ELSE status
               END as display_status
        FROM trading_positions
        ORDER BY
            CASE WHEN status = 'ACTIVE' THEN 0 ELSE 1 END,
            buy_time DESC
        LIMIT 50
        ''')

        records = cursor.fetchall()

        # Format records for display
        formatted_records = []
        for record in records:
            symbol, buy_price, quantity, investment, target_price, buy_time, sell_time, sell_price, actual_profit, status = record

            formatted_records.append({
                'symbol': symbol,
                'buy_price': round(buy_price, 2) if buy_price else 0,
                'quantity': quantity,
                'investment': round(investment, 2) if investment else 0,
                'target_price': round(target_price, 2) if target_price else 800,
                'buy_time': buy_time,
                'sell_time': sell_time,
                'sell_price': round(sell_price, 2) if sell_price else 0,
                'actual_profit': round(actual_profit, 2) if actual_profit else 0,
                'status': status
            })

        # Get summary statistics
        cursor.execute('''
        SELECT
            COUNT(*) as total_trades,
            SUM(CASE WHEN status = 'ACTIVE' THEN 1 ELSE 0 END) as active_positions,
            SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_trades,
            SUM(CASE WHEN actual_profit > 0 THEN 1 ELSE 0 END) as profitable_trades,
            ROUND(AVG(actual_profit), 2) as avg_profit,
            ROUND(SUM(actual_profit), 2) as total_profit,
            COUNT(DISTINCT symbol) as unique_symbols
        FROM trading_positions
        ''')

        stats = cursor.fetchone()

        conn.close()

        logger.info(f"✅ Retrieved {len(formatted_records)} paper trading records")

        return jsonify({
            'success': True,
            'records': formatted_records,
            'stats': {
                'total_trades': stats[0] if stats else 0,
                'active_positions': stats[1] if stats else 0,
                'completed_trades': stats[2] if stats else 0,
                'profitable_trades': stats[3] if stats else 0,
                'avg_profit': stats[4] if stats else 0,
                'total_profit': stats[5] if stats else 0,
                'unique_symbols': stats[6] if stats else 0
            }
        })

    except Exception as e:
        logger.error(f"Error fetching paper trading records: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/layer2-confirmations')
def get_layer2_confirmations():
    """Get Layer 2 confirmation signals with proper trend tracking"""
    try:
        logger.info("🔄 Fetching Layer 2 confirmations with trend tracking")

        import sqlite3
        from datetime import datetime, timedelta

        # Connect to trading operations database for Layer 2 confirmations
        conn = sqlite3.connect(TRADING_DB_PATH)
        cursor = conn.cursor()

        # Ensure pending_confirmations table exists with correct schema (same as confirmation engine)
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS pending_confirmations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            signal_type TEXT NOT NULL,
            trigger_price REAL NOT NULL,
            trigger_time TIMESTAMP NOT NULL,
            confirmation_data TEXT,
            status TEXT DEFAULT 'PENDING',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Add confirmation_data column if it doesn't exist (for existing databases)
        try:
            cursor.execute('ALTER TABLE pending_confirmations ADD COLUMN confirmation_data TEXT')
        except:
            pass  # Column already exists

        # Get pending confirmations from confirmation engine table
        cursor.execute('''
        SELECT symbol, signal_type, trigger_price, trigger_time, confirmation_data, status
        FROM pending_confirmations
        WHERE status = 'PENDING'
        ORDER BY trigger_time DESC
        LIMIT 50
        ''')

        pending_signals = cursor.fetchall()

        confirmations = []

        for signal in pending_signals:
            symbol, signal_type, trigger_price, trigger_time, confirmation_data, status = signal

            # Connect to market data database for price history
            conn_data = sqlite3.connect(MARKET_DB_PATH)
            cursor_data = conn_data.cursor()

            # Get the last 5 price points for this symbol to track trend
            cursor_data.execute('''
            SELECT close_price, timestamp
            FROM trading_data
            WHERE symbol = ?
            ORDER BY timestamp DESC
            LIMIT 5
            ''', (symbol,))

            price_history = cursor_data.fetchall()
            conn_data.close()

            if len(price_history) >= 2:
                # Calculate trend based on last 2 prices (not original signal price)
                current_price = price_history[0][0]
                previous_price = price_history[1][0]

                # Determine trend: RISE or FALL based on previous comparison
                if current_price > previous_price:
                    trend = "RISE"
                else:
                    trend = "FALL"

                # Parse existing confirmation data (JSON format from confirmation engine)
                import json
                try:
                    existing_confirmations = json.loads(confirmation_data) if confirmation_data else []
                except:
                    existing_confirmations = []

                # Create pattern sequence from confirmation data
                pattern_sequence = [conf.get('trend', '') for conf in existing_confirmations]

                # Check for BUY confirmation: 2 consecutive RISE
                buy_confirmation = len(pattern_sequence) >= 2 and pattern_sequence[-2:] == ['RISE', 'RISE']

                # Check for SELL confirmation: 2 consecutive FALL
                sell_confirmation = len(pattern_sequence) >= 2 and pattern_sequence[-2:] == ['FALL', 'FALL']

                confirmations.append({
                    'symbol': symbol,
                    'original_signal': signal_type,
                    'original_price': round(trigger_price, 2),
                    'current_price': round(current_price, 2),
                    'previous_price': round(previous_price, 2),
                    'trend': trend,
                    'pattern_sequence': ','.join(pattern_sequence),
                    'confirmations_count': len(existing_confirmations),
                    'buy_confirmation': buy_confirmation,
                    'sell_confirmation': sell_confirmation,
                    'ready_for_execution': buy_confirmation or sell_confirmation,
                    'timestamp': trigger_time,
                    'status': status
                })

        # Count confirmations by type
        pending_buy = sum(1 for c in confirmations if c['original_signal'] == 'BUY' and not c['ready_for_execution'])
        pending_sell = sum(1 for c in confirmations if c['original_signal'] == 'SELL' and not c['ready_for_execution'])
        ready_for_execution = sum(1 for c in confirmations if c['ready_for_execution'])

        conn.close()

        logger.info(f"✅ Retrieved {len(confirmations)} Layer 2 confirmations")

        return jsonify({
            'success': True,
            'confirmations': confirmations,
            'summary': {
                'pending_buy': pending_buy,
                'pending_sell': pending_sell,
                'ready_for_execution': ready_for_execution,
                'total_pending': len(confirmations)
            },
            'last_updated': datetime.now().strftime('%H:%M:%S')
        })

    except Exception as e:
        logger.error(f"Error fetching Layer 2 confirmations: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/paper-trading/force-action', methods=['POST'])
def force_trading_action():
    """Manual override controls: FORCE HOLD, FORCE BUY, FORCE SELL"""
    try:
        data = request.get_json()
        symbol = data.get('symbol')
        action = data.get('action')  # 'HOLD', 'BUY', 'SELL'
        price = data.get('price', 0)

        logger.info(f"🎯 Manual override: FORCE {action} for {symbol} at ₹{price}")

        import sqlite3
        from datetime import datetime

        conn = sqlite3.connect('Data/trading_operations.db')
        cursor = conn.cursor()

        if action == 'HOLD':
            # Mark symbol to hold - prevent any automatic trading
            # Create trading_signals table if it doesn't exist
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS trading_signals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                signal_type TEXT NOT NULL,
                price REAL NOT NULL,
                timestamp TEXT NOT NULL,
                pattern_sequence TEXT,
                executed INTEGER DEFAULT 0
            )
            ''')

            cursor.execute('''
            UPDATE trading_signals
            SET executed = -1, pattern_sequence = 'FORCE_HOLD'
            WHERE symbol = ? AND executed = 0
            ''', (symbol,))

            message = f"🛑 FORCE HOLD applied to {symbol}. All automatic trading suspended."

        elif action == 'BUY':
            # Force immediate BUY execution with ₹100,000 investment logic
            investment_amount = 100000  # Fixed investment amount
            shares_quantity = investment_amount // price  # Calculate shares (integer division)
            actual_investment = shares_quantity * price  # Actual investment (should be ≤ ₹100,000)

            # Ensure investment is between ₹100,000 and ₹120,000
            if actual_investment < 100000:
                shares_quantity += 1  # Add one more share to exceed ₹100,000
                actual_investment = shares_quantity * price

            # Check if it exceeds ₹120,000, if so reduce by 1 share
            if actual_investment > 120000:
                shares_quantity -= 1
                actual_investment = shares_quantity * price

            cursor.execute('''
            INSERT INTO trading_positions
            (symbol, buy_price, shares_quantity, investment, target_price, buy_time, status)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (symbol, price, shares_quantity, actual_investment, 800, datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 'ACTIVE'))

            # Mark signal as executed
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS trading_signals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                signal_type TEXT NOT NULL,
                price REAL NOT NULL,
                timestamp TEXT NOT NULL,
                pattern_sequence TEXT,
                executed INTEGER DEFAULT 0
            )
            ''')

            cursor.execute('''
            UPDATE trading_signals
            SET executed = 1, pattern_sequence = 'FORCE_BUY'
            WHERE symbol = ? AND executed = 0
            ''', (symbol,))

            message = f"💰 FORCE BUY executed for {symbol} at ₹{price}. Position opened."

        elif action == 'SELL':
            # Force immediate SELL execution for active positions
            cursor.execute('''
            SELECT id, buy_price, shares_quantity, investment
            FROM trading_positions
            WHERE symbol = ? AND status = 'ACTIVE'
            ORDER BY buy_time DESC LIMIT 1
            ''', (symbol,))

            position = cursor.fetchone()

            if position:
                position_id, buy_price, quantity, investment = position

                # Calculate profit with ₹250 standard charges
                gross_profit = (price - buy_price) * quantity
                standard_charges = 250  # Fixed charges per trade
                net_profit = gross_profit - standard_charges

                cursor.execute('''
                UPDATE trading_positions
                SET sell_time = ?, sell_price = ?, actual_profit = ?, status = 'COMPLETED'
                WHERE id = ?
                ''', (datetime.now().strftime('%Y-%m-%d %H:%M:%S'), price, net_profit, position_id))

                message = f"💸 FORCE SELL executed for {symbol} at ₹{price}. Net Profit: ₹{net_profit:.2f}"
            else:
                message = f"❌ No active position found for {symbol} to sell."

        else:
            return jsonify({'success': False, 'error': 'Invalid action. Use HOLD, BUY, or SELL.'}), 400

        conn.commit()
        conn.close()

        logger.info(f"✅ Manual override completed: {message}")

        return jsonify({
            'success': True,
            'message': message,
            'action': action,
            'symbol': symbol,
            'price': price,
            'timestamp': datetime.now().strftime('%H:%M:%S')
        })

    except Exception as e:
        logger.error(f"Error executing force action: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/db1-signal-generation', methods=['POST'])
def generate_db1_signal():
    """Generate DB1 signal and send to DB2 for Layer 2 confirmation"""
    try:
        data = request.get_json()
        symbol = data.get('symbol')
        signal_type = data.get('signal_type')  # 'BUY' or 'SELL'
        price = data.get('price')

        logger.info(f"🎯 DB1 Signal Generation: {signal_type} for {symbol} at ₹{price}")

        import sqlite3
        from datetime import datetime

        # Connect to main database for trading signals
        conn = sqlite3.connect('Data/trading_data.db')
        cursor = conn.cursor()

        # Create trading_signals table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS trading_signals (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            signal_type TEXT NOT NULL,
            price REAL NOT NULL,
            timestamp TEXT NOT NULL,
            pattern_sequence TEXT,
            executed INTEGER DEFAULT 0,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Insert DB1 signal into DB2 for Layer 2 confirmation
        cursor.execute('''
        INSERT INTO trading_signals
        (symbol, signal_type, price, timestamp, pattern_sequence, executed)
        VALUES (?, ?, ?, ?, ?, ?)
        ''', (symbol, signal_type, price, datetime.now().strftime('%Y-%m-%d %H:%M:%S'), '', 0))

        conn.commit()
        conn.close()

        logger.info(f"✅ DB1 signal sent to DB2: {signal_type} {symbol} @ ₹{price}")

        return jsonify({
            'success': True,
            'message': f'DB1 {signal_type} signal generated for {symbol} at ₹{price}. Sent to DB2 for Layer 2 confirmation.',
            'signal': {
                'symbol': symbol,
                'signal_type': signal_type,
                'price': price,
                'timestamp': datetime.now().strftime('%H:%M:%S'),
                'status': 'Sent to Layer 2'
            }
        })

    except Exception as e:
        logger.error(f"Error generating DB1 signal: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/db2-execution', methods=['POST'])
def execute_db2_signal():
    """Execute DB2 signal after Layer 2 confirmation with proper investment logic"""
    try:
        data = request.get_json()
        symbol = data.get('symbol')
        action = data.get('action')  # 'BUY' or 'SELL'
        price = data.get('price')

        logger.info(f"💰 DB2 Execution: {action} for {symbol} at ₹{price}")

        import sqlite3
        from datetime import datetime

        conn = sqlite3.connect('Data/trading_operations.db')
        cursor = conn.cursor()

        if action == 'BUY':
            # Execute BUY with ₹100,000 investment logic
            investment_amount = 100000  # Fixed investment amount
            shares_quantity = investment_amount // price  # Calculate shares (integer division)
            actual_investment = shares_quantity * price  # Actual investment

            # Ensure investment is between ₹100,000 and ₹120,000
            if actual_investment < 100000:
                shares_quantity += 1  # Add one more share to exceed ₹100,000
                actual_investment = shares_quantity * price

            # Check if it exceeds ₹120,000, if so reduce by 1 share
            if actual_investment > 120000:
                shares_quantity -= 1
                actual_investment = shares_quantity * price

            cursor.execute('''
            INSERT INTO trading_positions
            (symbol, buy_price, shares_quantity, investment, target_price, buy_time, status)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (symbol, price, shares_quantity, actual_investment, 800, datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 'ACTIVE'))

            message = f"💰 BUY executed: {symbol} - {shares_quantity} shares at ₹{price} (Investment: ₹{actual_investment})"

        elif action == 'SELL':
            # Execute SELL with ₹250 charges
            cursor.execute('''
            SELECT id, buy_price, shares_quantity, investment
            FROM trading_positions
            WHERE symbol = ? AND status = 'ACTIVE'
            ORDER BY buy_time DESC LIMIT 1
            ''', (symbol,))

            position = cursor.fetchone()

            if position:
                position_id, buy_price, quantity, investment = position

                # Calculate profit with ₹250 standard charges
                gross_profit = (price - buy_price) * quantity
                standard_charges = 250  # Fixed charges per trade
                net_profit = gross_profit - standard_charges

                cursor.execute('''
                UPDATE trading_positions
                SET sell_time = ?, sell_price = ?, actual_profit = ?, status = 'COMPLETED'
                WHERE id = ?
                ''', (datetime.now().strftime('%Y-%m-%d %H:%M:%S'), price, net_profit, position_id))

                message = f"💸 SELL executed: {symbol} at ₹{price} - Net Profit: ₹{net_profit:.2f} (after ₹250 charges)"
            else:
                message = f"❌ No active position found for {symbol} to sell."

        # Mark signal as executed in DB2
        conn_signals = sqlite3.connect('Data/trading_data.db')
        cursor_signals = conn_signals.cursor()
        cursor_signals.execute('''
        UPDATE trading_signals
        SET executed = 1, pattern_sequence = ?
        WHERE symbol = ? AND signal_type = ? AND executed = 0
        ''', (f'EXECUTED_{action}', symbol, action))
        conn_signals.commit()
        conn_signals.close()

        conn.commit()
        conn.close()

        logger.info(f"✅ DB2 execution completed: {message}")

        return jsonify({
            'success': True,
            'message': message,
            'execution': {
                'symbol': symbol,
                'action': action,
                'price': price,
                'timestamp': datetime.now().strftime('%H:%M:%S')
            }
        })

    except Exception as e:
        logger.error(f"Error executing DB2 signal: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/db2-data-fetch', methods=['POST'])
def fetch_db2_data():
    """Fetch 2-minute data for DB2 Layer 2 confirmations (same as DB1 but 2-minute intervals)"""
    try:
        data = request.get_json()
        symbol = data.get('symbol')

        logger.info(f"📊 DB2 Data Fetch: Getting 2-minute data for {symbol}")

        # Use the same Angel One API configuration as DB1
        from realtime_data_fetcher import RealtimeDataFetcher

        # Initialize data fetcher (same as DB1)
        data_fetcher = RealtimeDataFetcher()

        # Get symbol token (same process as DB1)
        symbols_data = symbol_manager.get_symbols_with_tokens()

        token = None
        for sym, tok in symbols_data:
            if sym == symbol:
                token = tok
                break

        if not token:
            return jsonify({'success': False, 'error': f'Token not found for symbol {symbol}'}), 400

        # Fetch 2-minute data (same API, different interval)
        from datetime import datetime, timedelta
        import sqlite3

        # Get last 5 data points for Layer 2 confirmation
        end_time = datetime.now()
        start_time = end_time - timedelta(minutes=10)  # Last 10 minutes for 5 x 2-minute intervals

        # Fetch data from Angel One API (same as DB1)
        historical_data = data_fetcher.fetch_historical_data(
            symbol=symbol,
            token=token,
            start_time=start_time,
            end_time=end_time,
            interval='TWO_MINUTE'  # 2-minute intervals for DB2
        )

        if historical_data:
            # Store in DB2 database (separate from DB1)
            conn = sqlite3.connect('Data/trading_data_db2.db')
            cursor = conn.cursor()

            # Create table with same structure as DB1
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS trading_data_2min (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                token TEXT NOT NULL,
                exchange TEXT DEFAULT 'NSE',
                timestamp TEXT NOT NULL,
                open_price REAL NOT NULL,
                high_price REAL NOT NULL,
                low_price REAL NOT NULL,
                close_price REAL NOT NULL,
                volume INTEGER NOT NULL,
                interval_type TEXT DEFAULT 'TWO_MINUTE',
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, timestamp)
            )
            ''')

            # Insert data
            for record in historical_data:
                try:
                    cursor.execute('''
                    INSERT OR IGNORE INTO trading_data_2min
                    (symbol, token, timestamp, open_price, high_price, low_price, close_price, volume)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (symbol, token, record['timestamp'], record['open'], record['high'],
                          record['low'], record['close'], record['volume']))
                except Exception as e:
                    logger.error(f"Error inserting DB2 data: {e}")

            conn.commit()
            conn.close()

            logger.info(f"✅ DB2 data fetched and stored: {len(historical_data)} records for {symbol}")

            return jsonify({
                'success': True,
                'message': f'DB2 data fetched successfully for {symbol}',
                'records_fetched': len(historical_data),
                'interval': '2-minute',
                'database': 'DB2 (trading_data_db2.db)'
            })
        else:
            return jsonify({'success': False, 'error': 'No data received from Angel One API'}), 500

    except Exception as e:
        logger.error(f"Error fetching DB2 data: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500







@app.route('/api/auto-fetch/start', methods=['POST'])
def start_auto_fetch():
    """Start the automatic 15-minute data fetching system"""
    global data_fetching_active, realtime_fetcher

    try:
        logger.info("🚀 Starting automatic 15-minute data fetching system")

        if data_fetching_active:
            return jsonify({
                'success': False,
                'message': 'Auto-fetch is already running'
            })

        # Initialize realtime fetcher if not exists
        if realtime_fetcher is None:
            from realtime_data_fetcher import RealtimeDataFetcher
            realtime_fetcher = RealtimeDataFetcher()

        # Start auto-fetch in background
        data_fetching_active = True

        def auto_fetch_background():
            try:
                logger.info("🎯 Starting automatic 15-minute data fetching loop...")
                realtime_fetcher.run_realtime_loop()
            except Exception as e:
                logger.error(f"Error in auto-fetch loop: {e}")
                global data_fetching_active
                data_fetching_active = False

        import threading
        fetch_thread = threading.Thread(target=auto_fetch_background, daemon=True)
        fetch_thread.start()

        logger.info("✅ Auto-fetch started - will fetch data every 15 minutes during trading hours")

        return jsonify({
            'success': True,
            'message': '🚀 Auto-fetch started! System will fetch data every 15 minutes (9:15, 9:30, 9:45... till 15:15)',
            'status': 'ACTIVE',
            'schedule': 'Every 15 minutes during trading hours (9:15 AM - 3:15 PM)'
        })

    except Exception as e:
        logger.error(f"Error starting auto-fetch: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/auto-fetch/stop', methods=['POST'])
def stop_auto_fetch():
    """Stop the automatic data fetching system"""
    global data_fetching_active

    try:
        logger.info("🛑 Stopping automatic data fetching system")

        if not data_fetching_active:
            return jsonify({
                'success': False,
                'message': 'Auto-fetch is not running'
            })

        data_fetching_active = False

        logger.info("✅ Auto-fetch stopped")

        return jsonify({
            'success': True,
            'message': '🛑 Auto-fetch stopped successfully',
            'status': 'INACTIVE'
        })

    except Exception as e:
        logger.error(f"Error stopping auto-fetch: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/auto-fetch/status', methods=['GET'])
def get_auto_fetch_status():
    """Get the current status of auto-fetch system"""
    global data_fetching_active

    try:
        import sqlite3
        from datetime import datetime, timedelta

        # Check database for recent data
        conn = sqlite3.connect('Data/trading_data.db')
        cursor = conn.cursor()

        # Get latest data info
        cursor.execute('''
        SELECT
            MAX(timestamp) as last_fetch_time,
            COUNT(DISTINCT symbol) as symbols_updated,
            COUNT(*) as total_records_last_hour
        FROM trading_data
        WHERE timestamp >= datetime('now', '-1 hour')
        ''')

        result = cursor.fetchone()
        last_fetch_time, symbols_updated, total_records_last_hour = result

        # Check if system is active (data within last 20 minutes)
        cursor.execute('''
        SELECT COUNT(*)
        FROM trading_data
        WHERE timestamp >= datetime('now', '-20 minutes')
        ''')

        recent_records = cursor.fetchone()[0]
        is_active = recent_records > 0 and data_fetching_active

        conn.close()

        status = {
            'auto_fetch_active': data_fetching_active,
            'database_status': 'ACTIVE' if is_active else 'INACTIVE',
            'last_fetch_time': last_fetch_time,
            'symbols_updated': symbols_updated or 0,
            'total_records_last_hour': total_records_last_hour or 0,
            'recent_records': recent_records,
            'schedule': 'Every 15 minutes during trading hours (9:15 AM - 3:15 PM)',
            'next_intervals': [
                '09:15', '09:30', '09:45', '10:00', '10:15', '10:30', '10:45', '11:00',
                '11:15', '11:30', '11:45', '12:00', '12:15', '12:30', '12:45', '13:00',
                '13:15', '13:30', '13:45', '14:00', '14:15', '14:30', '14:45', '15:00', '15:15'
            ]
        }

        logger.info(f"📊 Auto-fetch status: {status}")

        return jsonify({
            'success': True,
            'status': status
        })

    except Exception as e:
        logger.error(f"Error getting auto-fetch status: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/manual-data-fetch', methods=['POST'])
def manual_data_fetch():
    """Manually fetch data for a few symbols to test the system"""
    try:
        data = request.get_json()
        symbol_count = data.get('symbol_count', 5)  # Default to 5 symbols

        logger.info(f"🔄 Manual data fetch for {symbol_count} symbols")

        from realtime_data_fetcher import RealtimeDataFetcher
        from datetime import datetime, timedelta
        import time

        # Initialize components
        data_fetcher = RealtimeDataFetcher()
        symbols_with_tokens = symbol_manager.get_symbols_with_tokens()

        # Take first few symbols for testing
        test_symbols = list(symbols_with_tokens)[:symbol_count]

        logger.info(f"📊 Testing data fetch for symbols: {[symbol for symbol, token in test_symbols]}")

        success_count = 0
        failed_count = 0
        fetched_data = []

        for symbol, token in test_symbols:
            try:
                logger.info(f"📊 Fetching data for {symbol} (token: {token})")

                # Fetch 15-minute data
                end_time = datetime.now()
                start_time = end_time - timedelta(hours=2)  # Last 2 hours

                historical_data = data_fetcher.fetch_historical_data(
                    symbol=symbol,
                    token=token,
                    start_time=start_time,
                    end_time=end_time,
                    interval='FIFTEEN_MINUTE'
                )

                if historical_data:

                    # Store in database with duplicate prevention
                    import sqlite3
                    conn = sqlite3.connect('Data/trading_data.db')
                    cursor = conn.cursor()

                    # Insert data using INSERT OR IGNORE to skip duplicates
                    for record in historical_data[-3:]:  # Last 3 records
                        cursor.execute('''
                        INSERT OR IGNORE INTO trading_data
                        (symbol, token, timestamp, open_price, high_price, low_price, close_price, volume)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (symbol, token, record['timestamp'], record['open'],
                              record['high'], record['low'], record['close'], record['volume']))

                    conn.commit()
                    conn.close()

                    fetched_data.append({
                        'symbol': symbol,
                        'records': len(historical_data),
                        'latest_price': historical_data[-1]['close'],
                        'latest_time': historical_data[-1]['timestamp']
                    })

                    success_count += 1
                    logger.info(f"✅ {symbol}: {len(historical_data)} records fetched, latest price: ₹{historical_data[-1]['close']}")
                else:
                    failed_count += 1
                    logger.warning(f"❌ {symbol}: No data received")

                # API rate limiting
                time.sleep(1)

            except Exception as e:
                failed_count += 1
                logger.error(f"❌ Error fetching {symbol}: {e}")
                continue

        logger.info(f"✅ Manual data fetch completed: {success_count} success, {failed_count} failed")

        return jsonify({
            'success': True,
            'message': f'Manual data fetch completed for {symbol_count} symbols',
            'results': {
                'success_count': success_count,
                'failed_count': failed_count,
                'total_symbols': len(test_symbols),
                'fetched_data': fetched_data
            }
        })

    except Exception as e:
        logger.error(f"Error in manual data fetch: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/test-duplicate-prevention', methods=['POST'])
def test_duplicate_prevention():
    """Test duplicate prevention by inserting the same record twice"""
    try:
        logger.info("🧪 Testing duplicate prevention mechanism")

        import sqlite3
        from datetime import datetime

        # Test data
        test_symbol = "TESTDUP"
        test_token = "12345"
        test_timestamp = "2025-06-05 15:15:00"
        test_price = 100.50

        conn = sqlite3.connect('Data/trading_data.db')
        cursor = conn.cursor()

        # First insertion - should succeed
        logger.info(f"🔄 First insertion attempt for {test_symbol}")
        cursor.execute('''
        INSERT OR IGNORE INTO trading_data
        (symbol, token, timestamp, open_price, high_price, low_price, close_price, volume)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (test_symbol, test_token, test_timestamp, test_price, test_price, test_price, test_price, 1000))

        first_insert_count = cursor.rowcount
        logger.info(f"✅ First insertion: {first_insert_count} row(s) affected")

        # Second insertion - should be ignored (duplicate)
        logger.info(f"🔄 Second insertion attempt for {test_symbol} (same timestamp)")
        cursor.execute('''
        INSERT OR IGNORE INTO trading_data
        (symbol, token, timestamp, open_price, high_price, low_price, close_price, volume)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (test_symbol, test_token, test_timestamp, test_price + 10, test_price + 10, test_price + 10, test_price + 10, 2000))

        second_insert_count = cursor.rowcount
        logger.info(f"⏭️ Second insertion: {second_insert_count} row(s) affected (should be 0)")

        # Check total records for this symbol
        cursor.execute('SELECT COUNT(*) FROM trading_data WHERE symbol = ?', (test_symbol,))
        total_records = cursor.fetchone()[0]

        # Get the actual record
        cursor.execute('SELECT * FROM trading_data WHERE symbol = ? ORDER BY timestamp', (test_symbol,))
        records = cursor.fetchall()

        conn.commit()
        conn.close()

        # Clean up test data
        conn = sqlite3.connect('Data/trading_data.db')
        cursor = conn.cursor()
        cursor.execute('DELETE FROM trading_data WHERE symbol = ?', (test_symbol,))
        conn.commit()
        conn.close()

        result = {
            'test_symbol': test_symbol,
            'first_insertion': first_insert_count,
            'second_insertion': second_insert_count,
            'total_records': total_records,
            'duplicate_prevented': second_insert_count == 0,
            'records_found': len(records),
            'test_passed': first_insert_count == 1 and second_insert_count == 0 and total_records == 1
        }

        if result['test_passed']:
            logger.info("✅ DUPLICATE PREVENTION TEST PASSED!")
            logger.info(f"   - First insertion: {first_insert_count} row (✅)")
            logger.info(f"   - Second insertion: {second_insert_count} rows (✅ - duplicate prevented)")
            logger.info(f"   - Total records: {total_records} (✅)")
        else:
            logger.error("❌ DUPLICATE PREVENTION TEST FAILED!")
            logger.error(f"   - First insertion: {first_insert_count} row")
            logger.error(f"   - Second insertion: {second_insert_count} rows")
            logger.error(f"   - Total records: {total_records}")

        return jsonify({
            'success': True,
            'message': 'Duplicate prevention test completed',
            'result': result
        })

    except Exception as e:
        logger.error(f"Error in duplicate prevention test: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500





# ===== DATA INTEGRITY CHECKER APIs =====
@app.route('/api/data-integrity/check', methods=['POST'])
def check_data_integrity():
    """Run data integrity check and fill missing data"""
    try:
        logger.info("Starting data integrity check via web interface")

        # Run the integrity check in background to avoid timeout
        def run_check():
            try:
                import datetime
                # Use the proper method with duplicate prevention
                result = data_integrity_checker.check_and_fill_missing_data(
                    trading_date=datetime.date.today(),
                    skip_complete=True,  # ENABLE DUPLICATE PREVENTION
                    startup_mode=False
                )
                logger.info(f"Data integrity check completed: {result}")

                # Emit results via WebSocket
                socketio.emit('integrity_check_complete', {
                    'success': True,
                    'result': result,
                    'message': f"Checked {result.get('checked', 0)} symbols, filled {result.get('filled', 0)} missing intervals, skipped {result.get('skipped', 0)} complete symbols"
                })

            except Exception as e:
                logger.error(f"Error in background integrity check: {e}")
                socketio.emit('integrity_check_complete', {
                    'success': False,
                    'error': str(e)
                })

        import threading
        check_thread = threading.Thread(target=run_check, daemon=True)
        check_thread.start()

        return jsonify({
            'success': True,
            'message': 'Data integrity check started in background. Check logs for progress.'
        })

    except Exception as e:
        logger.error(f"Error starting integrity check: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/data-integrity/comprehensive-check', methods=['POST'])
def comprehensive_data_integrity_check():
    """🔍 Run COMPREHENSIVE data integrity check for ALL symbols (including newly added ones)"""
    try:
        logger.info("🎯 COMPREHENSIVE CHECK WITH DUPLICATE PREVENTION CALLED!")
        logger.info("🔍 Starting COMPREHENSIVE data integrity check via web interface")
        logger.info("🎯 COMPLETE WORKFLOW: Symbol Manager → Add Symbols → Data Integrity → Comprehensive → Full Trading Flow")

        # Run the comprehensive check in background to avoid timeout
        def run_comprehensive_check():
            try:
                from data_integrity_checker import DataIntegrityChecker
                import datetime
                import sqlite3

                checker = DataIntegrityChecker()
                current_time = datetime.datetime.now()

                logger.info(f"🔍 COMPREHENSIVE CHECK at {current_time.strftime('%H:%M:%S')}")
                logger.info("🎯 GOAL: Make ALL symbols (including newly added) real-time available with last 25 trading hours")
                logger.info("🔄 FLOW: This will enable complete DB1 → DB2 → BUY/SELL trading flow")
                logger.info("📅 SCOPE: Last 25 trading intervals backwards from current time (across trading days)")

                # Function to get last 25 trading intervals backwards
                def get_last_25_trading_intervals():
                    """Get last 25 trading intervals backwards from current time"""
                    intervals = []
                    current = datetime.datetime.now()

                    # Market hours: 9:15 AM to 3:30 PM (Monday to Friday)
                    # 15-minute intervals: 9:15, 9:30, 9:45, ..., 3:15, 3:30

                    while len(intervals) < 25:
                        # Check if current time is a weekday (Monday=0, Sunday=6)
                        if current.weekday() < 5:  # Monday to Friday
                            # Generate trading intervals for this day
                            market_close = current.replace(hour=15, minute=30, second=0, microsecond=0)
                            market_open = current.replace(hour=9, minute=15, second=0, microsecond=0)

                            # If current time is after market close, start from market close
                            if current.time() > market_close.time():
                                interval_time = market_close
                            else:
                                # Round down to nearest 15-minute interval
                                minutes = (current.minute // 15) * 15
                                interval_time = current.replace(minute=minutes, second=0, microsecond=0)
                                # If we're before market open, go to previous day
                                if interval_time.time() < market_open.time():
                                    current = current - datetime.timedelta(days=1)
                                    continue

                            # Add intervals backwards from this point
                            while interval_time >= market_open and len(intervals) < 25:
                                intervals.append(interval_time)
                                interval_time -= datetime.timedelta(minutes=15)

                        # Move to previous day
                        current = current - datetime.timedelta(days=1)

                    return intervals[:25]  # Ensure exactly 25 intervals

                # Get last 25 trading intervals
                target_intervals = get_last_25_trading_intervals()
                logger.info(f"📊 Target intervals: {len(target_intervals)} intervals")
                logger.info(f"📅 From: {target_intervals[-1].strftime('%Y-%m-%d %H:%M')} to {target_intervals[0].strftime('%Y-%m-%d %H:%M')}")

                # Get ALL symbols (including newly added ones)
                symbols = symbol_manager.get_all_symbols()
                symbols_with_tokens = symbol_manager.get_symbols_with_tokens()

                total_symbols = len(symbols)
                total_with_tokens = len(symbols_with_tokens)

                logger.info(f"📊 Processing ALL {total_symbols} symbols ({total_with_tokens} have tokens)")
                logger.info("🚀 This includes ANY newly added symbols from Symbol Manager")

                processed_symbols = 0
                filled_intervals = 0
                symbols_with_data = 0
                newly_functional_symbols = []
                updated_symbols = []

                # Get expected interval count for DUPLICATE PREVENTION (check across target intervals)
                expected_count = len(target_intervals)
                skipped_symbols = 0

                # Process ALL symbols (including newly added ones)
                for symbol in symbols:
                    try:
                        logger.info(f"🔍 Processing {symbol} ({processed_symbols + 1}/{total_symbols})...")

                        # DUPLICATE PREVENTION: Check if symbol already has data for target intervals
                        existing_intervals = set()

                        # Group target intervals by date to check efficiently
                        intervals_by_date = {}
                        for interval in target_intervals:
                            date_key = interval.date()
                            if date_key not in intervals_by_date:
                                intervals_by_date[date_key] = []
                            intervals_by_date[date_key].append(interval)

                        # Get existing intervals for each date
                        for trading_date, date_intervals in intervals_by_date.items():
                            date_existing = checker.get_existing_intervals_for_symbol(symbol, trading_date)
                            existing_intervals.update(date_existing)

                        # Count how many target intervals already exist
                        existing_target_count = sum(1 for interval in target_intervals if interval in existing_intervals)

                        if existing_target_count >= expected_count:
                            skipped_symbols += 1
                            logger.info(f"✅ SKIP: {symbol} already has complete data for target intervals ({existing_target_count}/{expected_count})")
                            processed_symbols += 1
                            continue

                        is_new_symbol = existing_target_count == 0
                        if is_new_symbol:
                            logger.info(f"🆕 NEW SYMBOL: {symbol} (no data) - fetching last 25 trading intervals")
                        else:
                            logger.info(f"📊 INCOMPLETE: {symbol} ({existing_target_count}/{expected_count}) - filling missing intervals")

                        # Check and fill missing data for this symbol (specific 25 intervals backwards)
                        result = checker.check_symbol_specific_intervals(symbol, target_intervals)
                        filled_count = result.get('filled', 0)
                        filled_intervals += filled_count

                        if filled_count > 0:
                            symbols_with_data += 1
                            if is_new_symbol:
                                newly_functional_symbols.append(symbol)
                                logger.info(f"🎉 NEW SYMBOL READY: {symbol} now has {filled_count} data points")
                                logger.info(f"   ✅ {symbol} is now FULLY FUNCTIONAL for:")
                                logger.info(f"      📊 Priority Queue Analysis")
                                logger.info(f"      🎯 Layer 1 Signal Detection (4-FALL+2-RISE)")
                                logger.info(f"      🔄 Layer 2 Confirmations (DB2)")
                                logger.info(f"      💰 Paper Trading Execution")
                            else:
                                updated_symbols.append(symbol)
                                logger.info(f"✅ UPDATED: {symbol} filled {filled_count} missing intervals")
                        else:
                            logger.info(f"✅ COMPLETE: {symbol} already has all required data")

                        processed_symbols += 1

                        if processed_symbols % 10 == 0:
                            logger.info(f"📊 Progress: {processed_symbols}/{total_symbols} symbols processed")

                    except Exception as e:
                        logger.error(f"❌ Error processing {symbol}: {e}")
                        processed_symbols += 1  # Still count as processed
                        continue

                result = {
                    'total_symbols': total_symbols,
                    'processed_symbols': processed_symbols,
                    'skipped_symbols': skipped_symbols,
                    'symbols_with_data': symbols_with_data,
                    'filled_intervals': filled_intervals,
                    'newly_functional_symbols': newly_functional_symbols,
                    'updated_symbols': updated_symbols,
                    'new_symbols_count': len(newly_functional_symbols),
                    'updated_symbols_count': len(updated_symbols),
                    'success_rate': (processed_symbols / total_symbols) * 100 if total_symbols > 0 else 0,
                    'symbols_with_tokens': total_with_tokens,
                    'trading_ready': total_symbols == total_with_tokens
                }

                logger.info(f"✅ COMPREHENSIVE CHECK COMPLETE: {result}")

                if newly_functional_symbols:
                    logger.info(f"🎉 NEW SYMBOLS NOW TRADING-READY: {newly_functional_symbols}")
                    logger.info("🔄 Complete Trading Flow Enabled:")
                    logger.info("   📊 DB1: Historical data analysis for patterns")
                    logger.info("   🎯 Layer 1: 4-FALL+2-RISE signal detection")
                    logger.info("   🔄 Layer 2: DB2 confirmations (2 consecutive signals)")
                    logger.info("   💰 Paper Trading: BUY/SELL execution")

                if updated_symbols:
                    logger.info(f"✅ UPDATED SYMBOLS: {len(updated_symbols)} symbols had missing data filled")

                # Emit results via WebSocket
                socketio.emit('comprehensive_check_complete', {
                    'success': True,
                    'result': result,
                    'message': f"🔍 Comprehensive check complete: {processed_symbols}/{total_symbols} symbols processed, {filled_intervals} intervals filled, {len(newly_functional_symbols)} new symbols now trading-ready"
                })

            except Exception as e:
                logger.error(f"❌ Error in comprehensive check: {e}")
                socketio.emit('comprehensive_check_complete', {
                    'success': False,
                    'error': str(e)
                })

        # Start background thread
        import threading
        check_thread = threading.Thread(target=run_comprehensive_check, daemon=True)
        check_thread.start()

        return jsonify({
            'success': True,
            'message': '🔍 COMPREHENSIVE check started for ALL symbols (including newly added ones). This will make them fully functional for the complete trading flow: DB1 → DB2 → BUY/SELL. Check logs for progress.'
        })

    except Exception as e:
        logger.error(f"Error starting comprehensive check: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/priority-queue/analyze', methods=['POST'])
def manual_priority_queue_analysis():
    """🎯 MANUAL Priority Queue Analysis - Works at ANY TIME (no trading hours restriction)"""
    try:
        logger.info("🎯 MANUAL Priority Queue Analysis - ANYTIME ACCESS")

        # Run the priority queue analysis in background
        def run_manual_analysis():
            try:
                from realtime_data_fetcher import RealtimeDataFetcher
                import datetime

                fetcher = RealtimeDataFetcher()
                current_time = datetime.datetime.now()

                # MANUAL ANALYSIS - NO TIME RESTRICTIONS
                logger.info(f"🎯 MANUAL Analysis at {current_time.strftime('%H:%M:%S')} - NO TRADING HOURS RESTRICTION")
                logger.info("📊 Analyzing SQL DB1 for 4-FALL+2-RISE patterns, positions, and profit levels...")

                priority_batches = fetcher.analyze_symbol_patterns_for_priority()

                # Count symbols in each batch
                batch_counts = {
                    tier: len(batch) for tier, batch in priority_batches.items()
                }

                # Log detailed batch information
                logger.info(f"🥇 GOLD BATCH: {batch_counts['GOLD']} symbols")
                for i, symbol_priority in enumerate(priority_batches['GOLD'][:5], 1):  # Show first 5
                    if symbol_priority.position_status:
                        logger.info(f"  {i}. {symbol_priority.symbol} ({symbol_priority.position_status}: ₹{symbol_priority.profit_amount:.0f})")
                    else:
                        logger.info(f"  {i}. {symbol_priority.symbol} ({symbol_priority.pattern_status})")

                logger.info(f"🥈 SILVER BATCH: {batch_counts['SILVER']} symbols")
                for i, symbol_priority in enumerate(priority_batches['SILVER'][:5], 1):  # Show first 5
                    if symbol_priority.position_status:
                        logger.info(f"  {i}. {symbol_priority.symbol} ({symbol_priority.position_status}: ₹{symbol_priority.profit_amount:.0f})")
                    else:
                        logger.info(f"  {i}. {symbol_priority.symbol} ({symbol_priority.pattern_status})")

                logger.info(f"🥉 BRONZE BATCH: {batch_counts['BRONZE']} symbols")
                for i, symbol_priority in enumerate(priority_batches['BRONZE'][:5], 1):  # Show first 5
                    if symbol_priority.position_status:
                        logger.info(f"  {i}. {symbol_priority.symbol} ({symbol_priority.position_status}: ₹{symbol_priority.profit_amount:.0f})")
                    else:
                        logger.info(f"  {i}. {symbol_priority.symbol} ({symbol_priority.pattern_status})")

                logger.info(f"⚪ REMAINING BATCH: {batch_counts['REMAINING']} symbols")
                for i, symbol_priority in enumerate(priority_batches['REMAINING'][:5], 1):  # Show first 5
                    logger.info(f"  {i}. {symbol_priority.symbol} ({symbol_priority.pattern_status})")

                total_symbols = sum(batch_counts.values())
                logger.info(f"📊 MANUAL PRIORITY ANALYSIS COMPLETE: {total_symbols}/223 symbols analyzed")

                result = {
                    'total': total_symbols,
                    'success': total_symbols,
                    'failed': 0,
                    'batches': batch_counts,
                    'analysis_only': True,
                    'manual_analysis': True,
                    'timestamp': current_time.strftime('%H:%M:%S')
                }
                logger.info(f"✅ Manual priority analysis completed: {result}")

                # Emit results via WebSocket
                socketio.emit('priority_analysis_complete', {
                    'success': True,
                    'result': result,
                    'message': f"🎯 MANUAL Analysis Complete: {total_symbols}/223 symbols analyzed at {current_time.strftime('%H:%M:%S')}"
                })

            except Exception as e:
                logger.error(f"❌ Error in manual priority analysis: {e}")
                socketio.emit('priority_analysis_complete', {
                    'success': False,
                    'error': str(e)
                })

        # Start background thread
        import threading
        analysis_thread = threading.Thread(target=run_manual_analysis, daemon=True)
        analysis_thread.start()

        return jsonify({
            'success': True,
            'message': '🎯 MANUAL Priority Analysis started - Works at ANY TIME! Check logs for detailed results.'
        })

    except Exception as e:
        logger.error(f"Error starting manual priority analysis: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/priority-queue/fetch', methods=['POST'])
def priority_queue_fetch():
    """🎯 Execute PRIORITY QUEUE data fetch (GOLD/SILVER/BRONZE batches)"""
    try:
        logger.info("🎯 Starting PRIORITY QUEUE data fetch via web interface")

        # Run the priority queue fetch in background
        def run_priority_fetch():
            try:
                from realtime_data_fetcher import RealtimeDataFetcher
                import datetime

                fetcher = RealtimeDataFetcher()
                current_time = datetime.datetime.now()

                # Only do SQL analysis - NO API calls
                logger.info("🎯 Executing priority queue analysis (SQL only)...")
                priority_batches = fetcher.analyze_symbol_patterns_for_priority()

                # Count symbols in each batch
                batch_counts = {
                    tier: len(batch) for tier, batch in priority_batches.items()
                }

                # Log detailed batch information
                logger.info(f"🥇 GOLD BATCH: {batch_counts['GOLD']} symbols")
                for i, symbol_priority in enumerate(priority_batches['GOLD'][:5], 1):  # Show first 5
                    if symbol_priority.position_status:
                        logger.info(f"  {i}. {symbol_priority.symbol} ({symbol_priority.position_status}: ₹{symbol_priority.profit_amount:.0f})")
                    else:
                        logger.info(f"  {i}. {symbol_priority.symbol} ({symbol_priority.pattern_status})")

                logger.info(f"🥈 SILVER BATCH: {batch_counts['SILVER']} symbols")
                for i, symbol_priority in enumerate(priority_batches['SILVER'][:5], 1):  # Show first 5
                    if symbol_priority.position_status:
                        logger.info(f"  {i}. {symbol_priority.symbol} ({symbol_priority.position_status}: ₹{symbol_priority.profit_amount:.0f})")
                    else:
                        logger.info(f"  {i}. {symbol_priority.symbol} ({symbol_priority.pattern_status})")

                logger.info(f"🥉 BRONZE BATCH: {batch_counts['BRONZE']} symbols")
                for i, symbol_priority in enumerate(priority_batches['BRONZE'][:5], 1):  # Show first 5
                    if symbol_priority.position_status:
                        logger.info(f"  {i}. {symbol_priority.symbol} ({symbol_priority.position_status}: ₹{symbol_priority.profit_amount:.0f})")
                    else:
                        logger.info(f"  {i}. {symbol_priority.symbol} ({symbol_priority.pattern_status})")

                logger.info(f"⚪ REMAINING BATCH: {batch_counts['REMAINING']} symbols")
                for i, symbol_priority in enumerate(priority_batches['REMAINING'][:5], 1):  # Show first 5
                    logger.info(f"  {i}. {symbol_priority.symbol} ({symbol_priority.pattern_status})")

                total_symbols = sum(batch_counts.values())
                logger.info(f"📊 PRIORITY ANALYSIS COMPLETE: {total_symbols}/222 symbols analyzed")

                if total_symbols != 222:
                    logger.warning(f"⚠️ MISSING SYMBOLS: Expected 222, analyzed {total_symbols}")

                result = {
                    'total': total_symbols,
                    'success': total_symbols,
                    'failed': 0,
                    'batches': batch_counts,
                    'analysis_only': True
                }
                logger.info(f"✅ Priority queue fetch completed: {result}")

                # Emit results via WebSocket
                socketio.emit('priority_fetch_complete', {
                    'success': True,
                    'result': result,
                    'message': f"🎯 Priority Queue: {result.get('success', 0)}/{result.get('total', 0)} symbols fetched successfully"
                })

            except Exception as e:
                logger.error(f"❌ Error in priority queue fetch: {e}")
                socketio.emit('priority_fetch_complete', {
                    'success': False,
                    'error': str(e)
                })

        # Start background thread
        import threading
        fetch_thread = threading.Thread(target=run_priority_fetch, daemon=True)
        fetch_thread.start()

        return jsonify({
            'success': True,
            'message': '🎯 PRIORITY QUEUE fetch started. GOLD→SILVER→BRONZE→REMAINING execution order. Check logs for progress.'
        })

    except Exception as e:
        logger.error(f"Error starting priority queue fetch: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/realtime-fetcher/start', methods=['POST'])
def start_realtime_fetcher():
    """🚀 Start the realtime fetcher with priority queue"""
    global data_fetching_active, realtime_fetcher

    try:
        logger.info("🚀 FORCE STARTING realtime fetcher with priority queue")

        if data_fetching_active:
            return jsonify({
                'success': False,
                'message': 'Realtime fetcher is already running'
            })

        if realtime_fetcher is None:
            realtime_fetcher = RealtimeDataFetcher()

        data_fetching_active = True

        def fetch_data_background():
            try:
                logger.info("🎯 Starting realtime loop with automatic priority queue...")
                realtime_fetcher.run_realtime_loop()
            except Exception as e:
                logger.error(f"Error in realtime loop: {e}")
                global data_fetching_active
                data_fetching_active = False

        import threading
        fetch_thread = threading.Thread(target=fetch_data_background, daemon=True)
        fetch_thread.start()

        logger.info("✅ Realtime fetcher started - priority queue will execute every 15 minutes")

        return jsonify({
            'success': True,
            'message': '🚀 Realtime fetcher started! Priority queue will execute automatically every 15 minutes.'
        })

    except Exception as e:
        logger.error(f"Error starting realtime fetcher: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/realtime-fetcher/stop', methods=['POST'])
def stop_realtime_fetcher():
    """🛑 STOP the realtime fetcher"""
    global data_fetching_active

    try:
        data_fetching_active = False
        logger.info("🛑 Realtime fetcher stopped")

        # Also emit WebSocket notification
        socketio.emit('data_fetch_status', {
            'active': False,
            'message': '🛑 Realtime data fetching stopped'
        })

        return jsonify({
            'success': True,
            'message': '🛑 Realtime fetcher stopped'
        })

    except Exception as e:
        logger.error(f"Error stopping realtime fetcher: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/realtime-fetcher/status')
def get_realtime_fetcher_status():
    """📊 Get current status of realtime fetcher"""
    global data_fetching_active

    return jsonify({
        'success': True,
        'active': data_fetching_active,
        'message': 'Realtime fetcher is running' if data_fetching_active else 'Realtime fetcher is stopped'
    })

@app.route('/api/priority-queue/status')
def get_priority_queue_status():
    """🎯 Get current priority queue batch composition - WORKS AT ANY TIME"""
    try:
        from realtime_data_fetcher import RealtimeDataFetcher
        fetcher = RealtimeDataFetcher()

        # Get current priority batches - FORCE ANALYSIS REGARDLESS OF TIME
        logger.info("🎯 MANUAL Priority Queue Analysis - Running at ANY TIME (no trading hours restriction)")
        priority_batches = fetcher.analyze_symbol_patterns_for_priority()

        # Format for dashboard display
        batch_details = {}
        for tier, batch in priority_batches.items():
            symbols_info = []
            for symbol_priority in batch:
                symbol_info = {
                    'symbol': symbol_priority.symbol,
                    'pattern_status': symbol_priority.pattern_status,
                    'priority_score': symbol_priority.priority_score,
                    'last_price': getattr(symbol_priority, 'last_price', None)
                }

                # Add position info if available
                if hasattr(symbol_priority, 'position_status') and symbol_priority.position_status:
                    symbol_info['position_status'] = symbol_priority.position_status
                    symbol_info['profit_amount'] = getattr(symbol_priority, 'profit_amount', 0)

                symbols_info.append(symbol_info)

            batch_details[tier] = {
                'count': len(batch),
                'symbols': symbols_info
            }

        total_symbols = sum(len(batch) for batch in priority_batches.values())

        return jsonify({
            'success': True,
            'total_symbols': total_symbols,
            'expected_symbols': 222,
            'batches': batch_details,
            'timestamp': datetime.now().strftime('%H:%M:%S')
        })

    except Exception as e:
        logger.error(f"Error getting priority queue status: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/data-integrity/status')
def get_integrity_status():
    """Get data integrity status (quick version)"""
    try:
        # Quick status without checking all symbols
        import sqlite3
        from datetime import date

        conn = sqlite3.connect('Data/trading_data.db')
        cursor = conn.cursor()

        # Get basic stats
        cursor.execute("SELECT COUNT(DISTINCT symbol) FROM trading_data")
        symbols_with_data = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM trading_data")
        total_records = cursor.fetchone()[0]

        # Get actual trading symbols from database (symbols with data)
        total_symbols = symbols_with_data

        conn.close()

        status = {
            'date': date.today().isoformat(),
            'total_symbols': total_symbols,
            'symbols_with_data': symbols_with_data,
            'total_records': total_records,
            'coverage_percentage': (symbols_with_data / total_symbols * 100) if total_symbols > 0 else 0
        }

        return jsonify({
            'success': True,
            'status': status
        })
    except Exception as e:
        logger.error(f"Error getting integrity status: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ===== DUPLICATE REMOVER APIs =====
@app.route('/api/duplicates/scan', methods=['POST'])
def scan_duplicates():
    """Scan for duplicate records"""
    try:
        analysis = duplicate_remover.analyze_duplicates()

        # Convert pandas DataFrames to JSON-serializable format
        duplicates = []
        if 'duplicates_df' in analysis and not analysis['duplicates_df'].empty:
            duplicates = analysis['duplicates_df'].to_dict('records')

        # Create clean analysis without DataFrames
        clean_analysis = {
            'total_records': int(analysis.get('total_records', 0)),
            'unique_records': int(analysis.get('unique_records', 0)),
            'duplicate_records': int(analysis.get('duplicate_records', 0)),
            'duplicate_percentage': float(analysis.get('duplicate_percentage', 0)),
            'duplicate_entries': int(analysis.get('duplicate_entries', 0))
        }

        return jsonify({
            'success': True,
            'duplicates': duplicates,
            'count': len(duplicates),
            'analysis': clean_analysis
        })
    except Exception as e:
        logger.error(f"Error scanning duplicates: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/duplicates/remove', methods=['POST'])
def remove_duplicates():
    """Remove duplicate records"""
    try:
        result = duplicate_remover.remove_duplicates()
        return jsonify({
            'success': True,
            'result': result
        })
    except Exception as e:
        logger.error(f"Error removing duplicates: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ===== SYMBOL MANAGER APIs =====
@app.route('/api/symbols/stats')
def get_symbol_stats():
    """Get symbol statistics"""
    try:
        stats = symbol_manager.get_symbol_stats()
        return jsonify({
            'success': True,
            'stats': stats
        })
    except Exception as e:
        logger.error(f"Error getting symbol stats: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/symbols/missing-tokens')
def get_symbols_missing_tokens():
    """Get symbols that don't have tokens"""
    try:
        # Use symbol_manager for consistent symbol management
        missing_tokens = symbol_manager.get_symbols_without_tokens()
        all_symbols = symbol_manager.get_all_symbols()

        return jsonify({
            'success': True,
            'missing_tokens': missing_tokens,
            'count': len(missing_tokens),
            'total_symbols': len(all_symbols),
            'coverage_percentage': round(((len(all_symbols) - len(missing_tokens)) / len(all_symbols)) * 100, 1) if all_symbols else 0
        })
    except Exception as e:
        logger.error(f"Error getting symbols missing tokens: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/symbols/add', methods=['POST'])
def add_symbols():
    """Add new symbols to the system with complete workflow guidance"""
    try:
        data = request.get_json()
        symbols = data.get('symbols', [])

        if not symbols:
            return jsonify({'success': False, 'error': 'No symbols provided'}), 400

        logger.info(f"🆕 ADDING NEW SYMBOLS: {symbols}")
        logger.info("🔄 WORKFLOW: Symbol Manager → Add Symbols → Data Integrity → Comprehensive → Full Trading Flow")

        success = symbol_manager.add_symbols(symbols)

        if success:
            logger.info(f"✅ SYMBOLS ADDED: {symbols}")
            logger.info("📋 NEXT STEPS:")
            logger.info("   1. Go to Data Integrity tab")
            logger.info("   2. Click 'Comprehensive' button")
            logger.info("   3. System will fetch last 25 data points for new symbols")
            logger.info("   4. New symbols will become fully functional for trading")
            logger.info("🎯 COMPLETE FLOW: DB1 → DB2 → BUY/SELL signals will work normally")

            return jsonify({
                'success': True,
                'message': f'✅ Added {len(symbols)} symbols successfully! Next: Go to Data Integrity → Click Comprehensive to make them trading-ready.',
                'added_count': len(symbols),
                'added_symbols': symbols,
                'duplicates_count': 0,
                'duplicates': [],
                'symbols_added': symbols,
                'next_steps': [
                    'Go to Data Integrity tab',
                    'Click "Comprehensive" button',
                    'System will fetch last 25 data points',
                    'Symbols become fully functional for trading'
                ],
                'workflow': 'Symbol Manager → Add Symbols → Data Integrity → Comprehensive → Full Trading Flow'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to add symbols. Check logs for details.',
                'added_count': 0,
                'added_symbols': [],
                'duplicates_count': 0,
                'duplicates': []
            })

    except Exception as e:
        logger.error(f"Error adding symbols: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/symbols/delete', methods=['POST'])
def delete_symbols():
    """Delete symbols from the system completely"""
    try:
        data = request.get_json()
        symbols = data.get('symbols', [])

        if not symbols:
            return jsonify({'success': False, 'error': 'No symbols provided'}), 400

        success = symbol_manager.delete_symbols(symbols)
        return jsonify({
            'success': success,
            'message': f'Deleted {len(symbols)} symbols' if success else 'Failed to delete symbols',
            'deleted_symbols': symbols if success else [],
            'not_found': []
        })
    except Exception as e:
        logger.error(f"Error deleting symbols: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/symbols/delete-test', methods=['POST'])
def delete_test_symbol():
    """Delete TEST symbol from all databases"""
    try:
        import sqlite3

        deleted_from = []

        # Delete from DB1 (trading_data.db)
        try:
            conn1 = sqlite3.connect('Data/trading_data.db')
            cursor1 = conn1.cursor()
            cursor1.execute('DELETE FROM trading_data WHERE symbol = ?', ('TEST',))
            deleted_count_db1 = cursor1.rowcount
            conn1.commit()
            conn1.close()
            if deleted_count_db1 > 0:
                deleted_from.append(f'DB1: {deleted_count_db1} records')
        except Exception as e:
            logger.error(f"Error deleting from DB1: {e}")

        # Delete from DB2 (trading_operations.db)
        try:
            conn2 = sqlite3.connect('Data/trading_operations.db')
            cursor2 = conn2.cursor()
            cursor2.execute('DELETE FROM trading_positions WHERE symbol = ?', ('TEST',))
            deleted_count_db2 = cursor2.rowcount
            conn2.commit()
            conn2.close()
            if deleted_count_db2 > 0:
                deleted_from.append(f'DB2: {deleted_count_db2} records')
        except Exception as e:
            logger.error(f"Error deleting from DB2: {e}")

        # Delete from symbol manager
        try:
            success = symbol_manager.delete_symbols(['TEST'])
            if success:
                deleted_from.append('Symbol Manager')
        except Exception as e:
            logger.error(f"Error deleting from symbol manager: {e}")

        return jsonify({
            'success': True,
            'message': f'TEST symbol deleted from: {", ".join(deleted_from)}' if deleted_from else 'TEST symbol not found in any database',
            'deleted_from': deleted_from
        })

    except Exception as e:
        logger.error(f"Error deleting TEST symbol: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/data-integrity/check-duplicates', methods=['POST'])
def check_data_duplicates():
    """Check for duplicate data issues and provide detailed analysis"""
    try:
        data = request.get_json()
        symbol = data.get('symbol', 'RELIANCE')  # Default test symbol

        import sqlite3
        from datetime import datetime, date

        # Connect to DB1
        conn = sqlite3.connect('Data/trading_data.db')
        cursor = conn.cursor()

        # Check for duplicates for this symbol
        cursor.execute('''
        SELECT timestamp, COUNT(*) as count
        FROM trading_data
        WHERE symbol = ?
        GROUP BY timestamp
        HAVING COUNT(*) > 1
        ORDER BY timestamp DESC
        LIMIT 10
        ''', (symbol,))

        duplicates = cursor.fetchall()

        # Get total records for this symbol
        cursor.execute('SELECT COUNT(*) FROM trading_data WHERE symbol = ?', (symbol,))
        total_records = cursor.fetchone()[0]

        # Get unique timestamps count
        cursor.execute('SELECT COUNT(DISTINCT timestamp) FROM trading_data WHERE symbol = ?', (symbol,))
        unique_timestamps = cursor.fetchone()[0]

        # Get latest records
        cursor.execute('''
        SELECT timestamp, close_price, created_at
        FROM trading_data
        WHERE symbol = ?
        ORDER BY timestamp DESC
        LIMIT 5
        ''', (symbol,))

        latest_records = cursor.fetchall()
        conn.close()

        # Analysis
        duplicate_count = len(duplicates)
        duplicate_records = sum([count - 1 for _, count in duplicates])  # Extra records beyond first

        return jsonify({
            'success': True,
            'symbol': symbol,
            'analysis': {
                'total_records': total_records,
                'unique_timestamps': unique_timestamps,
                'duplicate_timestamps': duplicate_count,
                'duplicate_records': duplicate_records,
                'data_efficiency': f"{(unique_timestamps/total_records)*100:.1f}%" if total_records > 0 else "0%"
            },
            'duplicates': [
                {
                    'timestamp': timestamp,
                    'duplicate_count': count,
                    'extra_records': count - 1
                }
                for timestamp, count in duplicates
            ],
            'latest_records': [
                {
                    'timestamp': timestamp,
                    'close_price': close_price,
                    'created_at': created_at
                }
                for timestamp, close_price, created_at in latest_records
            ],
            'database_location': 'Data/trading_data.db (DB1)',
            'recommendation': 'No duplicates found' if duplicate_count == 0 else f'Found {duplicate_count} duplicate timestamps with {duplicate_records} extra records'
        })

    except Exception as e:
        logger.error(f"Error checking data duplicates: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500



@app.route('/api/symbols/search')
def search_symbols():
    """Search symbols"""
    try:
        query = request.args.get('q', '')
        results = symbol_manager.search_symbols(query)

        # Extract just the symbol names for the frontend
        symbol_names = [result['Symbol'] for result in results] if results else []

        return jsonify({
            'success': True,
            'results': symbol_names,
            'count': len(symbol_names),
            'detailed_results': results  # Keep full results for other uses
        })
    except Exception as e:
        logger.error(f"Error searching symbols: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/symbols/scan-duplicates')
def scan_duplicate_symbols():
    """Scan for duplicate symbols in Excel, DB1, and DB2"""
    try:
        import sqlite3

        duplicates_found = {
            'excel': [],
            'db1': [],
            'db2': [],
            'total_count': 0
        }

        # Check Excel file
        try:
            df = symbol_manager.load_symbols_df()
            excel_duplicates = df[df.duplicated(subset=['Symbol'], keep=False)]['Symbol'].tolist()
            duplicates_found['excel'] = list(set(excel_duplicates))
        except Exception as e:
            logger.error(f"Error checking Excel duplicates: {e}")

        # Check DB1 - Look for REAL duplicates (same symbol + same timestamp)
        try:
            conn1 = sqlite3.connect(MARKET_DB_PATH)
            cursor1 = conn1.cursor()

            # Find symbols with duplicate timestamp entries (REAL duplicates)
            cursor1.execute('''
                SELECT symbol
                FROM trading_data
                GROUP BY symbol, timestamp
                HAVING COUNT(*) > 1
            ''')
            db1_duplicates = [row[0] for row in cursor1.fetchall()]
            duplicates_found['db1'] = list(set(db1_duplicates))  # Remove duplicates from result

            conn1.close()
        except Exception as e:
            logger.error(f"Error checking DB1 duplicates: {e}")

        # Check DB2 - Look for REAL duplicates (same symbol + same timestamp)
        try:
            conn2 = sqlite3.connect(TRADING_DB_PATH)
            cursor2 = conn2.cursor()

            # Check multiple tables in DB2 for REAL duplicates
            db2_duplicates = []
            tables_to_check = ['trading_positions', 'pending_confirmations']

            for table in tables_to_check:
                try:
                    # Look for real duplicates (same symbol + same timestamp)
                    cursor2.execute(f'''
                        SELECT symbol
                        FROM {table}
                        GROUP BY symbol, timestamp
                        HAVING COUNT(*) > 1
                    ''')
                    table_duplicates = [row[0] for row in cursor2.fetchall()]
                    db2_duplicates.extend(table_duplicates)
                except:
                    continue

            duplicates_found['db2'] = list(set(db2_duplicates))
            conn2.close()
        except Exception as e:
            logger.error(f"Error checking DB2 duplicates: {e}")

        # Calculate total
        all_duplicates = set(duplicates_found['excel'] + duplicates_found['db1'] + duplicates_found['db2'])
        duplicates_found['total_count'] = len(all_duplicates)
        duplicates_found['all_duplicates'] = list(all_duplicates)

        return jsonify({
            'success': True,
            'duplicates': duplicates_found
        })

    except Exception as e:
        logger.error(f"Error scanning duplicates: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/symbols/clean-duplicates', methods=['POST'])
def clean_duplicate_symbols():
    """Clean duplicate symbols from Excel, DB1, and DB2"""
    try:
        success = symbol_manager.clean_duplicate_symbols()
        return jsonify({
            'success': success,
            'message': 'Cleaned duplicate symbols from Excel, DB1, and DB2' if success else 'Failed to clean duplicates'
        })
    except Exception as e:
        logger.error(f"Error cleaning duplicate symbols: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/symbols/export')
def export_symbols():
    """Export symbols to CSV"""
    try:
        filename = request.args.get('filename', 'symbols_export.csv')
        success = symbol_manager.export_symbols(filename)
        return jsonify({
            'success': success,
            'filename': filename,
            'message': f'Exported to {filename}' if success else 'Export failed'
        })
    except Exception as e:
        logger.error(f"Error exporting symbols: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ===== PAPER TRADING CSV APIs =====
@app.route('/api/paper-trades')
def get_paper_trades():
    """Get paper trading records from database"""
    try:
        import sqlite3

        conn = sqlite3.connect(TRADING_DB_PATH)  # Use trading operations database
        cursor = conn.cursor()

        # Get all paper trading records from database
        cursor.execute('''
        SELECT symbol, buy_price, shares_quantity, investment, target_value,
               target_price, buy_time, status, sell_price, sell_time, actual_profit
        FROM trading_positions
        ORDER BY buy_time DESC
        ''')

        records = cursor.fetchall()
        conn.close()

        trades = []
        for record in records:
            symbol, buy_price, shares_qty, investment, target_val, target_price, buy_time, status, sell_price, sell_time, actual_profit = record

            # Format the trade record
            if status == 'ACTIVE':
                trades.append({
                    'Symbol Name': symbol,
                    'Date Time': buy_time,
                    'Price': f"{buy_price:.2f}",
                    'Signals': 'BUY',
                    'API Call Link': f"smart_api.placeOrder(symbol='{symbol}', transactiontype='BUY', quantity={shares_qty}, price={buy_price:.2f})"
                })
            elif status == 'SOLD':
                # Add both BUY and SELL records for completed trades
                trades.append({
                    'Symbol Name': symbol,
                    'Date Time': buy_time,
                    'Price': f"{buy_price:.2f}",
                    'Signals': 'BUY',
                    'API Call Link': f"smart_api.placeOrder(symbol='{symbol}', transactiontype='BUY', quantity={shares_qty}, price={buy_price:.2f})"
                })
                if sell_time and sell_price:
                    trades.append({
                        'Symbol Name': symbol,
                        'Date Time': sell_time,
                        'Price': f"{sell_price:.2f}",
                        'Signals': 'SELL',
                        'API Call Link': f"smart_api.placeOrder(symbol='{symbol}', transactiontype='SELL', quantity={shares_qty}, price={sell_price:.2f})"
                    })

        return jsonify({
            'success': True,
            'trades': trades,
            'count': len(trades)
        })

    except Exception as e:
        logger.error(f"Error reading paper trades from database: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/paper-trades/download')
def download_paper_trades():
    """Download paper trading CSV file"""
    try:
        csv_file = "Data/csv_files/paper_trade.csv"

        if not os.path.exists(csv_file):
            return jsonify({'success': False, 'error': 'Paper trading file not found'}), 404

        return send_file(csv_file, as_attachment=True, download_name='paper_trade.csv')

    except Exception as e:
        logger.error(f"Error downloading paper trades: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/paper-trades/clear', methods=['POST'])
def clear_paper_trades():
    """Clear paper trading CSV file"""
    try:
        csv_file = "Data/csv_files/paper_trade.csv"

        # Recreate file with headers only
        headers = ['Symbol Name', 'Date Time', 'Price', 'API Call Link', 'Signals']
        with open(csv_file, 'w', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)
            writer.writerow(headers)

        return jsonify({
            'success': True,
            'message': 'Paper trading records cleared'
        })

    except Exception as e:
        logger.error(f"Error clearing paper trades: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/force-control', methods=['POST'])
def force_control():
    """Force control for paper trading - HOLD, BUY, or SELL"""
    try:
        data = request.get_json()
        action = data.get('action')  # 'HOLD', 'BUY', 'SELL'
        symbol = data.get('symbol')
        price = data.get('price', 0)

        if not action or not symbol:
            return jsonify({'success': False, 'error': 'Missing action or symbol'}), 400

        logger.info(f"🎮 FORCE CONTROL: {action} for {symbol} @ ₹{price}")

        if action == 'HOLD':
            # Cancel any pending confirmations for this symbol
            if trading_engine.confirmation_engine:
                trading_engine.confirmation_engine.cancel_pending_confirmation(symbol)

            logger.info(f"🟡 FORCE HOLD executed for {symbol}")
            broadcast_log('FORCE', f'🟡 FORCE HOLD: {symbol} - All pending actions cancelled', 'warning')

            return jsonify({
                'success': True,
                'message': f'FORCE HOLD executed for {symbol}',
                'action': 'HOLD'
            })

        elif action == 'BUY':
            # Execute immediate paper BUY
            success = trading_engine.execute_buy_signal(symbol, price)

            if success:
                logger.info(f"🟢 FORCE BUY executed for {symbol} @ ₹{price}")
                broadcast_log('FORCE', f'🟢 FORCE BUY: {symbol} @ ₹{price:.2f}', 'success')

                return jsonify({
                    'success': True,
                    'message': f'FORCE BUY executed for {symbol} @ ₹{price:.2f}',
                    'action': 'BUY'
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to execute BUY signal'}), 500

        elif action == 'SELL':
            # Execute immediate paper SELL
            success = trading_engine.execute_sell_signal(symbol, price)

            if success:
                logger.info(f"🔴 FORCE SELL executed for {symbol} @ ₹{price}")
                broadcast_log('FORCE', f'🔴 FORCE SELL: {symbol} @ ₹{price:.2f}', 'danger')

                return jsonify({
                    'success': True,
                    'message': f'FORCE SELL executed for {symbol} @ ₹{price:.2f}',
                    'action': 'SELL'
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to execute SELL signal'}), 500

        else:
            return jsonify({'success': False, 'error': f'Invalid action: {action}'}), 400

    except Exception as e:
        logger.error(f"Error in force control: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/confirmation-status')
def get_confirmation_status():
    """Get Layer 2 confirmation status"""
    try:
        if not trading_engine.confirmation_engine:
            return jsonify({
                'success': True,
                'pending_buy': 0,
                'pending_sell': 0,
                'confirmed_today': 0,
                'pending_confirmations': [],
                'message': 'Confirmation engine not initialized'
            })

        # Get pending confirmations from memory
        pending_confirmations = trading_engine.confirmation_engine.pending_confirmations

        # Count by type
        pending_buy = sum(1 for conf in pending_confirmations.values() if conf.signal_type == 'BUY')
        pending_sell = sum(1 for conf in pending_confirmations.values() if conf.signal_type == 'SELL')

        # Get confirmed trades today from DB2 (trading_operations.db)
        import sqlite3
        conn = sqlite3.connect('Data/trading_operations.db')
        cursor = conn.cursor()

        # Get confirmed trades from trading_positions table
        try:
            cursor.execute('''
            SELECT COUNT(*) FROM trading_positions
            WHERE status IN ('BOUGHT', 'SOLD')
            AND DATE(created_at) = DATE('now')
            ''')
            result = cursor.fetchone()
            confirmed_today = result[0] if result else 0
        except Exception as e:
            logger.error(f"Error getting confirmed trades: {e}")
            confirmed_today = 0

        # Also get pending confirmations from DB2
        try:
            cursor.execute('''
            SELECT COUNT(*) FROM pending_confirmations
            WHERE status = 'PENDING'
            ''')
            result = cursor.fetchone()
            db2_pending = result[0] if result else 0
        except Exception as e:
            logger.error(f"Error getting DB2 pending confirmations: {e}")
            db2_pending = 0

        conn.close()

        # Format pending confirmations for display
        import datetime
        confirmation_details = []
        for symbol, conf in pending_confirmations.items():
            confirmations_received = len([d for d in conf.confirmation_data if d.get('trend')])
            time_elapsed = (datetime.datetime.now() - conf.trigger_time).total_seconds()

            if time_elapsed < 60:
                time_str = f"{int(time_elapsed)}s ago"
            elif time_elapsed < 3600:
                time_str = f"{int(time_elapsed/60)}m ago"
            else:
                time_str = f"{int(time_elapsed/3600)}h ago"

            confirmation_details.append({
                'symbol': symbol,
                'signal_type': conf.signal_type,
                'trigger_price': conf.trigger_price,
                'confirmations_received': confirmations_received,
                'time_elapsed': time_str
            })

        return jsonify({
            'success': True,
            'pending_buy': pending_buy,
            'pending_sell': pending_sell,
            'confirmed_today': confirmed_today,
            'pending_confirmations': confirmation_details,
            'db2_pending': db2_pending,
            'total_pending': pending_buy + pending_sell + db2_pending,
            'engine_status': 'ACTIVE' if trading_engine.confirmation_engine else 'INACTIVE'
        })

    except Exception as e:
        logger.error(f"Error getting confirmation status: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500



@app.route('/api/manual-paper-trading', methods=['POST'])
def manual_paper_trading():
    """Manual paper trading entry - bypass DB1, go directly to DB2"""
    global trading_engine

    try:
        data = request.get_json()
        symbol = data.get('symbol', '').strip().upper()
        signal_type = data.get('signal_type', '').upper()  # BUY or SELL

        if not symbol or not signal_type:
            return jsonify({'status': 'error', 'message': 'Symbol and signal_type are required'}), 400

        if signal_type not in ['BUY', 'SELL']:
            return jsonify({'status': 'error', 'message': 'signal_type must be BUY or SELL'}), 400

        # Get current price for the symbol
        try:
            # Try to get latest price from database first
            import sqlite3
            conn = sqlite3.connect(MARKET_DB_PATH)
            cursor = conn.cursor()
            cursor.execute('''
                SELECT close_price FROM trading_data
                WHERE symbol = ?
                ORDER BY timestamp DESC
                LIMIT 1
            ''', (symbol,))

            result = cursor.fetchone()
            conn.close()

            if result:
                current_price = result[0]
            else:
                return jsonify({'status': 'error', 'message': f'No price data found for {symbol}'}), 400

        except Exception as e:
            return jsonify({'status': 'error', 'message': f'Error getting price for {symbol}: {e}'}), 500

        # Add signal directly to DB2 confirmation engine
        if trading_engine and hasattr(trading_engine, 'confirmation_engine') and trading_engine.confirmation_engine:
            if signal_type == 'BUY':
                trading_engine.confirmation_engine.add_buy_signal_for_confirmation(symbol, current_price)
                logger.info(f"🎯 MANUAL BUY: {symbol} @ ₹{current_price:.2f} added to DB2 confirmation")
            else:  # SELL
                trading_engine.confirmation_engine.add_sell_signal_for_confirmation(symbol, current_price)
                logger.info(f"🎯 MANUAL SELL: {symbol} @ ₹{current_price:.2f} added to DB2 confirmation")

            return jsonify({
                'status': 'success',
                'message': f'Manual {signal_type} signal for {symbol} added to DB2 confirmation',
                'symbol': symbol,
                'signal_type': signal_type,
                'price': current_price,
                'flow': 'MANUAL → DB2 → Paper Trading'
            })
        else:
            return jsonify({'status': 'error', 'message': 'Trading engine or confirmation engine not available'}), 500

    except Exception as e:
        logger.error(f"Error in manual paper trading: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/paper-trades/complete-reset', methods=['POST'])
def complete_reset_paper_trades():
    """🔥 COMPLETE TRADING SYSTEM RESET: Clear all trading data except total pot"""
    try:
        import sqlite3

        logger.info("🔥 STARTING COMPLETE TRADING SYSTEM RESET...")
        broadcast_log('SYSTEM', '🔥 COMPLETE TRADING SYSTEM RESET INITIATED', 'warning')

        # 1. Clear CSV file
        csv_file = "Data/csv_files/paper_trade.csv"
        headers = ['Symbol Name', 'Date Time', 'Price', 'API Call Link', 'Signals']
        with open(csv_file, 'w', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)
            writer.writerow(headers)
        logger.info("✅ CSV file cleared")

        # 2. Reset DB2 (trading operations database) - COMPREHENSIVE CLEANUP
        trading_conn = sqlite3.connect(TRADING_DB_PATH)
        trading_cursor = trading_conn.cursor()

        # Count records before deletion for reporting
        try:
            trading_cursor.execute("SELECT COUNT(*) FROM trading_positions")
            positions_count = trading_cursor.fetchone()[0]
        except:
            positions_count = 0

        try:
            trading_cursor.execute("SELECT COUNT(*) FROM pending_confirmations")
            confirmations_count = trading_cursor.fetchone()[0]
        except:
            confirmations_count = 0

        # Clear all trading-related tables in DB2
        tables_to_clear = [
            'trading_positions',        # All BUY/SELL positions
            'pending_confirmations',    # Layer 2 confirmations
            'confirmation_price_data'   # 2-minute confirmation data
        ]

        for table_name in tables_to_clear:
            try:
                trading_cursor.execute(f"DELETE FROM {table_name}")
                logger.info(f"✅ Cleared DB2 table: {table_name}")
            except sqlite3.OperationalError as e:
                logger.warning(f"⚠️ DB2 table {table_name} doesn't exist: {e}")

        # Reset portfolio status in DB2 - KEEP TOTAL POT, RESET EVERYTHING ELSE
        trading_cursor.execute("""
            UPDATE portfolio_status SET
            total_pot = 22200000.0,
            available_pot = 22200000.0,
            used_funds = 0.0,
            vault_amount = 0.0,
            active_positions = 0,
            total_profit = 0.0,
            updated_at = CURRENT_TIMESTAMP
            WHERE id = 1
        """)
        logger.info("✅ DB2 Portfolio status reset - Total pot preserved at ₹2.22 Cr")

        trading_conn.commit()
        trading_conn.close()

        # Set signals_count for logging
        signals_count = 0

        # 3. Reset trading engine state if available
        if 'trading_engine' in globals() and trading_engine:
            # Reset vault and positions
            trading_engine.vault_amount = 0.0
            trading_engine.active_positions.clear()
            trading_engine.completed_trades.clear()

            # Reset trading states for all symbols
            for symbol in trading_engine.trading_states:
                trading_engine.trading_states[symbol] = {
                    'position': None,
                    'last_analysis_time': None,
                    'consecutive_patterns': 0
                }

            logger.info("✅ Trading engine state reset")

        # 4. Emit real-time updates to frontend
        socketio.emit('portfolio_update', {
            'total_pot': 22200000.0,
            'available_pot': 22200000.0,
            'used_funds': 0.0,
            'vault_amount': 0.0,
            'active_positions': 0,
            'completed_trades': 0,
            'total_profit': 0.0
        })

        # Log comprehensive reset summary
        reset_summary = f"""
🔥 COMPLETE TRADING SYSTEM RESET COMPLETED:

📊 DATA CLEARED:
• Trading Positions: {positions_count} records deleted
• Trading Signals: {signals_count} records deleted
• Paper Trading CSV: Cleared
• Trading Engine State: Reset

💰 FINANCIAL STATUS RESET:
• Total Pot: ₹2.22 Cr (PRESERVED)
• Available Pot: ₹2.22 Cr (RESET)
• Used Funds: ₹0 (RESET)
• Vault Amount: ₹0 (RESET)
• Active Positions: 0 (RESET)
• Completed Trades: 0 (RESET)

🚀 System is ready for fresh trading!
        """

        logger.info(reset_summary)
        broadcast_log('SYSTEM', '✅ COMPLETE RESET SUCCESSFUL - All trading data cleared', 'success')
        broadcast_log('SYSTEM', f'📊 Cleared {positions_count} positions and {signals_count} signals', 'success')
        broadcast_log('SYSTEM', '💰 Portfolio reset: ₹2.22 Cr available, ready for trading', 'success')

        return jsonify({
            'success': True,
            'message': f'Complete reset successful: {positions_count} positions and {signals_count} signals cleared. Portfolio reset to ₹2.22 Cr available.',
            'details': {
                'positions_cleared': positions_count,
                'signals_cleared': signals_count,
                'total_pot': 22200000.0,
                'vault_amount': 0.0,
                'active_positions': 0
            }
        })

    except Exception as e:
        logger.error(f"❌ Error performing complete reset: {e}")
        broadcast_log('SYSTEM', f'❌ Complete reset failed: {str(e)}', 'error')
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/dual-system/performance')
def get_dual_system_performance():
    """Get dual-database system performance metrics"""
    try:
        # Get performance metrics from the dual-database system
        performance = trading_engine.get_dual_system_performance()

        return jsonify({
            'success': True,
            'performance': performance
        })

    except Exception as e:
        logger.error(f"Error getting dual system performance: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# Add new route for trading logs
@app.route('/api/trading-logs')
def get_trading_logs():
    """Get trading-specific logs (BUY/SELL signals only)"""
    try:
        import sqlite3
        conn = sqlite3.connect('Data/trading_data.db')
        cursor = conn.cursor()

        # Get recent trading signals with details
        cursor.execute("""
            SELECT symbol, signal_type, price, timestamp, pattern_sequence, executed
            FROM trading_signals
            ORDER BY timestamp DESC
            LIMIT 50
        """)

        logs = []
        for row in cursor.fetchall():
            symbol, signal_type, price, timestamp, pattern_sequence, executed = row

            # Create detailed log message
            if signal_type == 'BUY':
                reason = "4-FALL+2-RISE pattern detected"
                message = f"BUY {symbol} @ ₹{price:.2f} - {reason}"
            else:
                reason = "₹800 profit target reached" if executed else "SELL signal detected"
                message = f"SELL {symbol} @ ₹{price:.2f} - {reason}"

            logs.append({
                'timestamp': timestamp,
                'type': signal_type,
                'symbol': symbol,
                'price': price,
                'message': message,
                'reason': reason,
                'executed': executed,
                'pattern': pattern_sequence
            })

        conn.close()

        return jsonify({
            'success': True,
            'logs': logs,
            'count': len(logs)
        })

    except Exception as e:
        logger.error(f"Error fetching trading logs: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500









@app.route('/api/data-integrity/check-with-progress', methods=['POST'])
def check_data_integrity_with_progress():
    """Enhanced data integrity check with real-time progress updates"""
    try:
        data = request.get_json() or {}
        check_last_intervals = data.get('check_last_intervals', 25)
        trading_hours_only = data.get('trading_hours_only', True)

        logger.info(f"📊 Enhanced data integrity check requested - Last {check_last_intervals} intervals, Trading hours only: {trading_hours_only}")

        # Run the integrity check in background with progress updates
        def run_enhanced_check():
            try:
                # Emit initial progress
                socketio.emit('integrity_check_progress', {
                    'progress': 5,
                    'status': 'Initializing data integrity check...',
                    'message': f'📊 Starting check for last {check_last_intervals} intervals across 222 symbols',
                    'type': 'info'
                })

                # Get symbols list
                symbols = symbol_manager.get_all_symbols()
                total_symbols = len(symbols)

                socketio.emit('integrity_check_progress', {
                    'progress': 10,
                    'status': f'Found {total_symbols} symbols to check...',
                    'message': f'📋 Loaded {total_symbols} symbols for integrity check',
                    'type': 'info'
                })

                # Initialize data integrity checker
                from data_integrity_checker import DataIntegrityChecker
                integrity_checker = DataIntegrityChecker()

                # Run the actual integrity check with progress tracking
                filled_count = 0
                checked_count = 0

                for i, symbol in enumerate(symbols):
                    try:
                        # Calculate progress (10% to 90% for symbol processing)
                        progress = 10 + int((i / total_symbols) * 80)

                        # Emit progress every 10 symbols or for important symbols
                        if i % 10 == 0 or i < 5 or i >= total_symbols - 5:
                            socketio.emit('integrity_check_progress', {
                                'progress': progress,
                                'status': f'Checking {symbol} ({i+1}/{total_symbols})...',
                                'message': f'🔍 Checking data integrity for {symbol}',
                                'type': 'info'
                            })

                        # Check this symbol's data integrity for last N intervals
                        today = datetime.now().date()
                        symbol_result = integrity_checker.check_symbol_last_intervals(symbol, check_last_intervals, today)

                        if symbol_result and symbol_result.get('filled', 0) > 0:
                            filled_count += symbol_result.get('filled', 0)
                            socketio.emit('integrity_check_progress', {
                                'progress': progress,
                                'status': f'Filled {symbol_result.get("filled", 0)} intervals for {symbol}',
                                'message': f'✅ {symbol}: Filled {symbol_result.get("filled", 0)} missing intervals',
                                'type': 'success'
                            })

                        checked_count += 1

                    except Exception as e:
                        logger.error(f"Error checking {symbol}: {e}")
                        socketio.emit('integrity_check_progress', {
                            'progress': progress,
                            'status': f'Error checking {symbol}: {str(e)[:50]}...',
                            'message': f'❌ {symbol}: Error during check - {str(e)}',
                            'type': 'warning'
                        })

                # Final results
                socketio.emit('integrity_check_progress', {
                    'progress': 95,
                    'status': 'Finalizing results...',
                    'message': f'📊 Completed checking {checked_count}/{total_symbols} symbols',
                    'type': 'info'
                })

                result = {
                    'checked': checked_count,
                    'total': total_symbols,
                    'filled': filled_count,
                    'check_last_intervals': check_last_intervals,
                    'trading_hours_only': trading_hours_only
                }

                logger.info(f"✅ Enhanced data integrity check completed: {result}")

                # Emit final completion
                socketio.emit('integrity_check_complete', {
                    'success': True,
                    'result': result,
                    'message': f"✅ Data Integrity Complete: Checked {checked_count}/{total_symbols} symbols, filled {filled_count} missing intervals (last {check_last_intervals} intervals)"
                })

            except Exception as e:
                logger.error(f"❌ Error in enhanced integrity check: {e}")
                socketio.emit('integrity_check_complete', {
                    'success': False,
                    'error': str(e)
                })

        # Start background thread
        import threading
        check_thread = threading.Thread(target=run_enhanced_check, daemon=True)
        check_thread.start()

        return jsonify({
            'success': True,
            'message': f'📊 Enhanced data integrity check started for last {check_last_intervals} intervals. Real-time progress will be shown.'
        })

    except Exception as e:
        logger.error(f"Error starting enhanced integrity check: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500



@app.route('/api/data-integrity/check', methods=['POST'])
def manual_data_integrity_check():
    """Manual data integrity check triggered by user"""
    try:
        logger.info("👤 Manual data integrity check requested by user")

        from data_integrity_checker import DataIntegrityChecker
        integrity_checker = DataIntegrityChecker()

        # Check today's data and fill missing intervals
        today = datetime.now().date()
        logger.info(f"📊 Checking data integrity for ALL 222 symbols on {today}...")

        result = integrity_checker.check_and_fill_missing_data(today, startup_mode=False)

        if result.get('success', False):
            logger.info(f"✅ Manual Data Integrity: {result.get('filled', 0)} intervals filled successfully")
            return jsonify({
                "success": True,
                "message": "Data integrity check completed successfully",
                "filled": result.get('filled', 0),
                "total_checked": result.get('total_checked', 0)
            })
        else:
            logger.warning(f"⚠️ Manual Data Integrity: Issues detected - {result.get('error', 'Unknown error')}")
            return jsonify({
                "success": False,
                "error": result.get('error', 'Unknown error'),
                "filled": result.get('filled', 0)
            })

    except Exception as e:
        logger.error(f"❌ Manual Data Integrity: Check failed - {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/pending-signals', methods=['GET'])
def get_pending_signals():
    """Get pending trading signals awaiting manual confirmation"""
    try:
        signals = []
        for signal in trading_engine.pending_signals:
            signals.append({
                'symbol': signal['symbol'],
                'signal_type': signal['signal_type'],
                'price': signal['price'],
                'reason': signal['reason'],
                'timestamp': signal['timestamp'].strftime('%Y-%m-%d %H:%M:%S'),
                'status': signal['status']
            })

        return jsonify({
            "success": True,
            "signals": signals,
            "count": len(signals)
        })

    except Exception as e:
        logger.error(f"Error getting pending signals: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/manual-trade/execute', methods=['POST'])
def execute_manual_trade():
    """Execute manual BUY/SELL trade with live price confirmation"""
    try:
        data = request.get_json()
        symbol = data.get('symbol')
        action = data.get('action')  # 'BUY' or 'SELL'
        user_confirmed_price = data.get('confirmed_price')

        if not all([symbol, action]):
            return jsonify({"success": False, "error": "Missing required parameters"}), 400

        logger.info(f"👤 Manual {action} trade requested: {symbol}")

        # Get LIVE price from API for confirmation
        symbols_with_tokens = symbol_manager.get_symbols_with_tokens()

        # Find token for the symbol
        token = None
        for sym, tok in symbols_with_tokens:
            if sym.upper() == symbol.upper():
                token = tok
                break

        if not token:
            return jsonify({
                "success": False,
                "error": f"Token not found for symbol {symbol}"
            }), 400

        # Get live price
        if not realtime_fetcher.smart_api:
            if not realtime_fetcher.connect_to_api():
                return jsonify({
                    "success": False,
                    "error": "Failed to connect to API"
                }), 500

        # Fetch current price
        import datetime
        from config import DATA_FETCH_CONFIG

        now = datetime.datetime.now()
        from_time = now - datetime.timedelta(minutes=30)

        from_date = from_time.strftime("%Y-%m-%d %H:%M")
        to_date = now.strftime("%Y-%m-%d %H:%M")

        params = {
            "exchange": DATA_FETCH_CONFIG["exchange"],
            "symboltoken": token,
            "interval": DATA_FETCH_CONFIG["interval"],
            "fromdate": from_date,
            "todate": to_date
        }

        logger.info(f"📡 Fetching LIVE price for manual {action}: {symbol}")
        response = realtime_fetcher.smart_api.getCandleData(params)

        if not (response and response.get('status') and response.get('data')):
            return jsonify({
                "success": False,
                "error": f"Failed to get live price for {symbol}"
            }), 500

        candles = response['data']
        if not candles:
            return jsonify({
                "success": False,
                "error": f"No live data available for {symbol}"
            }), 500

        # Get latest price
        latest_candle = candles[-1]
        live_price = float(latest_candle[4])  # Close price

        logger.info(f"📊 LIVE price for {symbol}: ₹{live_price:.2f}")

        # Calculate shares and investment for BUY
        if action == 'BUY':
            investment_amount = 100000  # ₹1L per trade
            shares_quantity = int(investment_amount / live_price)
            actual_investment = shares_quantity * live_price

            logger.info(f"💰 BUY calculation: {shares_quantity} shares × ₹{live_price:.2f} = ₹{actual_investment:.2f}")

            # Execute BUY
            result = trading_engine.execute_buy_signal(symbol, live_price)

            if result:
                logger.info(f"✅ Manual BUY executed: {symbol} - {shares_quantity} shares @ ₹{live_price:.2f}")
                return jsonify({
                    "success": True,
                    "message": f"BUY executed successfully",
                    "symbol": symbol,
                    "live_price": live_price,
                    "shares_quantity": shares_quantity,
                    "investment": actual_investment,
                    "action": action,
                    "timestamp": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })

        elif action == 'SELL':
            # Check if position exists
            if symbol not in trading_engine.active_positions:
                return jsonify({
                    "success": False,
                    "error": f"No active position found for {symbol}"
                }), 400

            position = trading_engine.active_positions[symbol]
            if hasattr(position, 'shares_quantity'):
                shares = position.shares_quantity
                buy_price = position.buy_price
                investment = position.investment
            else:
                shares = position.get('shares_quantity', 0)
                buy_price = position.get('buy_price', 0)
                investment = position.get('investment', 0)

            current_value = shares * live_price
            profit = current_value - investment

            logger.info(f"💰 SELL calculation: {shares} shares × ₹{live_price:.2f} = ₹{current_value:.2f} (Profit: ₹{profit:.2f})")

            # Execute SELL
            result = trading_engine.execute_sell_signal(symbol, live_price)

            if result:
                logger.info(f"✅ Manual SELL executed: {symbol} - {shares} shares @ ₹{live_price:.2f}")
                return jsonify({
                    "success": True,
                    "message": f"SELL executed successfully",
                    "symbol": symbol,
                    "live_price": live_price,
                    "shares_quantity": shares,
                    "buy_price": buy_price,
                    "sell_value": current_value,
                    "profit": profit,
                    "action": action,
                    "timestamp": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })

        # Remove from pending signals if it was there
        trading_engine.pending_signals = [s for s in trading_engine.pending_signals if s['symbol'] != symbol]

        return jsonify({"success": False, "error": f"{action} trade execution failed"}), 500

    except Exception as e:
        logger.error(f"Error executing manual trade: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/realtime-price/<symbol>', methods=['GET'])
def get_realtime_price(symbol):
    """Get LIVE real-time price for a symbol from API (not database)"""
    try:
        logger.info(f"👤 Manual request for LIVE price: {symbol}")

        # Get token for the symbol
        symbols_with_tokens = symbol_manager.get_symbols_with_tokens()

        # Find token for the symbol
        token = None
        for sym, tok in symbols_with_tokens:
            if sym.upper() == symbol.upper():
                token = tok
                break

        if not token:
            return jsonify({
                "success": False,
                "error": f"Token not found for symbol {symbol}"
            }), 404

        # Get live data from API
        if not realtime_fetcher.smart_api:
            if not realtime_fetcher.connect_to_api():
                return jsonify({
                    "success": False,
                    "error": "Failed to connect to API"
                }), 500

        # Fetch current candle data
        import datetime
        from config import DATA_FETCH_CONFIG

        now = datetime.datetime.now()
        from_time = now - datetime.timedelta(minutes=30)  # Get last 30 minutes

        from_date = from_time.strftime("%Y-%m-%d %H:%M")
        to_date = now.strftime("%Y-%m-%d %H:%M")

        params = {
            "exchange": DATA_FETCH_CONFIG["exchange"],
            "symboltoken": token,
            "interval": DATA_FETCH_CONFIG["interval"],
            "fromdate": from_date,
            "todate": to_date
        }

        logger.info(f"📡 Fetching LIVE data for {symbol} (token: {token})")
        response = realtime_fetcher.smart_api.getCandleData(params)

        if response and response.get('status') and response.get('data'):
            candles = response['data']
            if candles:
                # Get the latest candle
                latest_candle = candles[-1]
                candle_time_str = latest_candle[0].replace('Z', '+00:00')
                candle_time = datetime.datetime.fromisoformat(candle_time_str)
                candle_time = candle_time.replace(tzinfo=None)

                live_price = float(latest_candle[4])  # Close price

                logger.info(f"✅ LIVE price fetched: {symbol} @ ₹{live_price:.2f}")

                return jsonify({
                    "success": True,
                    "symbol": symbol,
                    "price": live_price,
                    "timestamp": candle_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "source": "LIVE_API",
                    "token": token,
                    "open_price": float(latest_candle[1]),
                    "high_price": float(latest_candle[2]),
                    "low_price": float(latest_candle[3]),
                    "volume": int(latest_candle[5])
                })
            else:
                return jsonify({
                    "success": False,
                    "error": f"No live data available for {symbol}"
                }), 404
        else:
            error_msg = response.get('message', 'Unknown API error') if response else 'No response from API'
            return jsonify({
                "success": False,
                "error": f"API error: {error_msg}"
            }), 500

    except Exception as e:
        logger.error(f"Error getting LIVE price for {symbol}: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/dual-system/status', methods=['GET'])
def get_dual_system_status():
    """Get status of the dual-database system"""
    try:
        status = trading_engine.get_dual_system_status()
        return jsonify({'success': True, 'status': status})

    except Exception as e:
        logger.error(f"Error getting dual system status: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/dual-system/restart', methods=['POST'])
def restart_dual_system():
    """Restart the dual-database system"""
    try:
        # Start dual-database system
        success = trading_engine.start_dual_database_system()

        if success:
            return jsonify({
                'success': True,
                'message': 'Dual-database system restarted successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to restart dual-database system'
            })

    except Exception as e:
        logger.error(f"Error restarting dual system: {e}")
        return jsonify({'success': False, 'error': str(e)})

# WebSocket Events
@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    active_connections.add(request.sid)
    logger.info(f"Client connected: {request.sid}")

    # Send current status to newly connected client
    emit('trading_status', {
        'active': trading_active,
        'message': f'Trading engine is {"active" if trading_active else "inactive"}'
    })
    emit('data_fetch_status', {
        'active': data_fetching_active,
        'message': f'Data fetching is {"active" if data_fetching_active else "inactive"}'
    })

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    active_connections.discard(request.sid)
    logger.info(f"Client disconnected: {request.sid}")

@socketio.on('join_room')
def handle_join_room(data):
    """Handle client joining a room for specific updates"""
    room = data.get('room')
    if room:
        join_room(room)
        emit('status', {'message': f'Joined room: {room}'})

@socketio.on('leave_room')
def handle_leave_room(data):
    """Handle client leaving a room"""
    room = data.get('room')
    if room:
        leave_room(room)
        emit('status', {'message': f'Left room: {room}'})

def broadcast_real_time_update(data_type, data):
    """Broadcast real-time updates to all connected clients"""
    socketio.emit('real_time_update', {
        'type': data_type,
        'data': data,
        'timestamp': datetime.now().isoformat()
    })

def broadcast_trading_signal(signal_data):
    """Broadcast trading signals to all connected clients"""
    socketio.emit('trading_signal', {
        'signal': signal_data,
        'timestamp': datetime.now().isoformat()
    })

def broadcast_log(category, message, log_type='info'):
    """Broadcast log messages to all connected clients"""
    try:
        log_data = {
            'category': category,
            'message': message,
            'type': log_type,
            'timestamp': datetime.now().isoformat()
        }

        socketio.emit('log_message', log_data)

    except Exception as e:
        logger.error(f"❌ Error in broadcast_log: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

def auto_start_systems():
    """Complete system pre-flight check and automatic startup"""
    import threading
    from datetime import datetime, time, timedelta

    def comprehensive_startup_sequence():
        try:
            # Declare global variables at the start
            global realtime_fetcher, data_fetching_active, trading_active

            # Wait a moment for Flask to fully initialize
            import time
            time.sleep(2)

            logger.info("🔍 STARTING COMPREHENSIVE SYSTEM PRE-FLIGHT CHECK...")
            logger.info("=" * 60)

            # 1. SYSTEM VALIDATION
            logger.info("📋 Step 1: System Validation")

            # Check portfolio initialization
            if trading_engine.total_pot > 0:
                logger.info(f"✅ Portfolio: ₹{trading_engine.total_pot/10000000:.2f} Cr ({len(trading_engine.trading_states)} symbols)")
            else:
                logger.error("❌ Portfolio not initialized properly")
                return

            # Check database connection and optimize
            try:
                from database_setup import DatabaseManager
                db = DatabaseManager()
                db.connect()
                db.create_tables()  # Ensure tables exist

                logger.info("✅ Database: Connection successful with SQLAlchemy")
            except Exception as e:
                logger.error(f"❌ Database: Connection failed - {e}")
                return

            # Check API credentials
            try:
                if realtime_fetcher is None:
                    realtime_fetcher = RealtimeDataFetcher()

                if realtime_fetcher.connect_to_api():
                    logger.info("✅ Angel One API: Connection successful")
                else:
                    logger.error("❌ Angel One API: Connection failed")
                    return
            except Exception as e:
                logger.error(f"❌ Angel One API: Error - {e}")
                return

            # 2. MARKET TIMING CHECK
            logger.info("📋 Step 2: Market Timing Check")
            now = datetime.now()
            market_open = now.replace(hour=9, minute=15, second=0, microsecond=0)
            market_close = now.replace(hour=15, minute=30, second=0, microsecond=0)

            # If it's before 9:10 AM, wait until 9:15 AM
            pre_market = now.replace(hour=9, minute=10, second=0, microsecond=0)

            if now < pre_market:
                wait_time = (market_open - now).total_seconds()
                logger.info(f"⏰ Current time: {now.strftime('%H:%M:%S')}")
                logger.info(f"⏰ Market opens at 9:15 AM. Waiting {int(wait_time/60)} minutes...")
                time.sleep(wait_time)
                now = datetime.now()

            logger.info(f"✅ Current time: {now.strftime('%H:%M:%S')}")

            if market_open <= now <= market_close:
                logger.info("✅ Market Status: OPEN - Ready for trading")
            else:
                logger.info("⚠️ Market Status: CLOSED - System will prepare for next trading session")

            # 3. SKIP AUTOMATIC DATA INTEGRITY CHECK (MANUAL ONLY)
            logger.info("📋 Step 3: Data Integrity Check - MANUAL ONLY")
            logger.info("✅ Automatic data integrity check DISABLED")
            logger.info("👤 User will be prompted to check missing data manually via dashboard popup")

            # 4. TRADING POSITIONS CHECK
            logger.info("📋 Step 4: Trading Positions Check")

            try:
                # Check for existing positions
                active_positions = len(trading_engine.active_positions)
                completed_trades = len(trading_engine.completed_trades)

                logger.info(f"✅ Active Positions: {active_positions}")
                logger.info(f"✅ Completed Trades: {completed_trades}")

                if active_positions > 0:
                    logger.info("📊 System will continue monitoring existing positions")
                else:
                    logger.info("📊 No active positions - ready for new trading opportunities")

            except Exception as e:
                logger.error(f"❌ Position Check: Failed - {e}")

            # 5. SYSTEM READINESS CONFIRMATION
            logger.info("📋 Step 5: System Readiness Confirmation")
            logger.info("=" * 60)
            logger.info("🎯 PRE-FLIGHT CHECK COMPLETED SUCCESSFULLY!")
            logger.info("✅ Portfolio: Ready")
            logger.info("✅ Database: Ready")
            logger.info("✅ API Connection: Ready")
            logger.info("✅ Data Integrity: Verified")
            logger.info("✅ Trading Engine: Ready")
            logger.info("=" * 60)

            if market_open <= now <= market_close:
                logger.info("🚀 STARTING AUTOMATIC TRADING SYSTEMS...")

                # 6. COMPREHENSIVE DATA BACKFILL + REALTIME START
                logger.info("🔄 Auto-starting real-time data fetching with backfill...")
                try:
                    if not data_fetching_active:
                        if realtime_fetcher is None:
                            realtime_fetcher = RealtimeDataFetcher()

                        # 🚀 SKIP AUTOMATIC BACKFILL - Let realtime loop handle proper timing
                        logger.info("📊 Skipping automatic backfill - realtime loop will handle proper 15-minute intervals")
                        logger.info("✅ Realtime loop will wait for next proper interval (9:15, 9:30, 9:45, 10:00, 10:15, 10:30...)")

                        # Note: The realtime loop will automatically detect missing intervals
                        # and fetch data at the correct 15-minute boundaries

                        # 🚀 START REALTIME LOOP
                        data_fetching_active = True

                        def fetch_data_background():
                            logger.info("🚀 STARTING REALTIME LOOP...")
                            realtime_fetcher.run_realtime_loop()

                        fetch_thread = threading.Thread(target=fetch_data_background, daemon=True)
                        fetch_thread.start()

                        logger.info("✅ Real-time data fetching started automatically")
                        logger.info("📊 System will fetch data every 15 minutes: 9:15, 9:30, 9:45... till 3:15")

                        # Emit status update
                        socketio.emit('data_fetch_status', {
                            'active': True,
                            'message': 'Data fetching started with automatic backfill'
                        })

                except Exception as e:
                    logger.error(f"❌ Failed to auto-start data fetching: {e}")
                    import traceback
                    logger.error(f"Full traceback: {traceback.format_exc()}")

                # 2. Wait for data integrity check to complete, then start trading
                logger.info("⏳ Data integrity check completed. Starting trading engine...")
                time.sleep(5)  # Brief pause to ensure data is ready

                # 3. Start trading engine automatically (EVENT-DRIVEN MODE)
                logger.info("🎯 Auto-starting trading engine...")
                try:
                    if not trading_active:
                        trading_active = True
                        # Make trading_active accessible as module attribute for data_integrity_checker
                        globals()['trading_active'] = True

                        # Initialize and start confirmation engine
                        trading_engine.initialize_confirmation_engine()
                        if trading_engine.confirmation_engine:
                            trading_engine.confirmation_engine.start_confirmation_engine()

                        # CRITICAL: Set module-level attribute for external access
                        import sys
                        current_module = sys.modules[__name__]
                        setattr(current_module, 'trading_active', True)

                        logger.info(f"🔧 Trading active flag set: {trading_active}")
                        logger.info(f"🔧 Module trading_active: {getattr(current_module, 'trading_active', 'NOT_SET')}")

                        def trading_background():
                            trading_engine.run_trading_loop()

                        trading_thread = threading.Thread(target=trading_background, daemon=True)
                        trading_thread.start()

                        logger.info("✅ Trading engine started automatically in EVENT-DRIVEN mode")
                        logger.info("📊 Trading will ONLY happen after 15-minute data fetch intervals")
                        logger.info("🔄 NO trading during data integrity check - only after real-time fetch")

                        # Emit status update
                        socketio.emit('trading_status', {
                            'active': True,
                            'message': 'Trading engine started - waiting for 15-minute data fetch triggers'
                        })

                except Exception as e:
                    logger.error(f"❌ Failed to auto-start trading engine: {e}")

                logger.info("🎉 AUTOMATIC TRADING SYSTEM STARTUP COMPLETED!")
                logger.info("📈 Data fetching and trading are now running side-by-side")
                logger.info("🔄 System will fetch data every 15 minutes and analyze patterns continuously")
                logger.info("💰 Ready to trade ₹2.22 Cr portfolio with 222 symbols")
                logger.info("=" * 60)

            else:
                if now < market_open:
                    wait_time = (market_open - now).total_seconds()
                    logger.info(f"⏰ Market opens at 9:15 AM. Waiting {int(wait_time/60)} minutes...")
                    logger.info("🔄 Systems will auto-start when market opens")
                else:
                    logger.info("🌙 Market is closed. Systems will auto-start tomorrow at 9:15 AM")
                    logger.info("📊 System is ready and will activate automatically during trading hours")

        except Exception as e:
            logger.error(f"❌ CRITICAL ERROR in startup sequence: {e}")
            logger.error("🚨 System startup failed - please check configuration and restart")

    # Start the comprehensive startup sequence in a background thread
    startup_thread = threading.Thread(target=comprehensive_startup_sequence, daemon=True)
    startup_thread.start()

if __name__ == '__main__':
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)

    logger.info("Starting Flask Trading Application...")

    # Start automatic systems after Flask initialization
    auto_start_systems()

    # Run the Flask app with SocketIO
    socketio.run(
        app,
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=False  # Disable reloader to prevent issues with background threads
    )
