[E 250607 18:16:33 smartConnect:221] Error occurred while making a POST request to https://apiconnect.angelone.in/rest/secure/angelbroking/historical/v1/getCandleData. Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'ac:67:5d:71:1d:52', 'Accept': 'application/json', 'X-PrivateKey': 'BjxCyX7C', 'X-UserType': 'USER', 'X-SourceID': 'WEB', 'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.3N37H2mxnoRVJ9Ix4ENvJw10BROLo7bdHa3pR0Tm1ki_SISsPu5huPHPGZC3hTzaXdcGUj4Vk9tz1rz3NUw3wA'}, Request: {'exchange': 'NSE', 'symboltoken': '1333', 'interval': 'FIFTEEN_MINUTE', 'fromdate': '2025-06-05 14:45', 'todate': '2025-06-05 15:00'}, Response: HTTPSConnectionPool(host='apiconnect.angelone.in', port=443): Read timed out. (read timeout=7)
[E 250607 18:17:36 smartConnect:221] Error occurred while making a POST request to https://apiconnect.angelone.in/rest/secure/angelbroking/historical/v1/getCandleData. Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'ac:67:5d:71:1d:52', 'Accept': 'application/json', 'X-PrivateKey': 'BjxCyX7C', 'X-UserType': 'USER', 'X-SourceID': 'WEB', 'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.3N37H2mxnoRVJ9Ix4ENvJw10BROLo7bdHa3pR0Tm1ki_SISsPu5huPHPGZC3hTzaXdcGUj4Vk9tz1rz3NUw3wA'}, Request: {'exchange': 'NSE', 'symboltoken': '467', 'interval': 'FIFTEEN_MINUTE', 'fromdate': '2025-06-06 11:45', 'todate': '2025-06-06 12:00'}, Response: HTTPSConnectionPool(host='apiconnect.angelone.in', port=443): Read timed out. (read timeout=7)
[E 250607 18:39:28 smartConnect:221] Error occurred while making a POST request to https://apiconnect.angelone.in/rest/secure/angelbroking/historical/v1/getCandleData. Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'ac:67:5d:71:1d:52', 'Accept': 'application/json', 'X-PrivateKey': 'BjxCyX7C', 'X-UserType': 'USER', 'X-SourceID': 'WEB', 'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.3N37H2mxnoRVJ9Ix4ENvJw10BROLo7bdHa3pR0Tm1ki_SISsPu5huPHPGZC3hTzaXdcGUj4Vk9tz1rz3NUw3wA'}, Request: {'exchange': 'NSE', 'symboltoken': '11184', 'interval': 'FIFTEEN_MINUTE', 'fromdate': '2025-06-06 09:30', 'todate': '2025-06-06 09:45'}, Response: HTTPSConnectionPool(host='apiconnect.angelone.in', port=443): Read timed out. (read timeout=7)
[E 250607 18:42:58 smartConnect:221] Error occurred while making a POST request to https://apiconnect.angelone.in/rest/secure/angelbroking/historical/v1/getCandleData. Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'ac:67:5d:71:1d:52', 'Accept': 'application/json', 'X-PrivateKey': 'BjxCyX7C', 'X-UserType': 'USER', 'X-SourceID': 'WEB', 'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.3N37H2mxnoRVJ9Ix4ENvJw10BROLo7bdHa3pR0Tm1ki_SISsPu5huPHPGZC3hTzaXdcGUj4Vk9tz1rz3NUw3wA'}, Request: {'exchange': 'NSE', 'symboltoken': '20607', 'interval': 'FIFTEEN_MINUTE', 'fromdate': '2025-06-06 13:30', 'todate': '2025-06-06 13:45'}, Response: HTTPSConnectionPool(host='apiconnect.angelone.in', port=443): Read timed out. (read timeout=7)
[E 250607 18:44:09 smartConnect:221] Error occurred while making a POST request to https://apiconnect.angelone.in/rest/secure/angelbroking/historical/v1/getCandleData. Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'ac:67:5d:71:1d:52', 'Accept': 'application/json', 'X-PrivateKey': 'BjxCyX7C', 'X-UserType': 'USER', 'X-SourceID': 'WEB', 'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.3N37H2mxnoRVJ9Ix4ENvJw10BROLo7bdHa3pR0Tm1ki_SISsPu5huPHPGZC3hTzaXdcGUj4Vk9tz1rz3NUw3wA'}, Request: {'exchange': 'NSE', 'symboltoken': '15313', 'interval': 'FIFTEEN_MINUTE', 'fromdate': '2025-06-06 14:30', 'todate': '2025-06-06 14:45'}, Response: HTTPSConnectionPool(host='apiconnect.angelone.in', port=443): Read timed out. (read timeout=7)
[E 250607 18:44:48 smartConnect:221] Error occurred while making a POST request to https://apiconnect.angelone.in/rest/secure/angelbroking/historical/v1/getCandleData. Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'ac:67:5d:71:1d:52', 'Accept': 'application/json', 'X-PrivateKey': 'BjxCyX7C', 'X-UserType': 'USER', 'X-SourceID': 'WEB', 'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.3N37H2mxnoRVJ9Ix4ENvJw10BROLo7bdHa3pR0Tm1ki_SISsPu5huPHPGZC3hTzaXdcGUj4Vk9tz1rz3NUw3wA'}, Request: {'exchange': 'NSE', 'symboltoken': '15313', 'interval': 'FIFTEEN_MINUTE', 'fromdate': '2025-06-06 12:45', 'todate': '2025-06-06 13:00'}, Response: HTTPSConnectionPool(host='apiconnect.angelone.in', port=443): Read timed out. (read timeout=7)
