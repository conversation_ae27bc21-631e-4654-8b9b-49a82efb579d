[E 250607 18:16:33 smartConnect:221] Error occurred while making a POST request to https://apiconnect.angelone.in/rest/secure/angelbroking/historical/v1/getCandleData. Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'ac:67:5d:71:1d:52', 'Accept': 'application/json', 'X-PrivateKey': 'BjxCyX7C', 'X-UserType': 'USER', 'X-SourceID': 'WEB', 'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.3N37H2mxnoRVJ9Ix4ENvJw10BROLo7bdHa3pR0Tm1ki_SISsPu5huPHPGZC3hTzaXdcGUj4Vk9tz1rz3NUw3wA'}, Request: {'exchange': 'NSE', 'symboltoken': '1333', 'interval': 'FIFTEEN_MINUTE', 'fromdate': '2025-06-05 14:45', 'todate': '2025-06-05 15:00'}, Response: HTTPSConnectionPool(host='apiconnect.angelone.in', port=443): Read timed out. (read timeout=7)
[E 250607 18:17:36 smartConnect:221] Error occurred while making a POST request to https://apiconnect.angelone.in/rest/secure/angelbroking/historical/v1/getCandleData. Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'ac:67:5d:71:1d:52', 'Accept': 'application/json', 'X-PrivateKey': 'BjxCyX7C', 'X-UserType': 'USER', 'X-SourceID': 'WEB', 'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.3N37H2mxnoRVJ9Ix4ENvJw10BROLo7bdHa3pR0Tm1ki_SISsPu5huPHPGZC3hTzaXdcGUj4Vk9tz1rz3NUw3wA'}, Request: {'exchange': 'NSE', 'symboltoken': '467', 'interval': 'FIFTEEN_MINUTE', 'fromdate': '2025-06-06 11:45', 'todate': '2025-06-06 12:00'}, Response: HTTPSConnectionPool(host='apiconnect.angelone.in', port=443): Read timed out. (read timeout=7)
[E 250607 18:39:28 smartConnect:221] Error occurred while making a POST request to https://apiconnect.angelone.in/rest/secure/angelbroking/historical/v1/getCandleData. Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'ac:67:5d:71:1d:52', 'Accept': 'application/json', 'X-PrivateKey': 'BjxCyX7C', 'X-UserType': 'USER', 'X-SourceID': 'WEB', 'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.3N37H2mxnoRVJ9Ix4ENvJw10BROLo7bdHa3pR0Tm1ki_SISsPu5huPHPGZC3hTzaXdcGUj4Vk9tz1rz3NUw3wA'}, Request: {'exchange': 'NSE', 'symboltoken': '11184', 'interval': 'FIFTEEN_MINUTE', 'fromdate': '2025-06-06 09:30', 'todate': '2025-06-06 09:45'}, Response: HTTPSConnectionPool(host='apiconnect.angelone.in', port=443): Read timed out. (read timeout=7)
[E 250607 18:42:58 smartConnect:221] Error occurred while making a POST request to https://apiconnect.angelone.in/rest/secure/angelbroking/historical/v1/getCandleData. Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'ac:67:5d:71:1d:52', 'Accept': 'application/json', 'X-PrivateKey': 'BjxCyX7C', 'X-UserType': 'USER', 'X-SourceID': 'WEB', 'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.3N37H2mxnoRVJ9Ix4ENvJw10BROLo7bdHa3pR0Tm1ki_SISsPu5huPHPGZC3hTzaXdcGUj4Vk9tz1rz3NUw3wA'}, Request: {'exchange': 'NSE', 'symboltoken': '20607', 'interval': 'FIFTEEN_MINUTE', 'fromdate': '2025-06-06 13:30', 'todate': '2025-06-06 13:45'}, Response: HTTPSConnectionPool(host='apiconnect.angelone.in', port=443): Read timed out. (read timeout=7)
[E 250607 18:44:09 smartConnect:221] Error occurred while making a POST request to https://apiconnect.angelone.in/rest/secure/angelbroking/historical/v1/getCandleData. Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'ac:67:5d:71:1d:52', 'Accept': 'application/json', 'X-PrivateKey': 'BjxCyX7C', 'X-UserType': 'USER', 'X-SourceID': 'WEB', 'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.3N37H2mxnoRVJ9Ix4ENvJw10BROLo7bdHa3pR0Tm1ki_SISsPu5huPHPGZC3hTzaXdcGUj4Vk9tz1rz3NUw3wA'}, Request: {'exchange': 'NSE', 'symboltoken': '15313', 'interval': 'FIFTEEN_MINUTE', 'fromdate': '2025-06-06 14:30', 'todate': '2025-06-06 14:45'}, Response: HTTPSConnectionPool(host='apiconnect.angelone.in', port=443): Read timed out. (read timeout=7)
[E 250607 18:44:48 smartConnect:221] Error occurred while making a POST request to https://apiconnect.angelone.in/rest/secure/angelbroking/historical/v1/getCandleData. Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'ac:67:5d:71:1d:52', 'Accept': 'application/json', 'X-PrivateKey': 'BjxCyX7C', 'X-UserType': 'USER', 'X-SourceID': 'WEB', 'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.3N37H2mxnoRVJ9Ix4ENvJw10BROLo7bdHa3pR0Tm1ki_SISsPu5huPHPGZC3hTzaXdcGUj4Vk9tz1rz3NUw3wA'}, Request: {'exchange': 'NSE', 'symboltoken': '15313', 'interval': 'FIFTEEN_MINUTE', 'fromdate': '2025-06-06 12:45', 'todate': '2025-06-06 13:00'}, Response: HTTPSConnectionPool(host='apiconnect.angelone.in', port=443): Read timed out. (read timeout=7)
[E 250607 19:17:50 smartConnect:221] Error occurred while making a POST request to https://apiconnect.angelone.in/rest/secure/angelbroking/historical/v1/getCandleData. Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'ac:67:5d:71:1d:52', 'Accept': 'application/json', 'X-PrivateKey': 'BjxCyX7C', 'X-UserType': 'USER', 'X-SourceID': 'WEB', 'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZDB-5hPqHl_GbV7AZyN2LO9nZ8SEQgYMP_w4KiPlJF5oPIMT3NvyXOVJPDs59gEW7WjwnDVPIjIhLUNatcf7bQ'}, Request: {'exchange': 'NSE', 'symboltoken': '5385', 'interval': 'FIFTEEN_MINUTE', 'fromdate': '2025-06-06 11:45', 'todate': '2025-06-06 12:00'}, Response: HTTPSConnectionPool(host='apiconnect.angelone.in', port=443): Read timed out. (read timeout=7)
[E 250607 19:18:51 smartConnect:221] Error occurred while making a POST request to https://apiconnect.angelone.in/rest/secure/angelbroking/historical/v1/getCandleData. Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'ac:67:5d:71:1d:52', 'Accept': 'application/json', 'X-PrivateKey': 'BjxCyX7C', 'X-UserType': 'USER', 'X-SourceID': 'WEB', 'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZDB-5hPqHl_GbV7AZyN2LO9nZ8SEQgYMP_w4KiPlJF5oPIMT3NvyXOVJPDs59gEW7WjwnDVPIjIhLUNatcf7bQ'}, Request: {'exchange': 'NSE', 'symboltoken': '17903', 'interval': 'FIFTEEN_MINUTE', 'fromdate': '2025-06-06 14:45', 'todate': '2025-06-06 15:00'}, Response: HTTPSConnectionPool(host='apiconnect.angelone.in', port=443): Read timed out. (read timeout=7)
[E 250607 19:20:03 smartConnect:221] Error occurred while making a POST request to https://apiconnect.angelone.in/rest/secure/angelbroking/historical/v1/getCandleData. Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'ac:67:5d:71:1d:52', 'Accept': 'application/json', 'X-PrivateKey': 'BjxCyX7C', 'X-UserType': 'USER', 'X-SourceID': 'WEB', 'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZDB-5hPqHl_GbV7AZyN2LO9nZ8SEQgYMP_w4KiPlJF5oPIMT3NvyXOVJPDs59gEW7WjwnDVPIjIhLUNatcf7bQ'}, Request: {'exchange': 'NSE', 'symboltoken': '17903', 'interval': 'FIFTEEN_MINUTE', 'fromdate': '2025-06-06 09:15', 'todate': '2025-06-06 09:30'}, Response: HTTPSConnectionPool(host='apiconnect.angelone.in', port=443): Read timed out. (read timeout=7)
[E 250607 19:20:51 smartConnect:221] Error occurred while making a POST request to https://apiconnect.angelone.in/rest/secure/angelbroking/historical/v1/getCandleData. Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'ac:67:5d:71:1d:52', 'Accept': 'application/json', 'X-PrivateKey': 'BjxCyX7C', 'X-UserType': 'USER', 'X-SourceID': 'WEB', 'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZDB-5hPqHl_GbV7AZyN2LO9nZ8SEQgYMP_w4KiPlJF5oPIMT3NvyXOVJPDs59gEW7WjwnDVPIjIhLUNatcf7bQ'}, Request: {'exchange': 'NSE', 'symboltoken': '13587', 'interval': 'FIFTEEN_MINUTE', 'fromdate': '2025-06-06 13:00', 'todate': '2025-06-06 13:15'}, Response: HTTPSConnectionPool(host='apiconnect.angelone.in', port=443): Read timed out. (read timeout=7)
[E 250607 19:24:15 smartConnect:221] Error occurred while making a POST request to https://apiconnect.angelone.in/rest/secure/angelbroking/historical/v1/getCandleData. Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'ac:67:5d:71:1d:52', 'Accept': 'application/json', 'X-PrivateKey': 'BjxCyX7C', 'X-UserType': 'USER', 'X-SourceID': 'WEB', 'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZDB-5hPqHl_GbV7AZyN2LO9nZ8SEQgYMP_w4KiPlJF5oPIMT3NvyXOVJPDs59gEW7WjwnDVPIjIhLUNatcf7bQ'}, Request: {'exchange': 'NSE', 'symboltoken': '3563', 'interval': 'FIFTEEN_MINUTE', 'fromdate': '2025-06-06 11:15', 'todate': '2025-06-06 11:30'}, Response: HTTPSConnectionPool(host='apiconnect.angelone.in', port=443): Read timed out. (read timeout=7)
[E 250607 19:28:13 smartConnect:221] Error occurred while making a POST request to https://apiconnect.angelone.in/rest/secure/angelbroking/historical/v1/getCandleData. Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'ac:67:5d:71:1d:52', 'Accept': 'application/json', 'X-PrivateKey': 'BjxCyX7C', 'X-UserType': 'USER', 'X-SourceID': 'WEB', 'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IO_hVU-9DelUKg5icQqAifxITXfHM7MEZUjxL9a1tSKYkr8VQyAbUEr-Cx9BToOykJCMZC0UEsxKvEx1tv9vtw'}, Request: {'exchange': 'NSE', 'symboltoken': '6066', 'interval': 'FIFTEEN_MINUTE', 'fromdate': '2025-06-06 11:45', 'todate': '2025-06-06 12:00'}, Response: HTTPSConnectionPool(host='apiconnect.angelone.in', port=443): Read timed out. (read timeout=7)
