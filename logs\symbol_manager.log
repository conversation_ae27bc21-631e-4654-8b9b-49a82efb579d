2025-06-09 11:11:27,972 - __main__ - INFO - [INTEGRITY CHECK] Starting data integrity check for 2025-06-09
2025-06-09 11:11:28,184 - symbol_processor - INFO - Loaded 222 symbols from Excel file
2025-06-09 11:11:28,184 - symbol_processor - INFO - Extracted 222 unique symbols
2025-06-09 11:11:28,185 - symbol_processor - INFO - Loaded 223 token mappings
2025-06-09 11:11:28,185 - symbol_processor - INFO - Found tokens for 222 symbols
2025-06-09 11:11:28,237 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:11:28,237 - __main__ - INFO - Fetching missing data for 360ONE at 11:00
2025-06-09 11:11:28,507 - __main__ - INFO - Connected to Angel One API for data integrity check
2025-06-09 11:11:28,607 - __main__ - INFO - [SUCCESS] Filled 360ONE at 11:00: Close=1067.65
2025-06-09 11:11:30,620 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:11:30,636 - __main__ - INFO - Stored 1 missing intervals for 360ONE
2025-06-09 11:11:30,662 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:11:30,662 - __main__ - INFO - Fetching missing data for 3MINDIA at 11:00
2025-06-09 11:11:30,762 - __main__ - INFO - [SUCCESS] Filled 3MINDIA at 11:00: Close=29375.00
2025-06-09 11:11:32,762 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:11:32,771 - __main__ - INFO - Stored 1 missing intervals for 3MINDIA
2025-06-09 11:11:32,793 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:11:32,793 - __main__ - INFO - Fetching missing data for ABB at 11:00
2025-06-09 11:11:32,895 - __main__ - INFO - [SUCCESS] Filled ABB at 11:00: Close=6158.50
2025-06-09 11:11:34,896 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:11:34,920 - __main__ - INFO - Stored 1 missing intervals for ABB
2025-06-09 11:11:34,952 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:11:34,952 - __main__ - INFO - Fetching missing data for ACC at 11:00
2025-06-09 11:11:35,069 - __main__ - INFO - [SUCCESS] Filled ACC at 11:00: Close=1901.70
2025-06-09 11:11:37,072 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:11:37,095 - __main__ - INFO - Stored 1 missing intervals for ACC
2025-06-09 11:11:37,128 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:11:37,128 - __main__ - INFO - Fetching missing data for ACMESOLAR at 11:00
2025-06-09 11:11:37,244 - __main__ - INFO - [SUCCESS] Filled ACMESOLAR at 11:00: Close=250.65
2025-06-09 11:11:39,245 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:11:39,254 - __main__ - INFO - Stored 1 missing intervals for ACMESOLAR
2025-06-09 11:11:39,278 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:11:39,278 - __main__ - INFO - Fetching missing data for AIAENG at 11:00
2025-06-09 11:11:39,384 - __main__ - INFO - [SUCCESS] Filled AIAENG at 11:00: Close=3502.40
2025-06-09 11:11:41,385 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:11:41,394 - __main__ - INFO - Stored 1 missing intervals for AIAENG
2025-06-09 11:11:41,417 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:11:41,417 - __main__ - INFO - Fetching missing data for APLAPOLLO at 11:00
2025-06-09 11:11:41,507 - __main__ - INFO - [SUCCESS] Filled APLAPOLLO at 11:00: Close=1892.50
2025-06-09 11:11:43,508 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:11:43,517 - __main__ - INFO - Stored 1 missing intervals for APLAPOLLO
2025-06-09 11:11:43,540 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:11:43,541 - __main__ - INFO - Fetching missing data for AUBANK at 11:00
2025-06-09 11:11:43,676 - __main__ - INFO - [SUCCESS] Filled AUBANK at 11:00: Close=775.60
2025-06-09 11:11:45,677 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:11:45,686 - __main__ - INFO - Stored 1 missing intervals for AUBANK
2025-06-09 11:11:45,710 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:11:45,710 - __main__ - INFO - Fetching missing data for AWL at 11:00
2025-06-09 11:11:45,833 - __main__ - INFO - [SUCCESS] Filled AWL at 11:00: Close=267.75
2025-06-09 11:11:47,835 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:11:47,850 - __main__ - INFO - Stored 1 missing intervals for AWL
2025-06-09 11:11:47,879 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:11:47,879 - __main__ - INFO - Fetching missing data for AADHARHFC at 11:00
2025-06-09 11:11:47,970 - __main__ - INFO - [SUCCESS] Filled AADHARHFC at 11:00: Close=454.05
2025-06-09 11:11:49,971 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:11:49,979 - __main__ - INFO - Stored 1 missing intervals for AADHARHFC
2025-06-09 11:11:50,001 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:11:50,002 - __main__ - INFO - Fetching missing data for AARTIIND at 11:00
2025-06-09 11:11:50,234 - __main__ - INFO - [SUCCESS] Filled AARTIIND at 11:00: Close=482.40
2025-06-09 11:11:52,236 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:11:52,250 - __main__ - INFO - Stored 1 missing intervals for AARTIIND
2025-06-09 11:11:52,289 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:11:52,289 - __main__ - INFO - Fetching missing data for AAVAS at 11:00
2025-06-09 11:11:52,409 - __main__ - INFO - [SUCCESS] Filled AAVAS at 11:00: Close=1915.20
2025-06-09 11:11:54,410 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:11:54,420 - __main__ - INFO - Stored 1 missing intervals for AAVAS
2025-06-09 11:11:54,442 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:11:54,442 - __main__ - INFO - Fetching missing data for ABBOTINDIA at 11:00
2025-06-09 11:11:54,570 - __main__ - INFO - [SUCCESS] Filled ABBOTINDIA at 11:00: Close=31675.00
2025-06-09 11:11:56,571 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:11:56,580 - __main__ - INFO - Stored 1 missing intervals for ABBOTINDIA
2025-06-09 11:11:56,606 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:11:56,606 - __main__ - INFO - Fetching missing data for ACE at 11:00
2025-06-09 11:11:56,715 - __main__ - INFO - [SUCCESS] Filled ACE at 11:00: Close=1260.10
2025-06-09 11:11:58,716 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:11:58,724 - __main__ - INFO - Stored 1 missing intervals for ACE
2025-06-09 11:11:58,748 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:11:58,748 - __main__ - INFO - Fetching missing data for ADANIENSOL at 11:00
2025-06-09 11:11:58,889 - __main__ - INFO - [SUCCESS] Filled ADANIENSOL at 11:00: Close=901.15
2025-06-09 11:12:00,891 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:00,903 - __main__ - INFO - Stored 1 missing intervals for ADANIENSOL
2025-06-09 11:12:00,927 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:00,927 - __main__ - INFO - Fetching missing data for ADANIENT at 11:00
2025-06-09 11:12:01,046 - __main__ - INFO - [SUCCESS] Filled ADANIENT at 11:00: Close=2561.00
2025-06-09 11:12:03,047 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:03,055 - __main__ - INFO - Stored 1 missing intervals for ADANIENT
2025-06-09 11:12:03,084 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:03,085 - __main__ - INFO - Fetching missing data for ADANIGREEN at 11:00
2025-06-09 11:12:03,211 - __main__ - INFO - [SUCCESS] Filled ADANIGREEN at 11:00: Close=1035.20
2025-06-09 11:12:05,214 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:05,267 - __main__ - INFO - Stored 1 missing intervals for ADANIGREEN
2025-06-09 11:12:05,293 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:05,293 - __main__ - INFO - Fetching missing data for ADANIPORTS at 11:00
2025-06-09 11:12:05,387 - __main__ - INFO - [SUCCESS] Filled ADANIPORTS at 11:00: Close=1471.00
2025-06-09 11:12:07,388 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:07,399 - __main__ - INFO - Stored 1 missing intervals for ADANIPORTS
2025-06-09 11:12:07,421 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:07,422 - __main__ - INFO - Fetching missing data for ADANIPOWER at 11:00
2025-06-09 11:12:07,540 - __main__ - INFO - [SUCCESS] Filled ADANIPOWER at 11:00: Close=564.20
2025-06-09 11:12:09,541 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:09,559 - __main__ - INFO - Stored 1 missing intervals for ADANIPOWER
2025-06-09 11:12:09,610 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:09,610 - __main__ - INFO - Fetching missing data for ATGL at 11:00
2025-06-09 11:12:09,749 - __main__ - INFO - [SUCCESS] Filled ATGL at 11:00: Close=687.30
2025-06-09 11:12:11,750 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:11,774 - __main__ - INFO - Stored 1 missing intervals for ATGL
2025-06-09 11:12:11,803 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:11,803 - __main__ - INFO - Fetching missing data for ABCAPITAL at 11:00
2025-06-09 11:12:11,933 - __main__ - INFO - [SUCCESS] Filled ABCAPITAL at 11:00: Close=241.53
2025-06-09 11:12:13,935 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:13,958 - __main__ - INFO - Stored 1 missing intervals for ABCAPITAL
2025-06-09 11:12:13,985 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:13,985 - __main__ - INFO - Fetching missing data for ASIANPAINT at 11:00
2025-06-09 11:12:14,082 - __main__ - INFO - [SUCCESS] Filled ASIANPAINT at 11:00: Close=2251.40
2025-06-09 11:12:16,083 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:16,091 - __main__ - INFO - Stored 1 missing intervals for ASIANPAINT
2025-06-09 11:12:16,117 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:16,117 - __main__ - INFO - Fetching missing data for ASTERDM at 11:00
2025-06-09 11:12:16,229 - __main__ - INFO - [SUCCESS] Filled ASTERDM at 11:00: Close=571.75
2025-06-09 11:12:18,230 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:18,242 - __main__ - INFO - Stored 1 missing intervals for ASTERDM
2025-06-09 11:12:18,265 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:18,265 - __main__ - INFO - Fetching missing data for ASTRAZEN at 11:00
2025-06-09 11:12:18,370 - __main__ - INFO - [SUCCESS] Filled ASTRAZEN at 11:00: Close=9890.50
2025-06-09 11:12:20,371 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:20,382 - __main__ - INFO - Stored 1 missing intervals for ASTRAZEN
2025-06-09 11:12:20,405 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:20,405 - __main__ - INFO - Fetching missing data for ASTRAL at 11:00
2025-06-09 11:12:20,524 - __main__ - INFO - [SUCCESS] Filled ASTRAL at 11:00: Close=1524.20
2025-06-09 11:12:22,526 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:22,548 - __main__ - INFO - Stored 1 missing intervals for ASTRAL
2025-06-09 11:12:22,575 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:22,575 - __main__ - INFO - Fetching missing data for ATUL at 11:00
2025-06-09 11:12:22,698 - __main__ - INFO - [SUCCESS] Filled ATUL at 11:00: Close=7292.50
2025-06-09 11:12:24,701 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:24,719 - __main__ - INFO - Stored 1 missing intervals for ATUL
2025-06-09 11:12:24,747 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:24,747 - __main__ - INFO - Fetching missing data for AUROPHARMA at 11:00
2025-06-09 11:12:24,855 - __main__ - INFO - [SUCCESS] Filled AUROPHARMA at 11:00: Close=1166.50
2025-06-09 11:12:26,857 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:26,880 - __main__ - INFO - Stored 1 missing intervals for AUROPHARMA
2025-06-09 11:12:26,912 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:26,912 - __main__ - INFO - Fetching missing data for AIIL at 11:00
2025-06-09 11:12:27,003 - __main__ - INFO - [SUCCESS] Filled AIIL at 11:00: Close=2485.30
2025-06-09 11:12:29,004 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:29,013 - __main__ - INFO - Stored 1 missing intervals for AIIL
2025-06-09 11:12:29,037 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:29,037 - __main__ - INFO - Fetching missing data for DMART at 11:00
2025-06-09 11:12:29,167 - __main__ - INFO - [SUCCESS] Filled DMART at 11:00: Close=4213.30
2025-06-09 11:12:31,168 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:31,178 - __main__ - INFO - Stored 1 missing intervals for DMART
2025-06-09 11:12:31,201 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:31,201 - __main__ - INFO - Fetching missing data for AXISBANK at 11:00
2025-06-09 11:12:31,297 - __main__ - INFO - [SUCCESS] Filled AXISBANK at 11:00: Close=1218.30
2025-06-09 11:12:33,299 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:33,309 - __main__ - INFO - Stored 1 missing intervals for AXISBANK
2025-06-09 11:12:33,331 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:33,331 - __main__ - INFO - Fetching missing data for BASF at 11:00
2025-06-09 11:12:33,421 - __main__ - INFO - [SUCCESS] Filled BASF at 11:00: Close=5006.00
2025-06-09 11:12:35,422 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:35,433 - __main__ - INFO - Stored 1 missing intervals for BASF
2025-06-09 11:12:35,455 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:35,456 - __main__ - INFO - Fetching missing data for BEML at 11:00
2025-06-09 11:12:35,565 - __main__ - INFO - [SUCCESS] Filled BEML at 11:00: Close=4339.40
2025-06-09 11:12:37,565 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:37,575 - __main__ - INFO - Stored 1 missing intervals for BEML
2025-06-09 11:12:37,595 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:37,595 - __main__ - INFO - Fetching missing data for BLS at 11:00
2025-06-09 11:12:37,711 - __main__ - INFO - [SUCCESS] Filled BLS at 11:00: Close=384.15
2025-06-09 11:12:39,712 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:39,721 - __main__ - INFO - Stored 1 missing intervals for BLS
2025-06-09 11:12:39,743 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:39,743 - __main__ - INFO - Fetching missing data for BSE at 11:00
2025-06-09 11:12:39,885 - __main__ - INFO - [SUCCESS] Filled BSE at 11:00: Close=2977.20
2025-06-09 11:12:41,887 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:41,896 - __main__ - INFO - Stored 1 missing intervals for BSE
2025-06-09 11:12:41,918 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:41,918 - __main__ - INFO - Fetching missing data for BAJAJ-AUTO at 11:00
2025-06-09 11:12:42,031 - __main__ - INFO - [SUCCESS] Filled BAJAJ-AUTO at 11:00: Close=8634.50
2025-06-09 11:12:44,032 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:44,044 - __main__ - INFO - Stored 1 missing intervals for BAJAJ-AUTO
2025-06-09 11:12:44,065 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:44,065 - __main__ - INFO - Fetching missing data for BAJFINANCE at 11:00
2025-06-09 11:12:44,187 - __main__ - INFO - [SUCCESS] Filled BAJFINANCE at 11:00: Close=9774.50
2025-06-09 11:12:46,189 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:46,198 - __main__ - INFO - Stored 1 missing intervals for BAJFINANCE
2025-06-09 11:12:46,219 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:46,221 - __main__ - INFO - Fetching missing data for BAJAJFINSV at 11:00
2025-06-09 11:12:46,342 - __main__ - INFO - [SUCCESS] Filled BAJAJFINSV at 11:00: Close=2051.90
2025-06-09 11:12:48,343 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:48,351 - __main__ - INFO - Stored 1 missing intervals for BAJAJFINSV
2025-06-09 11:12:48,375 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:48,375 - __main__ - INFO - Fetching missing data for BAJAJHLDNG at 11:00
2025-06-09 11:12:48,517 - __main__ - INFO - [SUCCESS] Filled BAJAJHLDNG at 11:00: Close=14219.00
2025-06-09 11:12:50,518 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:50,527 - __main__ - INFO - Stored 1 missing intervals for BAJAJHLDNG
2025-06-09 11:12:50,548 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:50,548 - __main__ - INFO - Fetching missing data for BAJAJHFL at 11:00
2025-06-09 11:12:50,668 - __main__ - INFO - [SUCCESS] Filled BAJAJHFL at 11:00: Close=127.76
2025-06-09 11:12:52,671 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:52,700 - __main__ - INFO - Stored 1 missing intervals for BAJAJHFL
2025-06-09 11:12:52,730 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:52,730 - __main__ - INFO - Fetching missing data for BALKRISIND at 11:00
2025-06-09 11:12:52,823 - __main__ - INFO - [SUCCESS] Filled BALKRISIND at 11:00: Close=2502.00
2025-06-09 11:12:54,824 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:54,832 - __main__ - INFO - Stored 1 missing intervals for BALKRISIND
2025-06-09 11:12:54,853 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:54,853 - __main__ - INFO - Fetching missing data for BALRAMCHIN at 11:00
2025-06-09 11:12:54,949 - __main__ - INFO - [SUCCESS] Filled BALRAMCHIN at 11:00: Close=615.00
2025-06-09 11:12:56,951 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:56,972 - __main__ - INFO - Stored 1 missing intervals for BALRAMCHIN
2025-06-09 11:12:57,001 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:57,001 - __main__ - INFO - Fetching missing data for BANDHANBNK at 11:00
2025-06-09 11:12:57,115 - __main__ - INFO - [SUCCESS] Filled BANDHANBNK at 11:00: Close=185.00
2025-06-09 11:12:59,116 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:12:59,125 - __main__ - INFO - Stored 1 missing intervals for BANDHANBNK
2025-06-09 11:12:59,149 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:12:59,149 - __main__ - INFO - Fetching missing data for BANKBARODA at 11:00
2025-06-09 11:12:59,252 - __main__ - INFO - [SUCCESS] Filled BANKBARODA at 11:00: Close=249.14
2025-06-09 11:13:01,253 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:13:01,266 - __main__ - INFO - Stored 1 missing intervals for BANKBARODA
2025-06-09 11:13:01,291 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:13:01,291 - __main__ - INFO - Fetching missing data for BANKINDIA at 11:00
2025-06-09 11:13:01,437 - __main__ - INFO - [SUCCESS] Filled BANKINDIA at 11:00: Close=129.14
2025-06-09 11:13:03,438 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:13:03,449 - __main__ - INFO - Stored 1 missing intervals for BANKINDIA
2025-06-09 11:13:03,475 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:13:03,475 - __main__ - INFO - Fetching missing data for MAHABANK at 11:00
2025-06-09 11:13:03,595 - __main__ - INFO - [SUCCESS] Filled MAHABANK at 11:00: Close=57.41
2025-06-09 11:13:05,596 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:13:05,608 - __main__ - INFO - Stored 1 missing intervals for MAHABANK
2025-06-09 11:13:05,630 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:13:05,630 - __main__ - INFO - Fetching missing data for BATAINDIA at 11:00
2025-06-09 11:13:05,766 - __main__ - INFO - [SUCCESS] Filled BATAINDIA at 11:00: Close=1220.60
2025-06-09 11:13:07,767 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:13:07,776 - __main__ - INFO - Stored 1 missing intervals for BATAINDIA
2025-06-09 11:13:07,798 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:13:07,798 - __main__ - INFO - Fetching missing data for BAYERCROP at 11:00
2025-06-09 11:13:07,938 - __main__ - INFO - [SUCCESS] Filled BAYERCROP at 11:00: Close=5449.00
2025-06-09 11:13:09,948 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:13:09,959 - __main__ - INFO - Stored 1 missing intervals for BAYERCROP
2025-06-09 11:13:09,981 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:13:09,981 - __main__ - INFO - Fetching missing data for BERGEPAINT at 11:00
2025-06-09 11:13:10,124 - __main__ - INFO - [SUCCESS] Filled BERGEPAINT at 11:00: Close=571.90
2025-06-09 11:13:12,126 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:13:12,137 - __main__ - INFO - Stored 1 missing intervals for BERGEPAINT
2025-06-09 11:13:12,162 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:13:12,162 - __main__ - INFO - Fetching missing data for BDL at 11:00
2025-06-09 11:13:12,268 - __main__ - INFO - [SUCCESS] Filled BDL at 11:00: Close=1938.80
2025-06-09 11:13:14,270 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:13:14,297 - __main__ - INFO - Stored 1 missing intervals for BDL
2025-06-09 11:13:14,327 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:13:14,327 - __main__ - INFO - Fetching missing data for BEL at 11:00
2025-06-09 11:13:14,437 - __main__ - INFO - [SUCCESS] Filled BEL at 11:00: Close=392.55
2025-06-09 11:13:16,439 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:13:16,448 - __main__ - INFO - Stored 1 missing intervals for BEL
2025-06-09 11:13:16,477 - __main__ - INFO -   Missing: 10:15
2025-06-09 11:13:16,478 - __main__ - INFO -   Missing: 10:30
2025-06-09 11:13:16,478 - __main__ - INFO -   Missing: 10:45
2025-06-09 11:13:16,478 - __main__ - INFO -   ... and 1 more intervals
2025-06-09 11:13:16,478 - __main__ - INFO - Fetching missing data for BHARATFORG at 10:15
2025-06-09 11:13:16,621 - __main__ - INFO - [SUCCESS] Filled BHARATFORG at 10:15: Close=1324.80
2025-06-09 11:13:18,622 - __main__ - INFO - Fetching missing data for BHARATFORG at 10:30
2025-06-09 11:13:18,743 - __main__ - INFO - [SUCCESS] Filled BHARATFORG at 10:30: Close=1329.00
2025-06-09 11:13:20,744 - __main__ - INFO - Fetching missing data for BHARATFORG at 10:45
2025-06-09 11:13:20,876 - __main__ - INFO - [SUCCESS] Filled BHARATFORG at 10:45: Close=1328.00
2025-06-09 11:13:22,878 - __main__ - INFO - Fetching missing data for BHARATFORG at 11:00
2025-06-09 11:13:23,000 - __main__ - INFO - [SUCCESS] Filled BHARATFORG at 11:00: Close=1335.70
2025-06-09 11:13:25,001 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:13:25,010 - __main__ - INFO - Stored 4 missing intervals for BHARATFORG
2025-06-09 11:13:25,035 - __main__ - INFO -   Missing: 10:30
2025-06-09 11:13:25,035 - __main__ - INFO -   Missing: 10:45
2025-06-09 11:13:25,036 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:13:25,036 - __main__ - INFO - Fetching missing data for BHEL at 10:30
2025-06-09 11:13:25,140 - __main__ - INFO - [SUCCESS] Filled BHEL at 10:30: Close=258.60
2025-06-09 11:13:27,142 - __main__ - INFO - Fetching missing data for BHEL at 10:45
2025-06-09 11:13:27,268 - __main__ - INFO - [SUCCESS] Filled BHEL at 10:45: Close=258.05
2025-06-09 11:13:29,269 - __main__ - INFO - Fetching missing data for BHEL at 11:00
2025-06-09 11:13:29,403 - __main__ - INFO - [SUCCESS] Filled BHEL at 11:00: Close=258.15
2025-06-09 11:13:31,405 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:13:31,425 - __main__ - INFO - Stored 3 missing intervals for BHEL
2025-06-09 11:13:31,452 - __main__ - INFO -   Missing: 10:45
2025-06-09 11:13:31,452 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:13:31,452 - __main__ - INFO - Fetching missing data for BPCL at 10:45
2025-06-09 11:13:31,653 - __main__ - INFO - [SUCCESS] Filled BPCL at 10:45: Close=320.10
2025-06-09 11:13:33,654 - __main__ - INFO - Fetching missing data for BPCL at 11:00
2025-06-09 11:13:33,834 - __main__ - INFO - [SUCCESS] Filled BPCL at 11:00: Close=320.35
2025-06-09 11:13:35,836 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:13:35,848 - __main__ - INFO - Stored 2 missing intervals for BPCL
2025-06-09 11:13:35,878 - __main__ - INFO -   Missing: 10:45
2025-06-09 11:13:35,878 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:13:35,878 - __main__ - INFO - Fetching missing data for BHARTIARTL at 10:45
2025-06-09 11:13:35,983 - __main__ - INFO - [SUCCESS] Filled BHARTIARTL at 10:45: Close=1863.90
2025-06-09 11:13:37,985 - __main__ - INFO - Fetching missing data for BHARTIARTL at 11:00
2025-06-09 11:13:38,142 - __main__ - INFO - [SUCCESS] Filled BHARTIARTL at 11:00: Close=1866.10
2025-06-09 11:13:40,144 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:13:40,153 - __main__ - INFO - Stored 2 missing intervals for BHARTIARTL
2025-06-09 11:13:40,176 - __main__ - INFO -   Missing: 10:45
2025-06-09 11:13:40,176 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:13:40,176 - __main__ - INFO - Fetching missing data for BHARTIHEXA at 10:45
2025-06-09 11:13:40,304 - __main__ - INFO - [SUCCESS] Filled BHARTIHEXA at 10:45: Close=1820.20
2025-06-09 11:13:42,305 - __main__ - INFO - Fetching missing data for BHARTIHEXA at 11:00
2025-06-09 11:13:42,433 - __main__ - INFO - [SUCCESS] Filled BHARTIHEXA at 11:00: Close=1813.90
2025-06-09 11:13:44,435 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:13:44,446 - __main__ - INFO - Stored 2 missing intervals for BHARTIHEXA
2025-06-09 11:13:44,470 - __main__ - INFO -   Missing: 10:45
2025-06-09 11:13:44,470 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:13:44,470 - __main__ - INFO - Fetching missing data for BIKAJI at 10:45
2025-06-09 11:13:44,689 - __main__ - INFO - [SUCCESS] Filled BIKAJI at 10:45: Close=760.40
2025-06-09 11:13:46,690 - __main__ - INFO - Fetching missing data for BIKAJI at 11:00
2025-06-09 11:13:46,801 - __main__ - INFO - [SUCCESS] Filled BIKAJI at 11:00: Close=760.05
2025-06-09 11:13:48,803 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:13:48,813 - __main__ - INFO - Stored 2 missing intervals for BIKAJI
2025-06-09 11:13:48,838 - __main__ - INFO -   Missing: 10:45
2025-06-09 11:13:48,838 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:13:48,838 - __main__ - INFO - Fetching missing data for BIOCON at 10:45
2025-06-09 11:13:48,946 - __main__ - INFO - [SUCCESS] Filled BIOCON at 10:45: Close=334.25
2025-06-09 11:13:50,947 - __main__ - INFO - Fetching missing data for BIOCON at 11:00
2025-06-09 11:13:51,079 - __main__ - INFO - [SUCCESS] Filled BIOCON at 11:00: Close=334.00
2025-06-09 11:13:53,080 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:13:53,090 - __main__ - INFO - Stored 2 missing intervals for BIOCON
2025-06-09 11:13:53,113 - __main__ - INFO -   Missing: 10:45
2025-06-09 11:13:53,114 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:13:53,114 - __main__ - INFO - Fetching missing data for BSOFT at 10:45
2025-06-09 11:13:53,228 - __main__ - INFO - [SUCCESS] Filled BSOFT at 10:45: Close=417.00
2025-06-09 11:13:55,229 - __main__ - INFO - Fetching missing data for BSOFT at 11:00
2025-06-09 11:13:55,340 - __main__ - INFO - [SUCCESS] Filled BSOFT at 11:00: Close=417.15
2025-06-09 11:13:57,342 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:13:57,357 - __main__ - INFO - Stored 2 missing intervals for BSOFT
2025-06-09 11:13:57,380 - __main__ - INFO -   Missing: 10:45
2025-06-09 11:13:57,380 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:13:57,380 - __main__ - INFO - Fetching missing data for BLUEDART at 10:45
2025-06-09 11:13:57,481 - __main__ - INFO - [SUCCESS] Filled BLUEDART at 10:45: Close=6518.50
2025-06-09 11:13:59,481 - __main__ - INFO - Fetching missing data for BLUEDART at 11:00
2025-06-09 11:13:59,608 - __main__ - INFO - [SUCCESS] Filled BLUEDART at 11:00: Close=6535.00
2025-06-09 11:14:01,609 - database_setup - INFO - Connected to sqlite database
2025-06-09 11:14:01,621 - __main__ - INFO - Stored 2 missing intervals for BLUEDART
2025-06-09 11:14:01,644 - __main__ - INFO -   Missing: 10:45
2025-06-09 11:14:01,644 - __main__ - INFO -   Missing: 11:00
2025-06-09 11:14:01,644 - __main__ - INFO - Fetching missing data for BLUESTARCO at 10:45
2025-06-09 11:14:01,791 - __main__ - INFO - [SUCCESS] Filled BLUESTARCO at 10:45: Close=1564.50
