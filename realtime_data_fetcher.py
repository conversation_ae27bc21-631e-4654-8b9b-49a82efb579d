"""
Real-time data fetcher - Fetches data every 15 minutes during trading hours
9:15, 9:30, 9:45, 10:00... until 3:15
"""
import datetime
import time
import logging
import pandas as pd
import pyotp
from SmartApi import SmartConnect
from config import Config, DATA_FETCH_CONFIG, ensure_directories
from symbol_manager import SymbolManager
from database_setup import DatabaseManager
from data_integrity_checker import DataIntegrityChecker
from dataclasses import dataclass
from typing import List, Dict, Tuple

@dataclass
class SymbolPriority:
    """Data class for symbol priority information"""
    symbol: str
    token: str
    tier: str  # GOLD, SILVER, BRONZE, REMAINING
    priority_score: float
    pattern_status: str  # FFFFR?, FFFF??, etc.
    position_status: str = None  # SELL-READY, SELL-MONITOR, POSITION, None
    profit_amount: float = 0.0
    last_price: float = 0.0

class RealtimeDataFetcher:
    def __init__(self, config_file: str = "angelone_smartapi_config.json"):
        self.config = Config(config_file)
        self.smart_api = None
        self.symbol_manager = SymbolManager()
        self.db_manager = DatabaseManager()
        self.integrity_checker = DataIntegrityChecker()
        self.logger = self._setup_logging()
        
        # Trading hours configuration
        self.MARKET_OPEN_HOUR = 9
        self.MARKET_OPEN_MINUTE = 15
        self.MARKET_CLOSE_HOUR = 15
        self.MARKET_CLOSE_MINUTE = 15  # Last data point at 3:15 PM
        
        # API rate limit for real-time fetching - ENHANCED CONSERVATIVE
        self.REQUEST_DELAY_SECONDS = 1.0  # Increased to 1 second (1 request per second) to avoid AB1004 errors
        
        ensure_directories()
        
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/realtime_data_fetcher.log'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    def connect_to_api(self) -> bool:
        """Connect to Angel One SmartAPI"""
        try:
            credentials = self.config.api_credentials
            
            # Generate TOTP
            totp = pyotp.TOTP(credentials["totp_secret"])
            totp_code = totp.now()
            
            # Initialize SmartConnect
            self.smart_api = SmartConnect(api_key=credentials["api_key"])
            
            # Login
            data = self.smart_api.generateSession(
                clientCode=credentials["username"],
                password=credentials["password"],
                totp=totp_code
            )
            
            if data and data.get('status'):
                self.logger.info("Connected to Angel One API for real-time fetching")
                return True
            else:
                self.logger.error(f"API connection failed: {data}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error connecting to API: {e}")
            return False
    
    def get_next_15min_interval(self, current_time: datetime.datetime) -> datetime.datetime:
        """Calculate the next MARKET-ALIGNED 15-minute interval (9:15, 9:30, 9:45, 10:00, 10:15, 10:30...)"""

        # Market intervals start at 9:15 and repeat every 15 minutes
        market_start = current_time.replace(
            hour=self.MARKET_OPEN_HOUR,
            minute=self.MARKET_OPEN_MINUTE,
            second=0,
            microsecond=0
        )

        # Calculate how many 15-minute intervals have passed since market open
        time_since_market_open = current_time - market_start
        total_minutes = time_since_market_open.total_seconds() / 60

        # Find the next 15-minute interval
        intervals_passed = int(total_minutes // 15)
        next_interval_minutes = (intervals_passed + 1) * 15

        # Calculate the exact next interval time
        next_interval = market_start + datetime.timedelta(minutes=next_interval_minutes)

        # If we're exactly on an interval, return the current time
        if abs((current_time - next_interval + datetime.timedelta(minutes=15)).total_seconds()) < 60:
            return current_time.replace(second=0, microsecond=0)

        return next_interval

    def get_missing_intervals_for_today(self, current_time: datetime.datetime) -> list:
        """Get list of missing 15-minute intervals for today that should have been fetched"""
        missing_intervals = []

        # Market start time for today
        market_start = current_time.replace(
            hour=self.MARKET_OPEN_HOUR,
            minute=self.MARKET_OPEN_MINUTE,
            second=0,
            microsecond=0
        )

        # If current time is before market open, no intervals to fetch
        if current_time < market_start:
            return missing_intervals

        # Generate all 15-minute intervals from market start until current time
        interval_time = market_start
        while interval_time <= current_time:
            # Check if this interval has already passed
            if interval_time < current_time:
                missing_intervals.append(interval_time)
            interval_time += datetime.timedelta(minutes=15)

        return missing_intervals
    
    def is_trading_hours(self, current_time: datetime.datetime) -> bool:
        """Check if current time is within trading hours"""
        market_open = current_time.replace(
            hour=self.MARKET_OPEN_HOUR, 
            minute=self.MARKET_OPEN_MINUTE, 
            second=0, 
            microsecond=0
        )
        market_close = current_time.replace(
            hour=self.MARKET_CLOSE_HOUR, 
            minute=self.MARKET_CLOSE_MINUTE, 
            second=0, 
            microsecond=0
        )
        
        return market_open <= current_time <= market_close
    
    def fetch_latest_candle_for_symbol(self, symbol: str, token: str, current_time: datetime.datetime) -> dict:
        """Fetch the latest 15-minute candle for a single symbol with proper retry logic for RELIABILITY"""
        max_retries = 3
        # ENHANCED RETRY DELAYS: 5s, 10s, 20s for AB1004 errors
        retry_delays = [5.0, 10.0, 20.0]

        for attempt in range(max_retries):
            try:
                if not self.smart_api:
                    if not self.connect_to_api():
                        return None

                # Get today's market start time
                today = current_time.date()
                market_start = datetime.datetime(today.year, today.month, today.day,
                                               self.MARKET_OPEN_HOUR, self.MARKET_OPEN_MINUTE)

                from_date = market_start.strftime("%Y-%m-%d %H:%M")
                to_date = current_time.strftime("%Y-%m-%d %H:%M")

                # Fetch candle data
                params = {
                    "exchange": DATA_FETCH_CONFIG["exchange"],
                    "symboltoken": token,
                    "interval": DATA_FETCH_CONFIG["interval"],
                    "fromdate": from_date,
                    "todate": to_date
                }

                response = self.smart_api.getCandleData(params)

                if response and response.get('status') and response.get('data'):
                    candles = response['data']
                    if candles:
                        # Get the latest completed candle
                        latest_candle = candles[-1]

                        # Parse timestamp
                        candle_time_str = latest_candle[0].replace('Z', '+00:00')
                        candle_time = datetime.datetime.fromisoformat(candle_time_str)

                        return {
                            "symbol": symbol,
                            "token": token,
                            "exchange": DATA_FETCH_CONFIG["exchange"],
                            "timestamp": candle_time,
                            "open_price": float(latest_candle[1]),
                            "high_price": float(latest_candle[2]),
                            "low_price": float(latest_candle[3]),
                            "close_price": float(latest_candle[4]),
                            "volume": int(latest_candle[5]),
                            "interval_type": DATA_FETCH_CONFIG["interval"]
                        }
                elif response and not response.get('status'):
                    # API returned error
                    error_msg = response.get('message', 'Unknown API error')

                    # ENHANCED: Check for rate limiting and API errors (AB1004)
                    if any(keyword in error_msg.lower() for keyword in ['rate limit', 'too many requests', 'access denied', 'try after sometime', 'ab1004', 'something went wrong', 'please try after sometime']):
                        if attempt < max_retries - 1:
                            # ENHANCED BACKOFF: 5s, 10s, 20s for AB1004 errors
                            backoff_delay = retry_delays[attempt]
                            self.logger.warning(f"🚫 API ERROR/RATE LIMIT for {symbol} (attempt {attempt + 1}). Backing off for {backoff_delay}s... Error: {error_msg}")
                            time.sleep(backoff_delay)
                            continue
                        else:
                            self.logger.error(f"💥 API ERROR/RATE LIMIT EXCEEDED for {symbol} after {max_retries} attempts with delays: {error_msg}")
                            return None
                    else:
                        # Other API error - don't retry
                        self.logger.error(f"API error for {symbol}: {error_msg}")
                        return None

                # No data returned but no error
                if attempt < max_retries - 1:
                    self.logger.warning(f"No data for {symbol} (attempt {attempt + 1}). Retrying...")
                    time.sleep(retry_delays[attempt])
                    continue
                else:
                    return None

            except Exception as e:
                error_msg = str(e).lower()

                # Check if it's a rate limiting exception
                if any(keyword in error_msg for keyword in ['rate limit', 'too many requests', 'access denied', 'ab1004', 'something went wrong']):
                    if attempt < max_retries - 1:
                        # PROPER BACKOFF: 5s, 10s, 30s
                        backoff_delay = retry_delays[attempt]
                        self.logger.warning(f"🚫 RATE LIMIT EXCEPTION for {symbol} (attempt {attempt + 1}). Backing off for {backoff_delay}s...")
                        time.sleep(backoff_delay)
                        continue
                    else:
                        self.logger.error(f"💥 RATE LIMIT EXCEEDED for {symbol} after {max_retries} attempts with proper delays: {e}")
                        return None
                else:
                    # Other exception - don't retry
                    self.logger.error(f"Exception fetching candle for {symbol}: {e}")
                    return None

        return None

    def fetch_all_symbols_parallel(self, current_time: datetime.datetime) -> dict:
        """HIGH-SPEED PARALLEL data fetching (5 symbols at a time)"""
        try:
            # Initialize database
            self.db_manager.connect()
            self.db_manager.create_tables()

            # Get all symbols with tokens
            symbols_with_tokens = self.symbol_manager.get_symbols_with_tokens()

            if not symbols_with_tokens:
                self.logger.error("No symbols with tokens found")
                return {"total": 0, "success": 0, "failed": 0}

            self.logger.info(f"🚀 HIGH-SPEED PARALLEL FETCH: {len(symbols_with_tokens)} symbols")
            self.logger.info(f"⚡ Processing 5 symbols simultaneously for maximum speed...")

            # Use parallel processing for speed
            from concurrent.futures import ThreadPoolExecutor, as_completed
            import threading

            success_count = 0
            failed_count = 0
            collected_data = []
            data_lock = threading.Lock()

            def fetch_single_symbol(symbol_token_pair):
                """Fetch data for a single symbol with RETRY LOGIC"""
                symbol, token = symbol_token_pair
                max_retries = 3
                # RETRY DELAYS: 5s, 10s, 15s for parallel processing
                retry_delays = [5.0, 10.0, 15.0]

                for attempt in range(max_retries):
                    try:
                        # Add proper delay to prevent rate limit hits
                        if attempt > 0:
                            delay = retry_delays[attempt - 1]
                            time.sleep(delay)  # Proper backoff

                        # Fetch data for this symbol
                        candle_data = self.fetch_latest_candle_for_symbol(symbol, token, current_time)

                        if candle_data:
                            return {
                                'symbol': symbol,
                                'success': True,
                                'data': candle_data,
                                'price': candle_data['close_price'],
                                'timestamp': candle_data['timestamp'].strftime('%H:%M'),
                                'attempts': attempt + 1
                            }
                        else:
                            if attempt < max_retries - 1:
                                continue  # Retry
                            return {'symbol': symbol, 'success': False, 'error': 'No data received after retries', 'data': None}

                    except Exception as e:
                        if attempt < max_retries - 1:
                            continue  # Retry
                        return {'symbol': symbol, 'success': False, 'error': f'Failed after {max_retries} attempts: {str(e)}', 'data': None}

            # Process symbols SEQUENTIALLY with proper 0.35s delays (like your reference code)
            self.logger.info(f"📊 Processing {len(symbols_with_tokens)} symbols SEQUENTIALLY with 0.35s delays...")

            # Initialize variables
            all_candle_data = []
            success_count = 0
            failed_count = 0

            for i, (symbol, token) in enumerate(symbols_with_tokens, 1):
                self.logger.info(f"🔄 Processing {i}/{len(symbols_with_tokens)}: {symbol}")

                # Fetch data for this symbol
                candle_data = self.fetch_latest_candle_for_symbol(symbol, token, current_time)

                if candle_data:
                    all_candle_data.append(candle_data)
                    success_count += 1
                    self.logger.info(f"✅ {symbol}: ₹{candle_data['close_price']:.2f} @ {candle_data['timestamp'].strftime('%H:%M')}")

                    # 🚀 INSTANT TRADING ANALYSIS - Store and analyze IMMEDIATELY
                    self.store_and_analyze_single_symbol(candle_data, symbol)

                else:
                    failed_count += 1
                    self.logger.error(f"❌ {symbol}: Failed to fetch data")

                # CRITICAL: Conservative delay between requests (2 req/sec limit)
                if i < len(symbols_with_tokens):  # Don't sleep after the last symbol
                    time.sleep(self.REQUEST_DELAY_SECONDS)  # Use the configured delay

                # Progress update every 50 symbols
                if i % 50 == 0 or i == len(symbols_with_tokens):
                    self.logger.info(f"📈 Progress: {i}/{len(symbols_with_tokens)} ({success_count} ✅, {failed_count} ❌)")

            # Store all collected data in database
            if all_candle_data:
                db_success = self.db_manager.insert_trading_data(all_candle_data)
                if db_success:
                    self.logger.info(f"💾 Stored {len(all_candle_data)} records in database")
                else:
                    self.logger.error("❌ Failed to store data in database")

            self.logger.info(f"🎉 SEQUENTIAL FETCH COMPLETED: {success_count} success, {failed_count} failed")

            # AUTOMATIC RETRY for failed symbols
            if failed_count > 0:
                self.logger.info(f"🔄 RETRYING {failed_count} failed symbols...")
                failed_symbols = []

                # Identify failed symbols
                successful_symbols = {data['symbol'] for data in all_candle_data}
                all_symbols = {pair[0] for pair in symbols_with_tokens}
                failed_symbols = list(all_symbols - successful_symbols)

                if failed_symbols:
                    self.logger.info(f"🔄 Retrying {len(failed_symbols)} failed symbols: {failed_symbols[:5]}...")

                    # Retry failed symbols one by one with longer delays
                    retry_success = 0
                    for symbol in failed_symbols:
                        try:
                            token = next(pair[1] for pair in symbols_with_tokens if pair[0] == symbol)
                            time.sleep(1.0)  # 1 second delay between retries

                            candle_data = self.fetch_latest_candle_for_symbol(symbol, token, current_time)
                            if candle_data:
                                all_candle_data.append(candle_data)
                                retry_success += 1
                                success_count += 1
                                failed_count -= 1
                                self.logger.info(f"✅ RETRY SUCCESS {symbol}: ₹{candle_data['close_price']:.2f}")
                            else:
                                self.logger.error(f"❌ RETRY FAILED {symbol}: No data")

                        except Exception as e:
                            self.logger.error(f"❌ RETRY ERROR {symbol}: {e}")

                    self.logger.info(f"🔄 RETRY COMPLETED: {retry_success} additional symbols recovered")

            # Store all collected data in database (final storage after retries)
            if all_candle_data:
                db_success = self.db_manager.insert_trading_data(all_candle_data)
                if db_success:
                    self.logger.info(f"💾 FINAL STORAGE: {len(all_candle_data)} records in database")
                else:
                    self.logger.error("❌ Failed to store data in database")

            self.logger.info(f"🎉 FINAL RESULT: {success_count} success, {failed_count} failed out of {len(symbols_with_tokens)} total")

            return {
                "total": len(symbols_with_tokens),
                "success": success_count,
                "failed": failed_count
            }

        except Exception as e:
            self.logger.error(f"Error in parallel fetch: {e}")
            return {"total": 0, "success": 0, "failed": 0}

    def fetch_all_symbols_realtime(self, current_time: datetime.datetime) -> dict:
        """Fetch real-time data for all symbols"""
        try:
            # Initialize database
            self.db_manager.connect()
            self.db_manager.create_tables()
            
            # Get all symbols with tokens
            symbols_with_tokens = self.symbol_manager.get_symbols_with_tokens()
            
            if not symbols_with_tokens:
                self.logger.error("No symbols with tokens found")
                return {"total": 0, "success": 0, "failed": 0}
            
            self.logger.info(f"Fetching real-time data for {len(symbols_with_tokens)} symbols")
            self.logger.info(f"Current time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            collected_data = []
            success_count = 0
            failed_count = 0
            
            for i, (symbol, token) in enumerate(symbols_with_tokens, 1):
                self.logger.info(f"Processing {i}/{len(symbols_with_tokens)}: {symbol}")
                
                # Fetch latest candle
                candle_data = self.fetch_latest_candle_for_symbol(symbol, token, current_time)
                
                if candle_data:
                    collected_data.append(candle_data)
                    success_count += 1
                    self.logger.info(f"SUCCESS {symbol}: Close={candle_data['close_price']:.2f} @ {candle_data['timestamp'].strftime('%H:%M')}")
                else:
                    failed_count += 1
                    self.logger.error(f"FAILED {symbol}: No data received")
                
                # Enhanced rate limiting with adaptive delays
                if i < len(symbols_with_tokens):
                    # Check if we're getting rate limited and adjust delay
                    if failed_count > success_count * 0.3:  # If >30% failures, slow down
                        adaptive_delay = self.REQUEST_DELAY_SECONDS * 2
                        self.logger.warning(f"High failure rate detected. Increasing delay to {adaptive_delay}s")
                        time.sleep(adaptive_delay)
                    else:
                        time.sleep(self.REQUEST_DELAY_SECONDS)

                # Progress logging every 50 symbols
                if i % 50 == 0:
                    self.logger.info(f"Progress: {i}/{len(symbols_with_tokens)} symbols processed ({success_count} success, {failed_count} failed)")
            
            # Store all collected data in database
            if collected_data:
                db_success = self.db_manager.insert_trading_data(collected_data)
                if db_success:
                    self.logger.info(f"Stored {len(collected_data)} records in database")
                else:
                    self.logger.error("Failed to store data in database")
            
            result = {
                "total": len(symbols_with_tokens),
                "success": success_count,
                "failed": failed_count,
                "records": len(collected_data),
                "timestamp": current_time
            }

            self.logger.info(f"Real-time fetch completed: {success_count} success, {failed_count} failed")

            # Run data integrity check after fetch
            self.run_integrity_check_after_fetch(current_time)

            return result
            
        except Exception as e:
            self.logger.error(f"Error in fetch_all_symbols_realtime: {e}")
            return {"total": 0, "success": 0, "failed": 0}

    def run_integrity_check_after_fetch(self, current_time: datetime.datetime):
        """Run data integrity check after real-time fetch"""
        try:
            self.logger.info("[INTEGRITY CHECK] Running data integrity check after fetch...")

            # Check data for current trading day
            trading_date = current_time.date()

            # Run integrity check (this will fill missing intervals, skipping complete symbols)
            result = self.integrity_checker.check_and_fill_missing_data(trading_date, skip_complete=True)

            if result.get('filled', 0) > 0:
                self.logger.info(f"[INTEGRITY CHECK] Filled {result['filled']} missing intervals (checked {result.get('checked', 0)}, skipped {result.get('skipped', 0)})")
            else:
                self.logger.info(f"[INTEGRITY CHECK] No missing intervals found (checked {result.get('checked', 0)}, skipped {result.get('skipped', 0)})")

        except Exception as e:
            self.logger.error(f"Error in data integrity check: {e}")

    def run_realtime_loop(self):
        """Main real-time data fetching loop"""
        self.logger.info("Starting real-time data fetching loop")
        self.logger.info(f"Trading hours: {self.MARKET_OPEN_HOUR}:{self.MARKET_OPEN_MINUTE:02d} to {self.MARKET_CLOSE_HOUR}:{self.MARKET_CLOSE_MINUTE:02d}")
        
        while True:
            now = datetime.datetime.now()
            
            # Check if it's a weekday (Monday=0, Sunday=6)
            if now.weekday() >= 5:  # Saturday or Sunday
                self.logger.info("Weekend - markets closed. Waiting until Monday...")
                # Sleep until Monday
                days_until_monday = 7 - now.weekday()
                sleep_seconds = days_until_monday * 24 * 3600
                time.sleep(sleep_seconds)
                continue
            
            # Check if within trading hours
            if not self.is_trading_hours(now):
                if now.hour < self.MARKET_OPEN_HOUR or (now.hour == self.MARKET_OPEN_HOUR and now.minute < self.MARKET_OPEN_MINUTE):
                    # Before market open
                    market_open = now.replace(hour=self.MARKET_OPEN_HOUR, minute=self.MARKET_OPEN_MINUTE, second=0, microsecond=0)
                    wait_seconds = (market_open - now).total_seconds()
                    self.logger.info(f"Market not open yet. Waiting {wait_seconds/60:.1f} minutes until {market_open.strftime('%H:%M')}")
                    time.sleep(wait_seconds + 5)  # Add 5 seconds buffer
                    continue
                else:
                    # After market close
                    self.logger.info("Market closed for today. Waiting until next trading day...")
                    # Sleep until next day's market open
                    next_day = now + datetime.timedelta(days=1)
                    next_market_open = next_day.replace(hour=self.MARKET_OPEN_HOUR, minute=self.MARKET_OPEN_MINUTE, second=0, microsecond=0)
                    wait_seconds = (next_market_open - now).total_seconds()
                    time.sleep(wait_seconds)
                    continue
            
            # Calculate next 15-minute interval
            next_fetch_time = self.get_next_15min_interval(now)

            # ALWAYS wait for the proper 15-minute interval - NEVER fetch immediately
            wait_seconds = (next_fetch_time - now).total_seconds()

            if wait_seconds > 0:
                self.logger.info(f"⏰ Waiting {wait_seconds:.0f} seconds for next 15-min interval: {next_fetch_time.strftime('%H:%M')}")
                self.logger.info(f"🎯 Next fetch will be at proper market interval: {next_fetch_time.strftime('%H:%M')}")
                time.sleep(wait_seconds + 1)  # Add 1 second buffer
                current_fetch_time = datetime.datetime.now()
            else:
                # We're exactly at the interval time
                current_fetch_time = next_fetch_time
                self.logger.info(f"🎯 Perfect timing! Fetching at {current_fetch_time.strftime('%H:%M')}")
            
            # ANALYZE EXISTING DB1 DATA instead of fetching new data
            self.logger.info(f"🎯 ANALYZING EXISTING DB1 DATA FOR TRADING SIGNALS AT {current_fetch_time.strftime('%H:%M')} ===")
            result = self.analyze_db1_for_trading_signals(current_fetch_time)
            
            self.logger.info(f"Fetch completed: {result.get('success', 0)}/{result.get('total', 0)} symbols successful")

            # 🚀 TRIGGER IMMEDIATE TRADING ANALYSIS AFTER DATA FETCH (EVENT-DRIVEN)
            if result.get('success', 0) > 0:
                self.logger.info(f"✅ Data fetch successful for {result.get('success', 0)} symbols - triggering trading analysis...")
                self.trigger_immediate_trading_analysis()
            else:
                self.logger.warning("❌ No successful data fetch - skipping trading analysis")

            # Wait for next interval (15 minutes)
            next_scheduled_time = self.get_next_15min_interval(current_fetch_time) + datetime.timedelta(minutes=15)
            wait_seconds = (next_scheduled_time - datetime.datetime.now()).total_seconds()
            
            if wait_seconds > 0:
                self.logger.info(f"Next fetch scheduled at {next_scheduled_time.strftime('%H:%M')} (waiting {wait_seconds/60:.1f} minutes)")
                time.sleep(wait_seconds)

    def trigger_immediate_trading_analysis(self):
        """Trigger immediate trading analysis after data fetch (EVENT-DRIVEN)"""
        try:
            self.logger.info("⚡ DATA FETCH COMPLETED - TRIGGERING IMMEDIATE TRADING ANALYSIS...")

            # Import here to avoid circular imports
            import flask_app

            # Get the global trading engine instance
            if hasattr(flask_app, 'trading_engine') and flask_app.trading_engine:
                self.logger.info("🎯 Found trading engine - starting immediate analysis...")

                # Check if trading is active - check both module attribute and global variable
                trading_active = False
                if hasattr(flask_app, 'trading_active'):
                    trading_active = flask_app.trading_active
                    self.logger.info(f"🔍 Trading active flag: {trading_active}")

                # Also check globals() for the flag
                if not trading_active and 'trading_active' in flask_app.__dict__:
                    trading_active = flask_app.__dict__['trading_active']
                    self.logger.info(f"🔍 Trading active from globals: {trading_active}")

                if trading_active:
                    # Trigger immediate analysis in a separate thread for speed
                    import threading
                    analysis_thread = threading.Thread(
                        target=flask_app.trading_engine.process_immediate_analysis,
                        daemon=True
                    )
                    analysis_thread.start()
                    self.logger.info("✅ TRADING ANALYSIS TRIGGERED - Processing symbols with fresh data...")
                else:
                    self.logger.warning("⚠️ Trading engine found but trading is not active - skipping analysis")
                    self.logger.warning(f"⚠️ Debug: hasattr(flask_app, 'trading_active'): {hasattr(flask_app, 'trading_active')}")
                    if hasattr(flask_app, 'trading_active'):
                        self.logger.warning(f"⚠️ Debug: flask_app.trading_active value: {flask_app.trading_active}")
            else:
                self.logger.error("❌ Trading engine not available for immediate analysis")

        except Exception as e:
            self.logger.error(f"❌ Error triggering trading analysis: {e}")
            import traceback
            self.logger.error(f"Full traceback: {traceback.format_exc()}")

    def store_and_analyze_single_symbol(self, candle_data: dict, symbol: str):
        """Store single symbol data and analyze INSTANTLY for trading signals"""
        try:
            # Store in database immediately
            db_success = self.db_manager.insert_trading_data([candle_data])
            if not db_success:
                self.logger.error(f"❌ Failed to store {symbol} data")
                return

            # 🚀 INSTANT ANALYSIS - Import trading engine and analyze immediately
            try:
                import flask_app
                if hasattr(flask_app, 'trading_engine') and flask_app.trading_engine:
                    # Get latest 7 candles for this symbol from SQL (4-FALL + 2-RISE STRATEGY)
                    latest_data = flask_app.trading_engine.get_latest_symbol_data(symbol, limit=7)

                    if len(latest_data) >= 6:
                        # DETAILED ANALYSIS LOGGING - Show last 6 data points and price differences
                        flask_app.trading_engine._log_detailed_symbol_analysis_4plus1(symbol, latest_data)

                        # Check for BUY signal (4-FALL + 1-RISE + 0.5% DROP) - SEND TO DB2
                        if flask_app.trading_engine.detect_4fall_1rise_pattern_with_drop(latest_data):
                            latest_price = latest_data[0]['close_price']
                            self.logger.info(f"🎯 BUY SIGNAL DETECTED: {symbol} @ ₹{latest_price:.2f} (FFFFR+0.5%DROP)")
                            self.logger.info(f"📤 SENDING TO DB2 for 2-minute confirmation")

                            # Send signal to DB2 for confirmation
                            flask_app.trading_engine._send_signal_to_db2(symbol, latest_price, 'BUY', 'FFFFR_0.5%_DROP')

                        # Check for SELL signal using hybrid strategy (for active positions)
                        elif symbol in flask_app.trading_engine.active_positions:
                            self.logger.info(f"🔍 CHECKING SELL for {symbol} (found in active_positions)")
                            self.logger.info(f"🔍 Total active positions: {len(flask_app.trading_engine.active_positions)}")
                            self.logger.info(f"🔍 Active symbols: {list(flask_app.trading_engine.active_positions.keys())}")
                            position = flask_app.trading_engine.active_positions[symbol]
                            recent_prices = [data['close_price'] for data in reversed(latest_data)]
                            latest_price = latest_data[0]['close_price']

                            # Calculate holding days (handle both object and dict + datetime format)
                            import datetime
                            if hasattr(position, 'buy_time'):
                                buy_time = position.buy_time
                                buy_price = position.buy_price
                                shares = position.shares_quantity
                                investment = position.investment
                            else:
                                buy_time_raw = position.get('buy_time', datetime.datetime.now())
                                buy_price = position.get('buy_price', latest_price)
                                shares = position.get('shares_quantity', 97)
                                investment = position.get('investment', shares * buy_price)

                                # Handle string datetime conversion
                                if isinstance(buy_time_raw, str):
                                    try:
                                        # Try common datetime formats
                                        for fmt in ['%Y-%m-%d %H:%M:%S.%f', '%Y-%m-%d %H:%M:%S', '%Y-%m-%d']:
                                            try:
                                                buy_time = datetime.datetime.strptime(buy_time_raw, fmt)
                                                break
                                            except ValueError:
                                                continue
                                        else:
                                            # If no format works, use current time
                                            buy_time = datetime.datetime.now()
                                            self.logger.warning(f"⚠️ Could not parse buy_time '{buy_time_raw}' for {symbol}, using current time")
                                    except Exception as e:
                                        buy_time = datetime.datetime.now()
                                        self.logger.warning(f"⚠️ Error parsing buy_time for {symbol}: {e}")
                                else:
                                    buy_time = buy_time_raw

                            holding_days = (datetime.datetime.now() - buy_time).days
                            current_profit = (shares * latest_price) - investment

                            # COMPREHENSIVE SELL ANALYSIS LOGGING
                            self.logger.info(f"🔍 DETAILED SELL ANALYSIS for {symbol}:")
                            self.logger.info(f"   Buy Price: ₹{buy_price:.2f}")
                            self.logger.info(f"   Current Price: ₹{latest_price:.2f}")
                            self.logger.info(f"   Price Change: {((latest_price - buy_price) / buy_price * 100):+.2f}%")
                            self.logger.info(f"   Shares: {shares}")
                            self.logger.info(f"   Investment: ₹{investment:.2f}")
                            self.logger.info(f"   Current Value: ₹{shares * latest_price:.2f}")
                            self.logger.info(f"   Current Profit: ₹{current_profit:.2f}")
                            self.logger.info(f"   Profit %: {(current_profit / investment * 100):+.2f}%")
                            self.logger.info(f"   Holding Days: {holding_days}")
                            self.logger.info(f"   Profit vs ₹800 threshold: {'✅ ABOVE' if current_profit >= 800 else '❌ BELOW'}")

                            # Use simple sell strategy (₹800 profit target)
                            try:
                                should_sell, reason, target_used = flask_app.trading_engine.simple_sell_strategy(
                                    position, latest_price
                                )

                                self.logger.info(f"   🎯 SELL DECISION: {should_sell}")
                                self.logger.info(f"   📋 REASON: {reason}")
                                self.logger.info(f"   🎯 TARGET USED: ₹{target_used}")

                                if should_sell:
                                    self.logger.info(f"🎯 SELL SIGNAL DETECTED: {symbol} @ ₹{latest_price:.2f}")
                                    self.logger.info(f"📋 Reason: {reason}")
                                    self.logger.info(f"👤 MANUAL CONFIRMATION REQUIRED - No automatic execution")

                                    # Store signal for manual review (don't execute automatically)
                                    flask_app.trading_engine.store_pending_signal(symbol, 'SELL', latest_price, reason)

                                else:
                                    self.logger.info(f"📊 HOLDING {symbol}: {reason}")

                            except Exception as strategy_error:
                                self.logger.error(f"❌ SELL STRATEGY ERROR for {symbol}: {strategy_error}")
                                import traceback
                                self.logger.error(f"❌ Traceback: {traceback.format_exc()}")
                    else:
                        self.logger.debug(f"⚠️ {symbol}: Insufficient data ({len(latest_data)}/7 points) for 4-FALL+2-RISE analysis")

            except Exception as e:
                self.logger.error(f"❌ Error in instant analysis for {symbol}: {e}")

        except Exception as e:
            self.logger.error(f"❌ Error in store_and_analyze_single_symbol for {symbol}: {e}")

    def analyze_symbol_patterns_for_priority(self) -> Dict[str, List[SymbolPriority]]:
        """🎯 Analyze all symbols and create GOLD/SILVER/BRONZE priority batches"""
        try:
            import sqlite3
            from collections import defaultdict

            conn = sqlite3.connect('Data/trading_data.db')
            # Optimize SQLite for performance
            conn.execute("PRAGMA cache_size = 10000")
            conn.execute("PRAGMA temp_store = MEMORY")
            conn.execute("PRAGMA journal_mode = WAL")
            cursor = conn.cursor()

            # OPTIMIZED: Get all symbols with recent data (last 3 days for speed)
            cursor.execute("""
                SELECT symbol, timestamp, close_price
                FROM trading_data
                WHERE DATE(timestamp) >= DATE('now', '-3 days')
                ORDER BY symbol, timestamp DESC
                LIMIT 10000
            """)

            all_data = cursor.fetchall()

            # Group by symbol and get last 7 data points per symbol
            symbol_data = defaultdict(list)
            for symbol, timestamp, price in all_data:
                symbol_data[symbol].append((timestamp, float(price)))

            # Keep only the latest 7 data points per symbol
            for symbol in symbol_data:
                symbol_data[symbol] = sorted(symbol_data[symbol], key=lambda x: x[0])[-7:]

            # Get active positions from DB2 (ABSOLUTE HIGHEST PRIORITY)
            try:
                conn_db2 = sqlite3.connect('Data/trading_operations.db')
                cursor_db2 = conn_db2.cursor()
                cursor_db2.execute("SELECT symbol, buy_price, target_price FROM trading_positions WHERE status = 'ACTIVE'")
                active_positions = {row[0]: {'buy_price': row[1], 'target_price': row[2]} for row in cursor_db2.fetchall()}
                conn_db2.close()
            except:
                # Fallback to old method if DB2 not available
                cursor.execute("SELECT symbol, buy_price, target_price FROM trading_positions WHERE status = 'ACTIVE'")
                active_positions = {row[0]: {'buy_price': row[1], 'target_price': row[2]} for row in cursor.fetchall()}

            # Get pending confirmations from rolling windows (HIGH PRIORITY)
            try:
                from rolling_window_monitor import get_rolling_window_manager
                rolling_manager = get_rolling_window_manager()
                pending_confirmations = rolling_manager.get_active_monitors()
            except:
                pending_confirmations = {}

            # Get symbols with tokens
            symbols_with_tokens = self.symbol_manager.get_symbols_with_tokens()
            symbol_token_map = {symbol: token for symbol, token in symbols_with_tokens}

            total_symbols_expected = len(symbols_with_tokens)
            self.logger.info(f"📊 Total symbols with tokens: {total_symbols_expected}")
            self.logger.info(f"📈 Active positions (DB2): {len(active_positions)}")
            self.logger.info(f"🔄 Pending confirmations: {len(pending_confirmations)}")
            self.logger.info(f"✅ ACTIVE TRADES GET ABSOLUTE PRIORITY in both 15-min and 2-min data")

            # Initialize priority batches
            gold_batch = []
            silver_batch = []
            bronze_batch = []
            remaining_batch = []

            # Process ALL symbols with tokens with CORRECTED PRIORITY LOGIC
            for symbol, token in symbols_with_tokens:
                data_points = symbol_data.get(symbol, [])

                # GOLD PRIORITY 1: Pending confirmations (ABSOLUTE HIGHEST)
                if symbol in pending_confirmations:
                    confirmation = pending_confirmations[symbol]
                    gold_batch.append(SymbolPriority(
                        symbol=symbol, token=token, tier='GOLD',
                        priority_score=1000.0, pattern_status=f'PENDING_{confirmation.get("signal_type", "UNKNOWN")}_CONFIRMATION',
                        position_status='CONFIRMING', profit_amount=0.0,
                        last_price=data_points[-1][1] if data_points else 0.0
                    ))
                    continue

                # GOLD PRIORITY 2: Check if symbol has active position
                elif symbol in active_positions:
                    position = active_positions[symbol]
                    # Use last known price or default to buy price
                    current_price = data_points[-1][1] if data_points else position['buy_price']
                    profit = (current_price - position['buy_price']) * 97  # Assuming 97 shares

                    if profit >= 800:  # GOLD: ₹800 profit reached - SELL-READY
                        gold_batch.append(SymbolPriority(
                            symbol=symbol, token=token, tier='GOLD',
                            priority_score=999.0, pattern_status='PROFIT_800_SELL_READY',
                            position_status='SELL-READY', profit_amount=profit,
                            last_price=current_price
                        ))
                        continue
                    else:
                        # GOLD: Active position monitoring (still high priority for DB1 monitoring)
                        gold_batch.append(SymbolPriority(
                            symbol=symbol, token=token, tier='GOLD',
                            priority_score=998.0, pattern_status=f'POSITION_PROFIT_{profit:.0f}',
                            position_status='MONITORING', profit_amount=profit,
                            last_price=current_price
                        ))
                        continue

                # For symbols without positions, check data availability
                if len(data_points) < 7:
                    remaining_batch.append(SymbolPriority(
                        symbol=symbol, token=token, tier='REMAINING',
                        priority_score=0.0, pattern_status=f'INSUFFICIENT_DATA_{len(data_points)}/7'
                    ))
                    continue

                # Sort by timestamp and get last 7 prices
                data_points.sort(key=lambda x: x[0])
                last_7_prices = [point[1] for point in data_points[-7:]]
                current_price = last_7_prices[-1]

                # Calculate trends (6 trends from 7 prices)
                trends = []
                for i in range(1, len(last_7_prices)):
                    diff = last_7_prices[i] - last_7_prices[i-1]
                    if abs(diff) < 0.01:  # Flat threshold
                        trends.append('FLAT')
                    elif diff > 0:
                        trends.append('RISE')
                    else:
                        trends.append('FALL')

                # Create pattern string
                pattern_str = ''.join([t[0] for t in trends])  # FFFFR, FFFF, etc.

                # Apply NEW priority logic as specified by user
                # GOLD: FFFFR (4 Falls + 1 Rise) - Highest priority
                if pattern_str == 'FFFFR':
                    gold_batch.append(SymbolPriority(
                        symbol=symbol, token=token, tier='GOLD',
                        priority_score=95.0, pattern_status='FFFFR_BUY_READY',
                        last_price=current_price
                    ))
                # SILVER: XFFFF (1 Any + 4 Falls) - Medium priority
                elif len(pattern_str) >= 5 and pattern_str[1:5] == 'FFFF':
                    silver_batch.append(SymbolPriority(
                        symbol=symbol, token=token, tier='SILVER',
                        priority_score=75.0, pattern_status='XFFFF_PATTERN',
                        last_price=current_price
                    ))
                # BRONZE: XXFFF (2 Any + 3 Falls) - Low priority
                elif len(pattern_str) >= 5 and pattern_str[2:5] == 'FFF':
                    bronze_batch.append(SymbolPriority(
                        symbol=symbol, token=token, tier='BRONZE',
                        priority_score=50.0, pattern_status='XXFFF_PATTERN',
                        last_price=current_price
                    ))
                else:  # REMAINING: zig-zag patterns or broken patterns
                    remaining_batch.append(SymbolPriority(
                        symbol=symbol, token=token, tier='REMAINING',
                        priority_score=10.0, pattern_status=f'{pattern_str}_ZIGZAG',
                        last_price=current_price
                    ))

            conn.close()

            # Sort each batch by priority score (highest first)
            gold_batch.sort(key=lambda x: x.priority_score, reverse=True)
            silver_batch.sort(key=lambda x: x.priority_score, reverse=True)
            bronze_batch.sort(key=lambda x: x.priority_score, reverse=True)
            remaining_batch.sort(key=lambda x: x.priority_score, reverse=True)

            # Log batch sizes and details
            self.logger.info(f"📊 PRIORITY ANALYSIS RESULTS:")
            self.logger.info(f"🥇 GOLD: {len(gold_batch)} symbols")
            self.logger.info(f"🥈 SILVER: {len(silver_batch)} symbols")
            self.logger.info(f"🥉 BRONZE: {len(bronze_batch)} symbols")
            self.logger.info(f"⚪ REMAINING: {len(remaining_batch)} symbols")

            total_analyzed = len(gold_batch) + len(silver_batch) + len(bronze_batch) + len(remaining_batch)
            self.logger.info(f"📊 Total symbols analyzed: {total_analyzed}/{total_symbols_expected}")

            if total_analyzed != total_symbols_expected:
                self.logger.info(f"🔍 ANALYSIS MISMATCH: Expected {total_symbols_expected}, got {total_analyzed} - Adding missing symbols to REMAINING batch")
                # Log which symbols are missing and add them to REMAINING batch
                all_analyzed_symbols = set()
                for batch in [gold_batch, silver_batch, bronze_batch, remaining_batch]:
                    for sp in batch:
                        all_analyzed_symbols.add(sp.symbol)

                symbols_with_tokens = self.symbol_manager.get_symbols_with_tokens()
                all_symbols = {symbol for symbol, token in symbols_with_tokens}
                missing_symbols = all_symbols - all_analyzed_symbols

                if missing_symbols:
                    self.logger.info(f"🔍 Adding {len(missing_symbols)} missing symbols to REMAINING batch: {list(missing_symbols)}")
                    # Add missing symbols to REMAINING batch
                    for symbol in missing_symbols:
                        token = next((token for s, token in symbols_with_tokens if s == symbol), None)
                        if token:
                            missing_sp = SymbolPriority(
                                symbol=symbol,
                                token=token,
                                priority_score=10.0,  # Low priority for missing symbols
                                pattern_status="DISCOVERY_MODE",
                                last_price=0.0
                            )
                            remaining_batch.append(missing_sp)
                            self.logger.info(f"✅ Added {symbol} to REMAINING batch for data fetching")

            # Return ALL symbols (no limits for now to ensure 222 symbols)
            return {
                'GOLD': gold_batch,      # All GOLD symbols
                'SILVER': silver_batch,  # All SILVER symbols
                'BRONZE': bronze_batch,  # All BRONZE symbols
                'REMAINING': remaining_batch  # All REMAINING symbols
            }

        except Exception as e:
            self.logger.error(f"Error in analyze_symbol_patterns_for_priority: {e}")
            return {'GOLD': [], 'SILVER': [], 'BRONZE': [], 'REMAINING': []}

    def analyze_db1_for_trading_signals(self, current_time: datetime.datetime) -> dict:
        """🎯 ANALYZE EXISTING DB1 DATA for trading signals instead of fetching new data"""
        try:
            self.logger.info("🎯 ANALYZING DB1 DATA FOR TRADING SIGNALS (NO API CALLS)")

            # Get priority batches based on existing DB1 data
            priority_batches = self.analyze_symbol_patterns_for_priority()

            total_symbols = sum(len(batch) for batch in priority_batches.values())
            signals_generated = 0

            # Process each tier for signal generation
            for tier in ['GOLD', 'SILVER', 'BRONZE', 'REMAINING']:
                batch = priority_batches[tier]
                if not batch:
                    continue

                tier_emoji = {'GOLD': '🥇', 'SILVER': '🥈', 'BRONZE': '🥉', 'REMAINING': '⚪'}[tier]
                self.logger.info(f"{tier_emoji} ANALYZING {tier} BATCH: {len(batch)} symbols")

                for symbol_priority in batch:
                    symbol = symbol_priority.symbol

                    # Check for BUY signals (FFFFR pattern)
                    if symbol_priority.pattern_status == 'FFFFR_BUY_READY':
                        self.logger.info(f"🎯 BUY SIGNAL: {symbol} @ ₹{symbol_priority.last_price:.2f} (FFFFR pattern)")
                        signals_generated += 1
                        # Send to DB2 for confirmation
                        self._send_signal_to_db2(symbol, symbol_priority.last_price, 'BUY', 'FFFFR')

                    # Check for SELL signals (active positions with profit)
                    elif symbol_priority.pattern_status == 'PROFIT_800_SELL_READY':
                        self.logger.info(f"🎯 SELL SIGNAL: {symbol} @ ₹{symbol_priority.last_price:.2f} (₹{symbol_priority.profit_amount:.0f} profit)")
                        signals_generated += 1
                        # Send to DB2 for confirmation
                        self._send_signal_to_db2(symbol, symbol_priority.last_price, 'SELL', 'PROFIT_TARGET')

            self.logger.info(f"✅ DB1 ANALYSIS COMPLETE: {total_symbols} symbols analyzed, {signals_generated} signals generated")

            return {
                'total': total_symbols,
                'success': signals_generated,
                'failed': 0,
                'analysis_mode': True
            }

        except Exception as e:
            self.logger.error(f"Error in analyze_db1_for_trading_signals: {e}")
            return {'total': 0, 'success': 0, 'failed': 0}

    def _send_signal_to_db2(self, symbol: str, price: float, signal_type: str, reason: str):
        """Send trading signal to DB2 for confirmation"""
        try:
            # Import here to avoid circular imports
            import flask_app

            if hasattr(flask_app, 'trading_engine') and flask_app.trading_engine:
                if signal_type == 'BUY':
                    flask_app.trading_engine.confirmation_engine.add_buy_signal_for_confirmation(symbol, price)
                    self.logger.info(f"📤 BUY signal sent to DB2: {symbol} @ ₹{price:.2f}")
                elif signal_type == 'SELL':
                    flask_app.trading_engine.confirmation_engine.add_sell_signal_for_confirmation(symbol, price)
                    self.logger.info(f"📤 SELL signal sent to DB2: {symbol} @ ₹{price:.2f}")
            else:
                self.logger.warning(f"⚠️ Trading engine not available - signal not sent: {symbol} {signal_type}")

        except Exception as e:
            self.logger.error(f"Error sending signal to DB2: {e}")

    def fetch_with_priority_queue(self, current_time: datetime.datetime) -> dict:
        """🎯 Fetch data using GOLD/SILVER/BRONZE priority queue strategy with ROCKET SPEED"""
        start_time = time.time()
        try:
            self.logger.info("🚀 STARTING ROCKET-FAST PRIORITY QUEUE DATA FETCH")

            # Initialize database
            self.db_manager.connect()
            self.db_manager.create_tables()

            # Analyze patterns and create priority batches
            self.logger.info("📊 Analyzing symbol patterns for priority batching...")
            priority_batches = self.analyze_symbol_patterns_for_priority()

            # Log batch composition
            self.logger.info(f"🥇 GOLD BATCH: {len(priority_batches['GOLD'])} symbols (CRITICAL)")
            self.logger.info(f"🥈 SILVER BATCH: {len(priority_batches['SILVER'])} symbols (HIGH)")
            self.logger.info(f"🥉 BRONZE BATCH: {len(priority_batches['BRONZE'])} symbols (MEDIUM)")
            self.logger.info(f"⚪ REMAINING BATCH: {len(priority_batches['REMAINING'])} symbols (LOW)")

            # ROCKET-FAST execution delays for priority queue (minimal delays for speed)
            tier_delays = {
                'GOLD': 0.0,    # No delays - ROCKET SPEED for critical signals
                'SILVER': 0.5,  # 0.5-second delay - FAST execution
                'BRONZE': 1.0,  # 1-second delay - MEDIUM speed
                'REMAINING': 2.0  # 2-second delay - SLOWER but still reasonable
            }

            all_candle_data = []
            total_success = 0
            total_failed = 0

            # Execute each tier in priority order with ROCKET-FAST processing
            for tier in ['GOLD', 'SILVER', 'BRONZE', 'REMAINING']:
                batch = priority_batches[tier]
                if not batch:
                    continue

                delay = tier_delays[tier]
                tier_emoji = {'GOLD': '🥇', 'SILVER': '🥈', 'BRONZE': '🥉', 'REMAINING': '⚪'}[tier]

                self.logger.info(f"\n{tier_emoji} EXECUTING {tier} BATCH: {len(batch)} symbols (delay: {delay}s)")

                tier_success = 0
                tier_failed = 0

                # 🚀 ROCKET-FAST EXECUTION: Use parallel processing for GOLD/SILVER tiers
                if tier in ['GOLD', 'SILVER'] and len(batch) > 3:
                    self.logger.info(f"🚀 ROCKET-FAST PARALLEL EXECUTION for {tier} batch")

                    # Use ThreadPoolExecutor for parallel processing of critical symbols
                    from concurrent.futures import ThreadPoolExecutor, as_completed
                    import threading

                    batch_data = []
                    data_lock = threading.Lock()

                    def fetch_symbol_fast(symbol_priority):
                        symbol = symbol_priority.symbol
                        token = symbol_priority.token

                        candle_data = self.fetch_latest_candle_for_symbol(symbol, token, current_time)

                        if candle_data:
                            with data_lock:
                                batch_data.append(candle_data)

                            # 🚀 INSTANT TRADING ANALYSIS for critical tiers
                            self.store_and_analyze_single_symbol(candle_data, symbol)

                            return {'symbol': symbol, 'success': True, 'data': candle_data}
                        else:
                            return {'symbol': symbol, 'success': False, 'data': None}

                    # Execute in parallel (max 5 threads for API rate limits)
                    with ThreadPoolExecutor(max_workers=5) as executor:
                        future_to_symbol = {executor.submit(fetch_symbol_fast, sp): sp for sp in batch}

                        for future in as_completed(future_to_symbol):
                            result = future.result()
                            if result['success']:
                                tier_success += 1
                                total_success += 1
                                self.logger.info(f"    ✅ {result['symbol']}: ₹{result['data']['close_price']:.2f}")
                            else:
                                tier_failed += 1
                                total_failed += 1
                                self.logger.error(f"    ❌ {result['symbol']}: Failed")

                    all_candle_data.extend(batch_data)

                else:
                    # Sequential processing for BRONZE/REMAINING or small batches
                    for i, symbol_priority in enumerate(batch, 1):
                        symbol = symbol_priority.symbol
                        token = symbol_priority.token

                        # Log symbol details
                        if symbol_priority.position_status:
                            self.logger.info(f"  {i:2d}. {symbol} ({symbol_priority.position_status}: ₹{symbol_priority.profit_amount:.0f})")
                        else:
                            self.logger.info(f"  {i:2d}. {symbol} ({symbol_priority.pattern_status})")

                        # Fetch data for this symbol
                        candle_data = self.fetch_latest_candle_for_symbol(symbol, token, current_time)

                        if candle_data:
                            all_candle_data.append(candle_data)
                            tier_success += 1
                            total_success += 1

                            # 🚀 INSTANT TRADING ANALYSIS for GOLD and SILVER tiers
                            if tier in ['GOLD', 'SILVER']:
                                self.store_and_analyze_single_symbol(candle_data, symbol)

                            self.logger.info(f"    ✅ ₹{candle_data['close_price']:.2f} @ {candle_data['timestamp'].strftime('%H:%M')}")
                        else:
                            tier_failed += 1
                            total_failed += 1
                            self.logger.error(f"    ❌ Failed to fetch data")

                        # Apply tier-specific delay (except for last symbol in batch)
                        if i < len(batch) and delay > 0:
                            time.sleep(delay)

                self.logger.info(f"{tier_emoji} {tier} BATCH COMPLETE: {tier_success} ✅, {tier_failed} ❌")

                # CRITICAL: GOLD and SILVER batches must not fail
                if tier in ['GOLD', 'SILVER'] and tier_failed > 0:
                    self.logger.warning(f"⚠️ CRITICAL: {tier_failed} failures in {tier} batch - these are high priority symbols!")

            # Store all collected data in database
            if all_candle_data:
                db_success = self.db_manager.insert_trading_data(all_candle_data)
                if db_success:
                    self.logger.info(f"💾 Stored {len(all_candle_data)} records in database")
                else:
                    self.logger.error("❌ Failed to store data in database")

            # Final summary with execution time
            total_symbols = sum(len(batch) for batch in priority_batches.values())
            execution_time = time.time() - start_time

            self.logger.info(f"\n🎉 ROCKET-FAST PRIORITY QUEUE FETCH COMPLETE:")
            self.logger.info(f"   📊 Total: {total_symbols} symbols")
            self.logger.info(f"   ✅ Success: {total_success}")
            self.logger.info(f"   ❌ Failed: {total_failed}")
            self.logger.info(f"   🎯 Success Rate: {(total_success/total_symbols*100):.1f}%")
            self.logger.info(f"   ⚡ Execution Time: {execution_time:.1f} seconds")
            self.logger.info(f"   🚀 Speed: {total_symbols/execution_time:.1f} symbols/second")

            # Check if we're ready for next 15-min interval
            time_remaining = 900 - execution_time  # 15 minutes = 900 seconds
            if time_remaining > 0:
                self.logger.info(f"   ✅ READY FOR NEXT INTERVAL: {time_remaining:.0f} seconds remaining")
            else:
                self.logger.warning(f"   ⚠️ EXECUTION TOOK TOO LONG: {-time_remaining:.0f} seconds over 15-min limit!")

            return {
                "total": total_symbols,
                "success": total_success,
                "failed": total_failed,
                "batches": {
                    tier: {"total": len(batch), "success": 0, "failed": 0}
                    for tier, batch in priority_batches.items()
                }
            }

        except Exception as e:
            self.logger.error(f"Error in fetch_with_priority_queue: {e}")
            return {"total": 0, "success": 0, "failed": 0}

    def fetch_all_symbols_with_priority_queue(self, current_time: datetime.datetime) -> dict:
        """🚀 ENHANCED: Original parallel fetching + Priority queue optimization"""
        start_time = time.time()
        try:
            self.logger.info("🚀 ENHANCED SEQUENTIAL FETCH WITH PRIORITY QUEUE OPTIMIZATION")

            # Initialize database
            self.db_manager.connect()
            self.db_manager.create_tables()

            # Ensure API connection is established
            if not self.smart_api:
                self.logger.info("🔌 Establishing API connection...")
                if not self.connect_to_api():
                    self.logger.error("❌ Failed to connect to API")
                    return {"total": 0, "success": 0, "failed": 0}

            # Get all symbols with tokens (original flow)
            symbols_with_tokens = self.symbol_manager.get_symbols_with_tokens()
            if not symbols_with_tokens:
                self.logger.error("No symbols with tokens found")
                return {"total": 0, "success": 0, "failed": 0}

            self.logger.info(f"📊 Total symbols to fetch: {len(symbols_with_tokens)}")

            # 🎯 PRIORITY QUEUE ENHANCEMENT: Analyze and prioritize symbols
            self.logger.info("🎯 Analyzing symbol patterns for priority optimization...")
            priority_batches = self.analyze_symbol_patterns_for_priority()

            # Create prioritized symbol list (GOLD → SILVER → BRONZE → REMAINING)
            prioritized_symbols = []
            for tier in ['GOLD', 'SILVER', 'BRONZE', 'REMAINING']:
                batch = priority_batches.get(tier, [])
                if batch:
                    self.logger.info(f"🎯 {tier} batch: {len(batch)} symbols")
                    prioritized_symbols.extend([(sp.symbol, sp.token) for sp in batch])

            # Add any missing symbols (fallback)
            existing_symbols = {symbol for symbol, token in prioritized_symbols}
            for symbol, token in symbols_with_tokens:
                if symbol not in existing_symbols:
                    prioritized_symbols.append((symbol, token))

            self.logger.info(f"📊 Prioritized order: {len(prioritized_symbols)} symbols")

            # 🔄 SEQUENTIAL PROCESSING WITH PRIORITY ORDER (NO PARALLEL API CALLS)
            all_candle_data = []
            total_success = 0
            total_failed = 0

            # Process symbols one by one in priority order
            for i, (symbol, token) in enumerate(prioritized_symbols, 1):
                self.logger.info(f"🔄 Processing {i}/{len(prioritized_symbols)}: {symbol}")

                # Fetch data for this symbol (sequential with proper retry logic)
                candle_data = self.fetch_latest_candle_for_symbol(symbol, token, current_time)

                if candle_data:
                    all_candle_data.append(candle_data)
                    total_success += 1

                    # 🚀 PRIORITY ENHANCEMENT: Instant analysis for first 25 symbols (GOLD/SILVER)
                    if i <= 25:
                        self.store_and_analyze_single_symbol(candle_data, symbol)

                    self.logger.info(f"    ✅ {symbol}: ₹{candle_data['close_price']:.2f}")
                else:
                    total_failed += 1
                    self.logger.error(f"    ❌ {symbol}: Failed to fetch")

                # Brief pause between symbols (respect API rate limits)
                if i < len(prioritized_symbols):
                    time.sleep(0.5)  # 500ms delay between symbols

            # Store all data in database (original logic)
            if all_candle_data:
                db_success = self.db_manager.insert_trading_data(all_candle_data)
                if db_success:
                    self.logger.info(f"💾 Stored {len(all_candle_data)} records in database")
                else:
                    self.logger.error("❌ Failed to store data in database")

            # Performance summary
            execution_time = time.time() - start_time
            total_symbols = len(prioritized_symbols)

            self.logger.info(f"\n🎉 ENHANCED PARALLEL FETCH COMPLETE:")
            self.logger.info(f"   📊 Total: {total_symbols} symbols")
            self.logger.info(f"   ✅ Success: {total_success}")
            self.logger.info(f"   ❌ Failed: {total_failed}")
            self.logger.info(f"   🎯 Success Rate: {(total_success/total_symbols*100):.1f}%")
            self.logger.info(f"   ⚡ Execution Time: {execution_time:.1f} seconds")
            self.logger.info(f"   🚀 Speed: {total_symbols/execution_time:.1f} symbols/second")

            return {
                "total": total_symbols,
                "success": total_success,
                "failed": total_failed
            }

        except Exception as e:
            self.logger.error(f"Error in fetch_all_symbols_with_priority_queue: {e}")
            return {"total": 0, "success": 0, "failed": 0}



def main():
    """Main function for real-time data fetching"""
    print("REAL-TIME DATA FETCHER")
    print("=" * 40)
    print("Fetches data every 15 minutes during trading hours")
    print("9:15, 9:30, 9:45, 10:00... until 3:15")
    print("=" * 40)
    
    fetcher = RealtimeDataFetcher()
    
    try:
        # Show current status
        validation = fetcher.symbol_manager.get_symbol_stats()
        print(f"Symbols ready for real-time fetching: {validation['symbols_with_tokens']}/{validation['total_symbols']}")

        if validation['symbols_with_tokens'] == 0:
            print("No symbols with tokens found! Run token generator first.")
            return
        
        print(f"Starting real-time data fetching loop...")
        print(f"Press Ctrl+C to stop")
        
        # Start real-time loop
        fetcher.run_realtime_loop()
        
    except KeyboardInterrupt:
        print("\nReal-time fetching stopped by user")
    except Exception as e:
        print(f"Error in real-time fetching: {e}")

if __name__ == "__main__":
    main()
