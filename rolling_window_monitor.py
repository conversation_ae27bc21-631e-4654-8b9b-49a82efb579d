#!/usr/bin/env python3
"""
Rolling Window Monitor - Continuous 2-minute Pattern Detection
Implements rolling window mechanism for R+R and F+F confirmations
"""

import time
import threading
import logging
import sqlite3
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Callable
from dataclasses import dataclass
from db1_db2_communicator import TradingSignal

@dataclass
class DataPoint:
    """2-minute data point structure"""
    timestamp: datetime
    price: float
    volume: int
    source: str = "API"

class RollingWindowMonitor:
    """
    Rolling Window Monitor for continuous 2-minute pattern detection
    
    Key Features:
    1. Continuous monitoring (no timeout)
    2. Rolling window mechanism
    3. R+R pattern detection for BUY confirmation
    4. F+F pattern detection for SELL confirmation
    5. Real-time data fetching every 2 minutes
    """
    
    def __init__(self, symbol: str, signal: TradingSignal, on_confirmation_callback: Callable):
        self.symbol = symbol
        self.signal = signal
        self.on_confirmation_callback = on_confirmation_callback
        
        self.logger = logging.getLogger(f"RollingWindow-{symbol}")
        
        # Rolling window data
        self.data_points = []
        self.base_price = signal.price
        self.start_time = datetime.now()
        
        # Control flags
        self.is_active = True
        self.is_confirmed = False
        
        # Monitoring thread
        self.monitor_thread = None
        
        self.logger.info(f"🔄 Rolling Window Monitor initialized for {symbol} "
                        f"({signal.signal_type} @ ₹{signal.price:.2f})")
    
    def start_monitoring(self):
        """Start the rolling window monitoring in a separate thread"""
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.logger.warning(f"⚠️ Monitor already running for {self.symbol}")
            return
        
        self.monitor_thread = threading.Thread(
            target=self._monitor_continuously,
            name=f"RollingWindow-{self.symbol}",
            daemon=True
        )
        self.monitor_thread.start()
        
        self.logger.info(f"🚀 Rolling window monitoring started for {self.symbol}")
    
    def stop_monitoring(self):
        """Stop the rolling window monitoring"""
        self.is_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        self.logger.info(f"🛑 Rolling window monitoring stopped for {self.symbol}")
    
    def _monitor_continuously(self):
        """
        Main monitoring loop - runs continuously until pattern confirmed
        NO TIMEOUT - keeps rolling until R+R or F+F pattern detected
        """
        self.logger.info(f"🔍 Starting continuous monitoring for {self.signal.signal_type} confirmation...")
        
        consecutive_failures = 0
        max_failures = 5
        
        while self.is_active and not self.is_confirmed:
            try:
                # Fetch latest 2-minute data point
                data_point = self._fetch_2min_data()
                
                if data_point:
                    # Add to rolling window
                    self.data_points.append(data_point)
                    consecutive_failures = 0
                    
                    # Keep only last 3 data points for pattern detection
                    if len(self.data_points) > 3:
                        self.data_points = self.data_points[-3:]
                    
                    self.logger.info(f"📊 {self.symbol}: New data point ₹{data_point.price:.2f} "
                                   f"(Window size: {len(self.data_points)})")
                    
                    # Check for confirmation pattern
                    if len(self.data_points) >= 2:
                        if self.signal.signal_type == 'BUY':
                            if self._detect_rise_rise_pattern():
                                self._confirm_signal()
                                break
                        
                        elif self.signal.signal_type == 'SELL':
                            if self._detect_fall_fall_pattern():
                                self._confirm_signal()
                                break
                    
                    # Log current window status
                    self._log_window_status()
                
                else:
                    consecutive_failures += 1
                    self.logger.warning(f"⚠️ Failed to fetch data for {self.symbol} "
                                      f"(Failures: {consecutive_failures}/{max_failures})")
                    
                    if consecutive_failures >= max_failures:
                        self.logger.error(f"❌ Too many data fetch failures for {self.symbol}. "
                                        f"Stopping monitor.")
                        break
                
                # Wait 2 minutes for next data point
                self.logger.info(f"⏰ Waiting 2 minutes for next data point...")
                time.sleep(120)  # 2 minutes
                
            except Exception as e:
                self.logger.error(f"❌ Error in rolling window monitoring: {e}")
                time.sleep(30)  # Wait 30 seconds before retrying
        
        if not self.is_confirmed:
            self.logger.warning(f"⚠️ Rolling window monitoring ended without confirmation for {self.symbol}")
    
    def _fetch_2min_data(self) -> Optional[DataPoint]:
        """
        Fetch latest 2-minute data point for the symbol
        In production, this would call Angel One API for 2-minute data
        For now, we'll simulate with latest 15-minute data
        """
        try:
            # TODO: Replace with actual 2-minute API call
            # For now, use latest 15-minute data as simulation
            conn = sqlite3.connect('Data/trading_data.db')
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT close_price, timestamp, volume
            FROM trading_data 
            WHERE symbol = ? 
            ORDER BY timestamp DESC 
            LIMIT 1
            ''', (self.symbol,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                price, timestamp_str, volume = result
                
                # Add small random variation to simulate 2-minute movement
                import random
                price_variation = random.uniform(-0.5, 0.5)  # ±0.5% variation
                simulated_price = price * (1 + price_variation / 100)
                
                return DataPoint(
                    timestamp=datetime.now(),
                    price=simulated_price,
                    volume=volume or 1000,
                    source="SIMULATED_2MIN"
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Error fetching 2-minute data: {e}")
            return None
    
    def _detect_rise_rise_pattern(self) -> bool:
        """
        Detect R+R pattern for BUY confirmation
        Requires 2 consecutive RISE movements from base price
        """
        try:
            if len(self.data_points) < 2:
                return False
            
            # Get last 2 data points
            point1 = self.data_points[-2]
            point2 = self.data_points[-1]
            
            # Check for R+R pattern
            rise1 = point1.price > self.base_price
            rise2 = point2.price > point1.price
            
            if rise1 and rise2:
                rise1_pct = ((point1.price - self.base_price) / self.base_price) * 100
                rise2_pct = ((point2.price - point1.price) / point1.price) * 100
                
                self.logger.info(f"✅ R+R PATTERN DETECTED for {self.symbol}!")
                self.logger.info(f"   Base: ₹{self.base_price:.2f}")
                self.logger.info(f"   Rise 1: ₹{point1.price:.2f} (+{rise1_pct:.2f}%)")
                self.logger.info(f"   Rise 2: ₹{point2.price:.2f} (+{rise2_pct:.2f}%)")
                
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Error detecting R+R pattern: {e}")
            return False
    
    def _detect_fall_fall_pattern(self) -> bool:
        """
        Detect F+F pattern for SELL confirmation
        Requires 2 consecutive FALL movements from base price
        """
        try:
            if len(self.data_points) < 2:
                return False
            
            # Get last 2 data points
            point1 = self.data_points[-2]
            point2 = self.data_points[-1]
            
            # Check for F+F pattern
            fall1 = point1.price < self.base_price
            fall2 = point2.price < point1.price
            
            if fall1 and fall2:
                fall1_pct = ((self.base_price - point1.price) / self.base_price) * 100
                fall2_pct = ((point1.price - point2.price) / point1.price) * 100
                
                self.logger.info(f"✅ F+F PATTERN DETECTED for {self.symbol}!")
                self.logger.info(f"   Base: ₹{self.base_price:.2f}")
                self.logger.info(f"   Fall 1: ₹{point1.price:.2f} (-{fall1_pct:.2f}%)")
                self.logger.info(f"   Fall 2: ₹{point2.price:.2f} (-{fall2_pct:.2f}%)")
                
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Error detecting F+F pattern: {e}")
            return False
    
    def _confirm_signal(self):
        """Confirm the signal and trigger execution"""
        try:
            self.is_confirmed = True
            self.is_active = False
            
            confirmation_price = self.data_points[-1].price
            confirmation_time = datetime.now()
            
            self.logger.info(f"🎯 SIGNAL CONFIRMED: {self.signal.signal_type} {self.symbol} "
                           f"@ ₹{confirmation_price:.2f}")
            
            # Call the confirmation callback
            self.on_confirmation_callback(
                symbol=self.symbol,
                signal=self.signal,
                confirmation_price=confirmation_price,
                confirmation_time=confirmation_time,
                data_points=self.data_points.copy()
            )
            
        except Exception as e:
            self.logger.error(f"❌ Error confirming signal: {e}")
    
    def _log_window_status(self):
        """Log current rolling window status"""
        if len(self.data_points) >= 2:
            latest = self.data_points[-1]
            previous = self.data_points[-2]
            
            trend = "RISE" if latest.price > previous.price else "FALL"
            change_pct = ((latest.price - previous.price) / previous.price) * 100
            
            self.logger.info(f"📈 Window Status: {trend} {change_pct:+.2f}% "
                           f"(₹{previous.price:.2f} → ₹{latest.price:.2f})")
    
    def get_status(self) -> Dict[str, any]:
        """Get current status of the rolling window monitor"""
        return {
            'symbol': self.symbol,
            'signal_type': self.signal.signal_type,
            'base_price': self.base_price,
            'is_active': self.is_active,
            'is_confirmed': self.is_confirmed,
            'data_points_count': len(self.data_points),
            'start_time': self.start_time.isoformat(),
            'latest_price': self.data_points[-1].price if self.data_points else None,
            'monitoring_duration_minutes': (datetime.now() - self.start_time).total_seconds() / 60
        }

class RollingWindowManager:
    """
    Manager for multiple rolling window monitors
    Handles creation, monitoring, and cleanup of rolling windows
    """
    
    def __init__(self):
        self.logger = logging.getLogger("RollingWindowManager")
        self.active_monitors = {}
        
        self.logger.info("✅ Rolling Window Manager initialized")
    
    def start_monitor(self, symbol: str, signal: TradingSignal, on_confirmation_callback: Callable) -> bool:
        """Start a new rolling window monitor for a symbol"""
        try:
            if symbol in self.active_monitors:
                self.logger.warning(f"⚠️ Monitor already exists for {symbol}. Stopping old monitor.")
                self.stop_monitor(symbol)
            
            monitor = RollingWindowMonitor(symbol, signal, on_confirmation_callback)
            self.active_monitors[symbol] = monitor
            monitor.start_monitoring()
            
            self.logger.info(f"🚀 Started rolling window monitor for {symbol}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error starting monitor for {symbol}: {e}")
            return False
    
    def stop_monitor(self, symbol: str) -> bool:
        """Stop rolling window monitor for a symbol"""
        try:
            if symbol in self.active_monitors:
                monitor = self.active_monitors[symbol]
                monitor.stop_monitoring()
                del self.active_monitors[symbol]
                
                self.logger.info(f"🛑 Stopped rolling window monitor for {symbol}")
                return True
            else:
                self.logger.warning(f"⚠️ No active monitor found for {symbol}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error stopping monitor for {symbol}: {e}")
            return False
    
    def get_active_monitors(self) -> Dict[str, Dict]:
        """Get status of all active monitors"""
        return {
            symbol: monitor.get_status() 
            for symbol, monitor in self.active_monitors.items()
        }
    
    def cleanup_completed_monitors(self):
        """Clean up monitors that have completed confirmation"""
        completed_symbols = []
        
        for symbol, monitor in self.active_monitors.items():
            if not monitor.is_active or monitor.is_confirmed:
                completed_symbols.append(symbol)
        
        for symbol in completed_symbols:
            self.stop_monitor(symbol)
        
        if completed_symbols:
            self.logger.info(f"🧹 Cleaned up {len(completed_symbols)} completed monitors")

# Global instance
rolling_window_manager = RollingWindowManager()

def get_rolling_window_manager() -> RollingWindowManager:
    """Get the global rolling window manager instance"""
    return rolling_window_manager
