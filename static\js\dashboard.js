/**
 * Advanced Trading System Dashboard JavaScript
 * Real-time WebSocket communication and UI management
 */

class TradingDashboard {
    constructor() {

        this.socket = null;
        this.symbols = [];
        this.activePositions = [];
        this.logs = [];

        this.init();
    }

    init() {

        this.initializeSocket();
        this.initializeEventListeners();
        this.loadInitialData();

        // Auto-refresh data every 30 seconds
        setInterval(() => {
            this.refreshPortfolioData();
        }, 30000);

        // Auto-refresh paper trading data every 15 seconds
        setInterval(() => {
            this.refreshPaperTradingIfVisible();
        }, 15000);

        // Auto-refresh priority queue status every 10 seconds
        setInterval(() => {
            this.loadPriorityQueueStatus();
            this.loadConfirmationStatus();
        }, 10000);

        // Load confirmation status on initial load
        this.loadConfirmationStatus();

        // Initialize new features
        this.initializeNewFeatures();
    }

    initializeSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            this.updateConnectionStatus(true);
            this.addLog('SYSTEM', 'Connected to trading server', 'success');
        });

        this.socket.on('disconnect', () => {
            this.updateConnectionStatus(false);
            this.addLog('SYSTEM', 'Disconnected from trading server', 'danger');
        });

        this.socket.on('trading_status', (data) => {
            this.updateTradingStatus(data.active);
            this.addLog('TRADING', data.message || 'Trading status updated', data.active ? 'success' : 'warning');
        });

        this.socket.on('data_fetch_status', (data) => {
            this.updateDataFetchStatus(data.active);
            this.addLog('DATA', data.message || 'Data fetch status updated', data.active ? 'success' : 'warning');
        });

        this.socket.on('real_time_update', (data) => {
            this.handleRealTimeUpdate(data);
        });

        this.socket.on('integrity_check_complete', (data) => {
            this.handleIntegrityCheckComplete(data);
        });

        this.socket.on('integrity_check_progress', (data) => {
            this.handleIntegrityCheckProgress(data);
        });

        this.socket.on('priority_fetch_complete', (data) => {
            this.handlePriorityFetchComplete(data);
        });

        this.socket.on('trading_signal', (data) => {
            this.handleTradingSignal(data);
        });

        this.socket.on('log_message', (data) => {
            this.addLog(data.category, data.message, data.type);
        });
    }

    initializeEventListeners() {
        // Trading control buttons
        document.getElementById('startTradingBtn').addEventListener('click', () => {
            this.startTrading();
        });

        document.getElementById('stopTradingBtn').addEventListener('click', () => {
            this.stopTrading();
        });

        document.getElementById('startDataFetchBtn').addEventListener('click', () => {
            this.startDataFetch();
        });

        // Symbol search
        document.getElementById('symbolSearch').addEventListener('input', (e) => {
            this.filterSymbols(e.target.value);
        });

        // Tab switching
        document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
            tab.addEventListener('shown.bs.tab', (e) => {
                const target = e.target.getAttribute('data-bs-target');
                if (target === '#symbols') {
                    this.loadSymbols();
                } else if (target === '#positions') {
                    this.loadActivePositions();
                } else if (target === '#sql') {
                    this.initializeSqlTab();
                } else if (target === '#data-logs') {
                    this.initializeDataLogsTab();
                }
            });
        });

        // SQL Query tab event listeners
        document.getElementById('executeQueryBtn').addEventListener('click', () => {
            this.executeQuery();
        });

        document.getElementById('clearQueryBtn').addEventListener('click', () => {
            document.getElementById('sqlQuery').value = '';
            this.clearQueryResults();
        });

        document.getElementById('formatQueryBtn').addEventListener('click', () => {
            this.formatQuery();
        });

        // Symbol Explorer tab event listeners
        document.getElementById('refreshSymbolDataBtn').addEventListener('click', () => {
            this.refreshSymbolExplorer();
        });

        document.getElementById('dataSourceSelect').addEventListener('change', () => {
            this.loadSymbolData();
        });

        document.getElementById('symbolSelect').addEventListener('change', () => {
            this.loadSymbolData();
        });

        document.getElementById('symbolSearchInput').addEventListener('input', (e) => {
            this.filterSymbolList(e.target.value);
        });

        document.getElementById('runDataIntegrityBtn').addEventListener('click', () => {
            this.runDataIntegrityCheck();
        });

        document.getElementById('clearDatabasesBtn').addEventListener('click', () => {
            this.clearAllDatabases();
        });

        document.getElementById('checkDuplicatesBtn').addEventListener('click', () => {
            this.checkDuplicates();
        });

        document.getElementById('queryTemplates').addEventListener('change', (e) => {
            this.loadQueryTemplate(e.target.value);
        });

        document.getElementById('exportCsvBtn').addEventListener('click', () => {
            this.exportResultsToCsv();
        });
    }



    async loadInitialData() {
        await this.refreshPortfolioData();
        await this.loadSymbols();
        await this.loadPriorityQueueStatus();
    }

    showDataIntegrityPopup() {
        // Create enhanced popup modal with progress tracking
        const popup = document.createElement('div');
        popup.className = 'modal fade';
        popup.id = 'dataIntegrityModal';
        popup.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">📊 Data Integrity Check Required</h5>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Missing Data Check</h6>
                            <p class="mb-2">The system needs to verify data integrity for trading hours (9:15 AM - 3:15 PM).</p>
                            <ul class="mb-2">
                                <li><strong>Check last 25 data points</strong> for each of 222 symbols</li>
                                <li><strong>Verify 15-minute intervals:</strong> 9:15, 9:30, 9:45, 10:00... till 3:15</li>
                                <li><strong>Fill missing data</strong> from Angel One API if gaps found</li>
                                <li><strong>Real-time progress</strong> will be shown in logs</li>
                            </ul>
                            <p class="mb-0"><small class="text-muted">⏱️ This may take 2-5 minutes depending on missing data.</small></p>
                        </div>

                        <!-- Progress Section (hidden initially) -->
                        <div id="integrityProgress" class="d-none">
                            <div class="progress mb-3">
                                <div id="integrityProgressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                                     role="progressbar" style="width: 0%"></div>
                            </div>
                            <div id="integrityStatus" class="text-center">
                                <small class="text-muted">Preparing data integrity check...</small>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="cancelIntegrityBtn">Skip for Now</button>
                        <button type="button" class="btn btn-primary" id="startIntegrityBtn">
                            <i class="fas fa-play me-1"></i>Start Data Check
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(popup);

        // Show modal
        const modal = new bootstrap.Modal(popup, {
            backdrop: 'static',  // Prevent closing by clicking outside
            keyboard: false      // Prevent closing with ESC key
        });
        modal.show();

        // Store modal reference for later use
        this.integrityModal = modal;
        this.integrityPopupElement = popup;

        // Add event listener for the start button
        const startBtn = popup.querySelector('#startIntegrityBtn');
        if (startBtn) {
            startBtn.addEventListener('click', () => {
                this.runDataIntegrityCheckWithProgress();
            });
        }

        // Remove from DOM when hidden
        popup.addEventListener('hidden.bs.modal', () => {
            popup.remove();
            this.integrityModal = null;
            this.integrityPopupElement = null;
        });
    }

    async runDataIntegrityCheck() {
        try {
            // Show loading indicator immediately
            this.showAlert('📊 Running data integrity check with DUPLICATE PREVENTION...', 'info');

            // Call comprehensive check endpoint with proper duplicate prevention
            const response = await fetch('/api/data-integrity/comprehensive-check', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showAlert(`✅ Data integrity check started with DUPLICATE PREVENTION. Check logs for progress.`, 'success');
            } else {
                this.showAlert(`❌ Data integrity check failed: ${result.error}`, 'danger');
            }

        } catch (error) {
            console.error('Error running data integrity check:', error);
            this.showAlert('❌ Error running data integrity check', 'danger');
        }
    }

    async runDataIntegrityCheckWithProgress() {
        try {

            // Show progress section
            const progressSection = document.getElementById('integrityProgress');
            const startBtn = document.getElementById('startIntegrityBtn');
            const cancelBtn = document.getElementById('cancelIntegrityBtn');

            console.log('📊 Found elements:', { progressSection, startBtn, cancelBtn });

            progressSection.classList.remove('d-none');
            startBtn.disabled = true;
            startBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Running...';
            cancelBtn.disabled = true;

            // Update status
            this.updateIntegrityProgress(0, 'Starting data integrity check...');
            this.addLog('INTEGRITY', '📊 Starting comprehensive data integrity check for last 25 intervals', 'info');

            // Call comprehensive check endpoint with proper duplicate prevention
            const response = await fetch('/api/data-integrity/comprehensive-check', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            console.log('📡 API Response received:', response.status, response.statusText);

            const result = await response.json();

            if (result.success) {
                this.updateIntegrityProgress(10, 'Data integrity check started in background...');
                this.addLog('INTEGRITY', '✅ Data integrity check started. Progress will be shown in real-time.', 'success');

                // The progress will be updated via WebSocket events
                // Keep the modal open to show progress
            } else {
                this.updateIntegrityProgress(0, `❌ Failed to start: ${result.error}`);
                this.addLog('INTEGRITY', `❌ Data integrity check failed: ${result.error}`, 'danger');

                // Re-enable buttons
                startBtn.disabled = false;
                startBtn.innerHTML = '<i class="fas fa-play me-1"></i>Start Data Check';
                cancelBtn.disabled = false;
            }

        } catch (error) {
            console.error('Error running data integrity check:', error);
            this.updateIntegrityProgress(0, '❌ Error starting data integrity check');
            this.addLog('INTEGRITY', '❌ Error running data integrity check', 'danger');

            // Re-enable buttons
            const startBtn = document.getElementById('startIntegrityBtn');
            const cancelBtn = document.getElementById('cancelIntegrityBtn');
            if (startBtn) {
                startBtn.disabled = false;
                startBtn.innerHTML = '<i class="fas fa-play me-1"></i>Start Data Check';
            }
            if (cancelBtn) cancelBtn.disabled = false;
        }
    }

    updateIntegrityProgress(percentage, status) {
        const progressBar = document.getElementById('integrityProgressBar');
        const statusElement = document.getElementById('integrityStatus');

        if (progressBar) {
            progressBar.style.width = `${percentage}%`;
            progressBar.setAttribute('aria-valuenow', percentage);
        }

        if (statusElement) {
            statusElement.innerHTML = `<small class="text-muted">${status}</small>`;
        }
    }



    async refreshPortfolioData() {
        try {
            const response = await fetch('/api/portfolio-status');
            const data = await response.json();
            
            if (data.success) {
                this.updatePortfolioMetrics(data.portfolio);
            }
        } catch (error) {
            console.error('Error fetching portfolio data:', error);
            this.addLog('ERROR', 'Failed to fetch portfolio data', 'danger');
        }
    }

    async loadSymbols() {
        try {
            const response = await fetch('/api/symbols');
            const data = await response.json();
            
            if (data.success) {
                this.symbols = data.symbols;
                this.renderSymbols(this.symbols);
                this.addLog('DATA', `Loaded ${data.count} symbols`, 'info');
            }
        } catch (error) {
            console.error('Error fetching symbols:', error);
            this.addLog('ERROR', 'Failed to fetch symbols', 'danger');
        }
    }

    async loadActivePositions() {
        try {
            const response = await fetch('/api/active-positions');
            const data = await response.json();
            
            if (data.success) {
                this.activePositions = data.positions;
                this.renderActivePositions(this.activePositions);
            }
        } catch (error) {
            console.error('Error fetching active positions:', error);
            this.addLog('ERROR', 'Failed to fetch active positions', 'danger');
        }
    }

    async startTrading() {
        try {
            const response = await fetch('/api/start-trading', { method: 'POST' });
            const data = await response.json();
            
            if (data.success) {
                this.addLog('TRADING', 'Trading engine start requested', 'info');
            } else {
                this.addLog('ERROR', data.message || 'Failed to start trading', 'danger');
            }
        } catch (error) {
            console.error('Error starting trading:', error);
            this.addLog('ERROR', 'Failed to start trading', 'danger');
        }
    }

    async stopTrading() {
        try {
            const response = await fetch('/api/stop-trading', { method: 'POST' });
            const data = await response.json();
            
            if (data.success) {
                this.addLog('TRADING', 'Trading engine stop requested', 'info');
            } else {
                this.addLog('ERROR', data.message || 'Failed to stop trading', 'danger');
            }
        } catch (error) {
            console.error('Error stopping trading:', error);
            this.addLog('ERROR', 'Failed to stop trading', 'danger');
        }
    }

    async startDataFetch() {
        try {
            const response = await fetch('/api/start-data-fetch', { method: 'POST' });
            const data = await response.json();

            if (data.success) {
                this.addLog('DATA', 'Data fetching start requested', 'info');
            } else {
                this.addLog('ERROR', data.message || 'Failed to start data fetch', 'danger');
            }
        } catch (error) {
            console.error('Error starting data fetch:', error);
            this.addLog('ERROR', 'Failed to start data fetch', 'danger');
        }
    }

    async stopDataFetch() {
        try {
            const response = await fetch('/api/stop-data-fetch', { method: 'POST' });
            const data = await response.json();

            if (data.success) {
                this.addLog('DATA', 'Data fetching stop requested', 'info');
            } else {
                this.addLog('ERROR', data.message || 'Failed to stop data fetching', 'danger');
            }
        } catch (error) {
            console.error('Error stopping data fetch:', error);
            this.addLog('ERROR', 'Failed to stop data fetching', 'danger');
        }
    }

    async loadPortfolioStatus() {
        try {
            const response = await fetch('/api/portfolio-status');
            const data = await response.json();

            if (data.success) {
                this.updatePortfolioMetrics(data.portfolio);
                this.addLog('PORTFOLIO', 'Portfolio status refreshed', 'info');
            } else {
                this.addLog('ERROR', 'Failed to load portfolio status', 'danger');
            }
        } catch (error) {
            console.error('Error loading portfolio status:', error);
            this.addLog('ERROR', 'Failed to load portfolio status', 'danger');
        }
    }

    updateConnectionStatus(connected) {
        const statusIndicator = document.getElementById('connectionStatus');
        const statusText = document.getElementById('connectionText');
        
        if (connected) {
            statusIndicator.className = 'status-indicator status-active';
            statusText.textContent = 'Connected';
        } else {
            statusIndicator.className = 'status-indicator status-inactive';
            statusText.textContent = 'Disconnected';
        }
    }

    updateTradingStatus(active) {
        const statusIndicator = document.getElementById('tradingStatus');
        const statusText = document.getElementById('tradingStatusText');
        const startBtn = document.getElementById('startTradingBtn');
        const stopBtn = document.getElementById('stopTradingBtn');
        
        if (active) {
            statusIndicator.className = 'status-indicator status-active';
            statusText.textContent = 'Trading Active';
            startBtn.classList.add('d-none');
            stopBtn.classList.remove('d-none');
        } else {
            statusIndicator.className = 'status-indicator status-inactive';
            statusText.textContent = 'Trading Inactive';
            startBtn.classList.remove('d-none');
            stopBtn.classList.add('d-none');
        }
    }

    updateDataFetchStatus(active) {
        const statusIndicator = document.getElementById('dataFetchStatus');
        const statusText = document.getElementById('dataFetchStatusText');
        
        if (active) {
            statusIndicator.className = 'status-indicator status-active';
            statusText.textContent = 'Data Fetch Active';
        } else {
            statusIndicator.className = 'status-indicator status-inactive';
            statusText.textContent = 'Data Fetch Inactive';
        }
    }

    updatePortfolioMetrics(portfolio) {
        // Only update detailed metrics in Paper Trading tab (no main dashboard metrics)
        this.updateDetailedPortfolioMetrics(portfolio);

        // Add debug info to console
        console.log('Portfolio Update:', {
            total_pot: portfolio.total_pot,
            available_pot: portfolio.available_pot,
            used_funds: portfolio.used_funds,
            vault_amount: portfolio.vault_amount,
            active_positions: portfolio.active_positions
        });
    }

    updateDetailedPortfolioMetrics(portfolio) {
        // Update detailed metrics in Paper Trading tab
        const elements = {
            'detailedTotalPot': portfolio.total_pot,
            'detailedAvailablePot': portfolio.available_pot || portfolio.total_pot,
            'detailedUsedFunds': portfolio.used_funds || 0,
            'detailedVaultAmount': portfolio.vault_amount,
            'detailedActivePositions': portfolio.active_positions,
            'detailedTotalProfit': portfolio.total_profit || portfolio.vault_amount
        };

        for (const [elementId, value] of Object.entries(elements)) {
            const element = document.getElementById(elementId);
            if (element) {
                if (elementId === 'detailedActivePositions') {
                    element.textContent = value;
                } else {
                    element.textContent = this.formatCurrency(value);
                }
            }
        }
    }

    renderSymbols(symbols) {
        const container = document.getElementById('symbolsGrid');
        
        if (symbols.length === 0) {
            container.innerHTML = '<div class="text-center text-muted">No symbols found</div>';
            return;
        }
        
        container.innerHTML = symbols.map(symbol => `
            <div class="symbol-card" onclick="dashboard.selectSymbol('${symbol}')">
                <div class="fw-bold">${symbol}</div>
                <div class="text-muted small">Click for details</div>
            </div>
        `).join('');
    }

    renderActivePositions(positions) {
        const tbody = document.getElementById('positionsTableBody');
        
        if (positions.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center text-muted">
                        <i class="fas fa-info-circle me-2"></i>
                        No active positions
                    </td>
                </tr>
            `;
            return;
        }
        
        tbody.innerHTML = positions.map(position => `
            <tr>
                <td><strong>${position.symbol}</strong></td>
                <td>₹${position.buy_price.toFixed(2)}</td>
                <td>${position.shares_quantity.toFixed(2)}</td>
                <td>₹${position.investment.toLocaleString()}</td>
                <td>₹${position.target_price.toFixed(2)}</td>
                <td class="price-positive">₹${(position.target_value - position.investment).toFixed(2)}</td>
                <td>${new Date(position.buy_time).toLocaleString()}</td>
                <td><span class="badge bg-success">${position.status}</span></td>
            </tr>
        `).join('');
    }

    filterSymbols(searchTerm) {
        const filtered = this.symbols.filter(symbol => 
            symbol.toLowerCase().includes(searchTerm.toLowerCase())
        );
        this.renderSymbols(filtered);
    }

    selectSymbol(symbol) {
        this.addLog('USER', `Selected symbol: ${symbol}`, 'info');
        // Here you can add functionality to show detailed symbol data
    }

    handleRealTimeUpdate(data) {
        this.addLog('REALTIME', `${data.type}: ${JSON.stringify(data.data)}`, 'info');

        if (data.type === 'portfolio_update') {
            this.updatePortfolioMetrics(data.data);
        }
    }

    handleIntegrityCheckComplete(data) {
        if (data.success) {
            this.displayIntegrityResults(data.result);
            this.addLog('INTEGRITY', data.message || 'Data integrity check completed', 'success');

            // Update progress to 100% and show completion
            this.updateIntegrityProgress(100, '✅ Data integrity check completed successfully!');

            // Update modal buttons
            const startBtn = document.getElementById('startIntegrityBtn');
            const cancelBtn = document.getElementById('cancelIntegrityBtn');
            if (startBtn) {
                startBtn.innerHTML = '<i class="fas fa-check me-1"></i>Completed';
                startBtn.classList.remove('btn-primary');
                startBtn.classList.add('btn-success');
            }
            if (cancelBtn) {
                cancelBtn.textContent = 'Close';
                cancelBtn.disabled = false;
            }

            // Auto-close modal after 3 seconds
            setTimeout(() => {
                if (this.integrityModal) {
                    this.integrityModal.hide();
                }
            }, 3000);

        } else {
            this.showError('integrityResults', data.error || 'Integrity check failed');
            this.addLog('INTEGRITY', 'Data integrity check failed', 'danger');

            // Update progress to show error
            this.updateIntegrityProgress(0, `❌ Error: ${data.error || 'Integrity check failed'}`);

            // Re-enable buttons
            const startBtn = document.getElementById('startIntegrityBtn');
            const cancelBtn = document.getElementById('cancelIntegrityBtn');
            if (startBtn) {
                startBtn.disabled = false;
                startBtn.innerHTML = '<i class="fas fa-play me-1"></i>Retry Check';
            }
            if (cancelBtn) cancelBtn.disabled = false;
        }
    }

    handleIntegrityCheckProgress(data) {
        // Handle real-time progress updates
        if (data.progress !== undefined) {
            this.updateIntegrityProgress(data.progress, data.status || 'Processing...');
        }

        if (data.message) {
            this.addLog('INTEGRITY', data.message, data.type || 'info');
        }
    }

    handlePriorityFetchComplete(data) {
        if (data.success) {
            this.displayPriorityQueueResults({
                message: data.message,
                status: 'Priority queue fetch completed',
                execution_order: 'GOLD → SILVER → BRONZE → REMAINING',
                note: `Fetched ${data.result?.success || 0}/${data.result?.total || 0} symbols successfully`
            });
            this.addLog('PRIORITY', data.message || 'Priority queue fetch completed', 'success');
        } else {
            this.showError('integrityResults', data.error || 'Priority queue fetch failed');
            this.addLog('PRIORITY', 'Priority queue fetch failed', 'danger');
        }
    }

    handleTradingSignal(data) {
        const signal = data.signal;
        this.addLog('SIGNAL', `${signal.signal_type} signal for ${signal.symbol} at ₹${signal.price}`,
                   signal.signal_type === 'BUY' ? 'success' : 'warning');

        // Auto-refresh paper trading data when new signal received
        this.refreshPaperTradingIfVisible();

        // Also refresh portfolio data to update pot/vault values
        this.refreshPortfolioData();
    }

    addLog(category, message, type = 'info') {
        const logContainer = document.getElementById('logContainer');
        const timestamp = new Date().toLocaleTimeString();
        const colorClass = {
            'success': 'text-success',
            'danger': 'text-danger',
            'warning': 'text-warning',
            'info': 'text-info'
        }[type] || 'text-light';
        
        const logEntry = `<div class="${colorClass}">[${timestamp}] [${category}] ${message}</div>`;
        logContainer.innerHTML += logEntry;
        logContainer.scrollTop = logContainer.scrollHeight;
        
        // Keep only last 100 log entries
        const logs = logContainer.children;
        if (logs.length > 100) {
            logContainer.removeChild(logs[0]);
        }
    }

    formatCurrency(amount) {
        if (amount === 0) return '₹0';

        const absAmount = Math.abs(amount);
        const isNegative = amount < 0;
        const prefix = isNegative ? '-₹' : '₹';

        if (absAmount >= 10000000) { // 1 crore and above
            const crores = absAmount / 10000000;
            if (crores >= 100) {
                return prefix + crores.toFixed(0) + ' Cr';
            } else if (crores >= 10) {
                return prefix + crores.toFixed(1) + ' Cr';
            } else {
                return prefix + crores.toFixed(2) + ' Cr';
            }
        } else if (absAmount >= 100000) { // 1 lakh and above
            const lakhs = absAmount / 100000;
            if (lakhs >= 10) {
                return prefix + lakhs.toFixed(0) + ' L';
            } else {
                return prefix + lakhs.toFixed(1) + ' L';
            }
        } else if (absAmount >= 1000) { // 1 thousand and above
            const thousands = absAmount / 1000;
            if (thousands >= 10) {
                return prefix + thousands.toFixed(0) + ' K';
            } else {
                return prefix + thousands.toFixed(1) + ' K';
            }
        } else {
            // Less than 1000, show full amount
            return prefix + absAmount.toLocaleString('en-IN');
        }
    }

    // SQL Query Tab Methods
    async initializeSqlTab() {
        await this.loadDatabaseSchema();
        await this.loadQueryTemplates();
    }

    async loadDatabaseSchema() {
        try {
            const response = await fetch('/api/sql-schema');
            const data = await response.json();

            if (data.success) {
                this.renderDatabaseSchema(data.schema);
            } else {
                this.showSchemaError(data.error);
            }
        } catch (error) {
            console.error('Error loading database schema:', error);
            this.showSchemaError('Failed to load database schema');
        }
    }

    async loadQueryTemplates() {
        try {
            const response = await fetch('/api/sql-templates');
            const data = await response.json();

            if (data.success) {
                this.renderQueryTemplates(data.templates);
            }
        } catch (error) {
            console.error('Error loading query templates:', error);
        }
    }

    renderDatabaseSchema(schema) {
        const container = document.getElementById('databaseSchema');

        let html = '';
        for (const [tableName, tableInfo] of Object.entries(schema)) {
            html += `
                <div class="mb-3">
                    <h6 class="text-primary">
                        <i class="fas fa-table me-2"></i>${tableName}
                        <span class="badge bg-secondary">${tableInfo.row_count} rows</span>
                    </h6>
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>Column</th>
                                    <th>Type</th>
                                    <th>Key</th>
                                </tr>
                            </thead>
                            <tbody>
            `;

            tableInfo.columns.forEach(col => {
                const keyBadge = col.primary_key ? '<span class="badge bg-warning">PK</span>' :
                               col.not_null ? '<span class="badge bg-info">NN</span>' : '';

                html += `
                    <tr>
                        <td><code>${col.name}</code></td>
                        <td>${col.type}</td>
                        <td>${keyBadge}</td>
                    </tr>
                `;
            });

            html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        container.innerHTML = html;
    }

    renderQueryTemplates(templates) {
        const select = document.getElementById('queryTemplates');

        // Clear existing options except the first one
        select.innerHTML = '<option value="">Select a template...</option>';

        for (const [category, queries] of Object.entries(templates)) {
            const optgroup = document.createElement('optgroup');
            optgroup.label = category.replace('_', ' ').toUpperCase();

            queries.forEach((query, index) => {
                const option = document.createElement('option');
                option.value = JSON.stringify(query);
                option.textContent = query.name;
                optgroup.appendChild(option);
            });

            select.appendChild(optgroup);
        }
    }

    loadQueryTemplate(templateJson) {
        if (!templateJson) return;

        try {
            const template = JSON.parse(templateJson);
            document.getElementById('sqlQuery').value = template.query;

            this.showQueryStatus(`Loaded template: ${template.name}`, 'info');
        } catch (error) {
            this.showQueryStatus('Error loading template', 'danger');
        }
    }

    async executeQuery() {
        const query = document.getElementById('sqlQuery').value.trim();

        if (!query) {
            this.showQueryStatus('Please enter a SQL query', 'warning');
            return;
        }

        this.showQueryStatus('Executing query...', 'info');

        try {
            const response = await fetch('/api/sql-query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ query: query })
            });

            const data = await response.json();

            if (data.success) {
                this.renderQueryResults(data);
                this.showQueryStatus(`Query executed successfully. ${data.row_count} rows returned.`, 'success');
            } else {
                this.showQueryStatus(`Query failed: ${data.error}`, 'danger');
                this.clearQueryResults();
            }
        } catch (error) {
            console.error('Error executing query:', error);
            this.showQueryStatus('Network error executing query', 'danger');
            this.clearQueryResults();
        }
    }

    renderQueryResults(data) {
        const container = document.getElementById('queryResults');
        const exportBtn = document.getElementById('exportCsvBtn');

        if (data.row_count === 0) {
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-info-circle me-2"></i>
                    Query executed successfully but returned no results
                </div>
            `;
            exportBtn.style.display = 'none';
            return;
        }

        // Store data for export
        this.lastQueryResults = data;

        // Create table
        let html = `
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
        `;

        data.columns.forEach(col => {
            html += `<th>${col}</th>`;
        });

        html += `
                        </tr>
                    </thead>
                    <tbody>
        `;

        data.data.forEach(row => {
            html += '<tr>';
            data.columns.forEach(col => {
                const value = row[col];
                const displayValue = value === null ? '<em class="text-muted">NULL</em>' :
                                   typeof value === 'number' ? value.toLocaleString() :
                                   String(value);
                html += `<td>${displayValue}</td>`;
            });
            html += '</tr>';
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        if (data.has_more) {
            html += `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Results limited to 1000 rows. Use LIMIT clause for specific row counts.
                </div>
            `;
        }

        container.innerHTML = html;
        exportBtn.style.display = 'inline-block';
    }

    clearQueryResults() {
        const container = document.getElementById('queryResults');
        const exportBtn = document.getElementById('exportCsvBtn');

        container.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-info-circle me-2"></i>
                Execute a query to see results here
            </div>
        `;

        exportBtn.style.display = 'none';
        this.lastQueryResults = null;
    }

    showQueryStatus(message, type) {
        const statusDiv = document.getElementById('queryStatus');

        statusDiv.className = `alert alert-${type}`;
        statusDiv.innerHTML = `<i class="fas fa-info-circle me-2"></i>${message}`;
        statusDiv.classList.remove('d-none');

        // Auto-hide after 5 seconds for non-error messages
        if (type !== 'danger') {
            setTimeout(() => {
                statusDiv.classList.add('d-none');
            }, 5000);
        }
    }

    showSchemaError(error) {
        const container = document.getElementById('databaseSchema');
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Error loading schema: ${error}
            </div>
        `;
    }

    formatQuery() {
        const textarea = document.getElementById('sqlQuery');
        let query = textarea.value;

        if (!query.trim()) return;

        // Basic SQL formatting
        query = query
            .replace(/\s+/g, ' ')
            .replace(/\s*,\s*/g, ',\n    ')
            .replace(/\bSELECT\b/gi, 'SELECT')
            .replace(/\bFROM\b/gi, '\nFROM')
            .replace(/\bWHERE\b/gi, '\nWHERE')
            .replace(/\bGROUP BY\b/gi, '\nGROUP BY')
            .replace(/\bORDER BY\b/gi, '\nORDER BY')
            .replace(/\bLIMIT\b/gi, '\nLIMIT')
            .trim();

        textarea.value = query;
        this.showQueryStatus('Query formatted', 'info');
    }

    // Symbol Explorer Tab Methods
    async initializeDataLogsTab() {
        await this.loadAllSymbols();
        await this.loadDatabaseSummaries();
        await this.populateSymbolDropdown();

        // Auto-refresh every 30 seconds
        if (this.symbolExplorerInterval) {
            clearInterval(this.symbolExplorerInterval);
        }
        this.symbolExplorerInterval = setInterval(() => {
            this.loadAllSymbols();
            this.loadDatabaseSummaries();
        }, 30000);
    }

    async loadAllSymbols() {
        try {
            const response = await fetch('/api/sql-dashboard/summary');
            const data = await response.json();

            if (data.success && data.summary.db1) {
                this.renderAllSymbolsList(data.summary.db1);
            }
        } catch (error) {
            console.error('Error loading symbols:', error);
        }
    }

    async populateSymbolDropdown() {
        try {
            const response = await fetch('/api/symbols/search?q=');
            const data = await response.json();

            if (data.success) {
                const select = document.getElementById('symbolSelect');
                select.innerHTML = '<option value="">Choose a symbol...</option>';

                data.results.forEach(symbol => {
                    const option = document.createElement('option');
                    option.value = symbol;
                    option.textContent = symbol;
                    select.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading symbol dropdown:', error);
        }
    }

    renderAllSymbolsList(db1Data) {
        const container = document.getElementById('allSymbolsList');
        const countBadge = document.getElementById('symbolCount');

        if (!db1Data || db1Data.error) {
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-exclamation-triangle me-2"></i>Error loading symbols
                </div>
            `;
            return;
        }

        countBadge.textContent = db1Data.unique_symbols || 0;

        // Get all symbols with their record counts
        let html = '';
        if (db1Data.top_symbols && db1Data.top_symbols.length > 0) {
            db1Data.top_symbols.forEach((symbolData, index) => {
                const isComplete = symbolData.count >= 25;
                const statusClass = isComplete ? 'text-success' : 'text-warning';
                const statusIcon = isComplete ? 'fas fa-check-circle' : 'fas fa-exclamation-circle';

                html += `
                    <div class="symbol-item d-flex justify-content-between align-items-center py-2 border-bottom"
                         style="cursor: pointer;"
                         onclick="dashboard.selectSymbolFromList('${symbolData.symbol}')">
                        <div>
                            <strong>${symbolData.symbol}</strong>
                            <br>
                            <small class="${statusClass}">
                                <i class="${statusIcon} me-1"></i>
                                ${symbolData.count} records
                            </small>
                        </div>
                        <div class="text-end">
                            <span class="badge ${isComplete ? 'bg-success' : 'bg-warning'}">${isComplete ? 'Complete' : 'Partial'}</span>
                        </div>
                    </div>
                `;
            });
        }

        container.innerHTML = html;
    }

    selectSymbolFromList(symbol) {
        const select = document.getElementById('symbolSelect');
        select.value = symbol;
        this.loadSymbolData();
    }

    filterSymbolList(searchTerm) {
        const symbolItems = document.querySelectorAll('.symbol-item');
        symbolItems.forEach(item => {
            const symbolName = item.querySelector('strong').textContent;
            if (symbolName.toLowerCase().includes(searchTerm.toLowerCase())) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    }

    async loadSymbolData() {
        const symbol = document.getElementById('symbolSelect').value;
        const dataSource = document.getElementById('dataSourceSelect').value;

        if (!symbol) {
            document.getElementById('symbolDataDisplay').innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-info-circle me-2"></i>Select a symbol to view its data
                </div>
            `;
            return;
        }

        try {
            let query = '';
            if (dataSource === 'db1') {
                query = `SELECT timestamp, open_price, high_price, low_price, close_price, volume, created_at FROM trading_data WHERE symbol = '${symbol}' ORDER BY timestamp DESC LIMIT 20`;
            } else {
                query = `SELECT symbol, buy_price, sell_price, shares_quantity, investment, profit_amount, status, buy_time, sell_time FROM trading_positions WHERE symbol = '${symbol}' ORDER BY buy_time DESC LIMIT 20`;
            }

            const response = await fetch('/api/sql-query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ query: query })
            });

            const data = await response.json();

            if (data.success) {
                this.renderSymbolData(data, symbol, dataSource);
            } else {
                this.showSymbolDataError(data.error);
            }
        } catch (error) {
            console.error('Error loading symbol data:', error);
            this.showSymbolDataError('Failed to load symbol data');
        }
    }

    renderSymbolData(data, symbol, dataSource) {
        const container = document.getElementById('symbolDataDisplay');

        if (data.row_count === 0) {
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-info-circle me-2"></i>No data found for ${symbol} in ${dataSource.toUpperCase()}
                </div>
            `;
            return;
        }

        let html = `
            <div class="mb-3">
                <h6 class="text-primary">
                    <i class="fas fa-chart-line me-2"></i>${symbol} - ${dataSource.toUpperCase()} Data (Last 20 Records)
                </h6>
            </div>
            <div class="table-responsive">
                <table class="table table-striped table-hover table-sm">
                    <thead class="table-dark">
                        <tr>
        `;

        // Add column headers
        data.columns.forEach(col => {
            html += `<th style="font-size: 0.8rem;">${col}</th>`;
        });

        html += `
                        </tr>
                    </thead>
                    <tbody>
        `;

        // Add data rows
        data.rows.forEach(row => {
            html += '<tr>';
            row.forEach((cell, index) => {
                let cellValue = cell;

                // Format specific columns
                if (data.columns[index].includes('price') || data.columns[index].includes('amount')) {
                    cellValue = typeof cell === 'number' ? `₹${cell.toFixed(2)}` : cell;
                } else if (data.columns[index].includes('time')) {
                    cellValue = cell ? new Date(cell).toLocaleString() : 'N/A';
                }

                html += `<td style="font-size: 0.8rem;">${cellValue || 'N/A'}</td>`;
            });
            html += '</tr>';
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = html;
    }

    refreshSymbolExplorer() {
        this.loadAllSymbols();
        this.loadDatabaseSummaries();
        this.loadSymbolData();
    }

    async loadDatabaseSummaries() {
        try {
            const response = await fetch('/api/sql-dashboard/summary');
            const data = await response.json();

            if (data.success) {
                this.renderDatabaseSummaries(data.summary);
            }
        } catch (error) {
            console.error('Error loading database summaries:', error);
        }
    }

    renderDatabaseSummaries(summary) {
        // Render DB1 Summary
        const db1Container = document.getElementById('db1Summary');
        if (summary.db1 && !summary.db1.error) {
            let html = `
                <div class="row text-center mb-3">
                    <div class="col-6">
                        <div class="metric-value text-primary">${summary.db1.total_records}</div>
                        <div class="metric-label">Total Records</div>
                    </div>
                    <div class="col-6">
                        <div class="metric-value text-info">${summary.db1.unique_symbols}</div>
                        <div class="metric-label">Unique Symbols</div>
                    </div>
                </div>
                <div class="row text-center mb-3">
                    <div class="col-6">
                        <div class="metric-value text-warning">${summary.db1.recent_records}</div>
                        <div class="metric-label">Recent Records</div>
                    </div>
                    <div class="col-6">
                        <div class="metric-value text-success">${summary.db1.volume_leaders ? summary.db1.volume_leaders.length : 0}</div>
                        <div class="metric-label">Volume Leaders</div>
                    </div>
                </div>
            `;

            if (summary.db1.top_symbols && summary.db1.top_symbols.length > 0) {
                html += `
                    <h6 class="text-success">📈 Top 5 Symbols (Data Count)</h6>
                    <div class="list-group list-group-flush">
                `;

                summary.db1.top_symbols.slice(0, 5).forEach(symbol => {
                    const isComplete = symbol.count >= 25;
                    const badgeClass = isComplete ? 'bg-success' : 'bg-warning';
                    html += `
                        <div class="list-group-item d-flex justify-content-between align-items-center py-1"
                             style="cursor: pointer;"
                             onclick="dashboard.selectSymbolFromSummary('${symbol.symbol}')">
                            <span>${symbol.symbol}</span>
                            <span class="badge ${badgeClass}">${symbol.count}</span>
                        </div>
                    `;
                });

                html += `</div>`;
            }

            db1Container.innerHTML = html;
        } else {
            db1Container.innerHTML = `<div class="alert alert-danger">Error: ${summary.db1?.error || 'Unknown error'}</div>`;
        }

        // Render DB2 Summary
        const db2Container = document.getElementById('db2Summary');
        if (summary.db2 && !summary.db2.error) {
            let html = `
                <div class="row text-center mb-3">
                    <div class="col-6">
                        <div class="metric-value text-success">${summary.db2.active_positions}</div>
                        <div class="metric-label">Active Positions</div>
                    </div>
                    <div class="col-6">
                        <div class="metric-value text-info">${summary.db2.closed_positions}</div>
                        <div class="metric-label">Closed Positions</div>
                    </div>
                </div>
                <div class="row text-center mb-3">
                    <div class="col-6">
                        <div class="metric-value text-success">₹${summary.db2.profit_stats.total_profit}</div>
                        <div class="metric-label">Total Profit</div>
                    </div>
                    <div class="col-6">
                        <div class="metric-value text-warning">${summary.db2.profit_stats.winning_trades}</div>
                        <div class="metric-label">Winning Trades</div>
                    </div>
                </div>
            `;

            if (summary.db2.recent_confirmations && summary.db2.recent_confirmations.length > 0) {
                html += `
                    <h6 class="text-info">🔄 Recent Confirmations</h6>
                    <div class="list-group list-group-flush">
                `;

                summary.db2.recent_confirmations.slice(0, 3).forEach(conf => {
                    const statusBadge = conf.ready ? 'bg-success' : 'bg-warning';
                    const statusText = conf.ready ? 'Ready' : 'Pending';
                    html += `
                        <div class="list-group-item d-flex justify-content-between align-items-center py-1">
                            <span>${conf.symbol} (${conf.signal})</span>
                            <span class="badge ${statusBadge}">${statusText}</span>
                        </div>
                    `;
                });

                html += `</div>`;
            }

            db2Container.innerHTML = html;
        } else {
            db2Container.innerHTML = `<div class="alert alert-danger">Error: ${summary.db2?.error || 'Unknown error'}</div>`;
        }
    }

    selectSymbolFromSummary(symbol) {
        const select = document.getElementById('symbolSelect');
        select.value = symbol;
        this.loadSymbolData();
    }

    showSymbolDataError(error) {
        const container = document.getElementById('symbolDataDisplay');
        container.innerHTML = `
            <div class="text-center text-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>Error: ${error}
            </div>
        `;
    }

    async runDataIntegrityCheck() {
        try {
            const response = await fetch('/api/data-integrity/comprehensive-check', {
                method: 'POST'
            });
            const data = await response.json();

            if (data.success) {
                this.showAlert('Data integrity check started successfully!', 'success');
                // Refresh logs to show progress
                setTimeout(() => this.refreshDataLogs(), 2000);
            } else {
                this.showAlert(`Error: ${data.error}`, 'danger');
            }
        } catch (error) {
            this.showAlert(`Network error: ${error.message}`, 'danger');
        }
    }

    async clearAllDatabases() {
        if (!confirm('⚠️ WARNING: This will clear ALL data from DB1 and DB2. Are you sure?')) {
            return;
        }

        try {
            const response = await fetch('/api/database/clear-all', {
                method: 'POST'
            });
            const data = await response.json();

            if (data.success) {
                this.showAlert('All databases cleared successfully!', 'success');
                // Refresh stats
                setTimeout(() => {
                    this.loadDatabaseStats();
                    this.loadSqlDashboardSummary();
                }, 1000);
            } else {
                this.showAlert(`Error: ${data.error}`, 'danger');
            }
        } catch (error) {
            this.showAlert(`Network error: ${error.message}`, 'danger');
        }
    }

    async checkDuplicates() {
        try {
            const response = await fetch('/api/data-integrity/check-duplicates', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ symbol: 'RELIANCE' })
            });
            const data = await response.json();

            if (data.success) {
                const message = data.recommendation;
                this.showAlert(message, data.analysis.duplicate_records > 0 ? 'warning' : 'success');
            } else {
                this.showAlert(`Error: ${data.error}`, 'danger');
            }
        } catch (error) {
            this.showAlert(`Network error: ${error.message}`, 'danger');
        }
    }

    showAlert(message, type) {
        // Create alert element
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert at top of main content
        const mainContent = document.querySelector('.container-fluid');
        mainContent.insertBefore(alertDiv, mainContent.firstChild);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    exportResultsToCsv() {
        if (!this.lastQueryResults) {
            this.showQueryStatus('No results to export', 'warning');
            return;
        }

        const data = this.lastQueryResults;
        let csv = data.columns.join(',') + '\n';

        data.data.forEach(row => {
            const values = data.columns.map(col => {
                const value = row[col];
                if (value === null) return '';
                if (typeof value === 'string' && value.includes(',')) {
                    return `"${value.replace(/"/g, '""')}"`;
                }
                return String(value);
            });
            csv += values.join(',') + '\n';
        });

        const blob = new Blob([csv], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `query_results_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showQueryStatus('Results exported to CSV', 'success');
    }
}

// Utility functions
function clearLogs() {
    document.getElementById('logContainer').innerHTML = '';
    dashboard.addLog('SYSTEM', 'Logs cleared', 'info');
}

function downloadLogs() {
    const logs = document.getElementById('logContainer').innerText;
    const blob = new Blob([logs], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `trading_logs_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    dashboard.addLog('SYSTEM', 'Logs downloaded', 'success');
}

// Add new features methods to TradingDashboard class
TradingDashboard.prototype.initializeNewFeatures = function() {
    // Add event listeners for new features
    this.initializeDataIntegrityFeatures();
    this.initializeDuplicateFeatures();
    this.initializeSymbolManagerFeatures();

    // Symbol manager tab event listener is now handled in initializeSymbolManagerFeatures

    // Load paper trades when Paper Trading tab is opened
    document.getElementById('paper-trading-tab').addEventListener('shown.bs.tab', () => {
        this.loadPaperTrades();
        this.initializeResetButton();
    });

    // Initialize paper trading features
    this.initializePaperTradingFeatures();


};

TradingDashboard.prototype.initializeDataIntegrityFeatures = function() {
    document.getElementById('runIntegrityCheckBtn').addEventListener('click', () => {
        this.runIntegrityCheck();
    });

    document.getElementById('getIntegrityStatusBtn').addEventListener('click', () => {
        this.getIntegrityStatus();
    });

    document.getElementById('runComprehensiveCheckBtn').addEventListener('click', () => {
        this.runComprehensiveIntegrityCheck();
    });

    document.getElementById('runPriorityQueueBtn').addEventListener('click', () => {
        this.runPriorityQueueFetch();
    });

    document.getElementById('stopRealtimeBtn').addEventListener('click', () => {
        this.stopRealtimeFetcher();
    });

    document.getElementById('startRealtimeBtn').addEventListener('click', () => {
        this.startRealtimeFetching();
    });
};

TradingDashboard.prototype.initializeDuplicateFeatures = function() {
    // Add event listener for when duplicates tab is shown
    document.getElementById('duplicates-tab').addEventListener('shown.bs.tab', () => {
        this.attachDuplicateEventListeners();
    });

    // Also attach immediately in case tab is already active
    this.attachDuplicateEventListeners();

    // Debug: Test if buttons exist
    setTimeout(() => {
        const scanBtn = document.getElementById('scanDuplicatesBtn');
        const removeBtn = document.getElementById('removeDuplicatesBtn');
        console.log('Duplicate buttons found:', {
            scanBtn: !!scanBtn,
            removeBtn: !!removeBtn
        });
    }, 1000);
};

TradingDashboard.prototype.attachDuplicateEventListeners = function() {
    const scanBtn = document.getElementById('scanDuplicatesBtn');
    const removeBtn = document.getElementById('removeDuplicatesBtn');

    if (scanBtn && !scanBtn.hasAttribute('data-listener-attached')) {
        scanBtn.addEventListener('click', () => {
            console.log('Scan Duplicates button clicked!'); // Debug log
            this.scanDuplicates();
        });
        scanBtn.setAttribute('data-listener-attached', 'true');
    }

    if (removeBtn && !removeBtn.hasAttribute('data-listener-attached')) {
        removeBtn.addEventListener('click', () => {
            console.log('Remove Duplicates button clicked!'); // Debug log
            this.removeDuplicates();
        });
        removeBtn.setAttribute('data-listener-attached', 'true');
    }
};

TradingDashboard.prototype.initializeSymbolManagerFeatures = function() {
    // Add event listener for when symbol manager tab is shown
    document.getElementById('symbol-manager-tab').addEventListener('shown.bs.tab', () => {
        this.loadSymbolStats();
        this.loadMissingTokens();
        this.attachSymbolManagerEventListeners();
    });

    // Also attach immediately in case tab is already active
    this.attachSymbolManagerEventListeners();

    // Debug: Test if buttons exist
    setTimeout(() => {
        const addBtn = document.getElementById('addSymbolsBtn');
        const deleteBtn = document.getElementById('deleteSymbolsBtn');
        console.log('Symbol manager buttons found:', {
            addBtn: !!addBtn,
            deleteBtn: !!deleteBtn
        });
    }, 1000);
};

TradingDashboard.prototype.attachSymbolManagerEventListeners = function() {
    const addBtn = document.getElementById('addSymbolsBtn');
    const deleteBtn = document.getElementById('deleteSymbolsBtn');
    const searchBtn = document.getElementById('searchSymbolsBtn');

    if (addBtn && !addBtn.hasAttribute('data-listener-attached')) {
        addBtn.addEventListener('click', () => {
            console.log('Add Symbols button clicked!'); // Debug log
            this.addSymbols();
        });
        addBtn.setAttribute('data-listener-attached', 'true');
    }

    if (deleteBtn && !deleteBtn.hasAttribute('data-listener-attached')) {
        deleteBtn.addEventListener('click', () => {
            console.log('Delete Symbols button clicked!'); // Debug log
            this.deleteSymbols();
        });
        deleteBtn.setAttribute('data-listener-attached', 'true');
    }

    if (searchBtn && !searchBtn.hasAttribute('data-listener-attached')) {
        searchBtn.addEventListener('click', () => {
            console.log('Search Symbols button clicked!'); // Debug log
            this.searchSymbols();
        });
        searchBtn.setAttribute('data-listener-attached', 'true');
    }
};

TradingDashboard.prototype.initializeResetButton = function() {
    const completeResetBtn = document.getElementById('completeResetBtn');
    if (completeResetBtn) {
        if (this.resetButtonHandler) {
            completeResetBtn.removeEventListener('click', this.resetButtonHandler);
        }

        this.resetButtonHandler = () => {
            this.completeResetPaperTrades();
        };

        completeResetBtn.addEventListener('click', this.resetButtonHandler);
        this.resetButtonInitialized = true;
    }
};

// Data Integrity Methods
TradingDashboard.prototype.runIntegrityCheck = async function() {
    try {
        this.showLoading('integrityResults', '🔍 Running integrity check with DUPLICATE PREVENTION...');

        // Use the comprehensive check endpoint which has proper duplicate prevention
        const response = await fetch('/api/data-integrity/comprehensive-check', { method: 'POST' });
        const data = await response.json();

        if (data.success) {
            // Show that the check is running in background
            const container = document.getElementById('integrityResults');
            container.innerHTML = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-clock me-2"></i>✅ Integrity Check Running (with Duplicate Prevention)</h6>
                    <p>${data.message}</p>
                    <p><small>✅ <strong>Duplicate Prevention Active:</strong> Only missing data will be fetched</small></p>
                    <p><small>📊 Results will appear here when complete. Check logs for progress.</small></p>
                </div>
            `;
            this.addLog('INTEGRITY', '✅ Data integrity check started with DUPLICATE PREVENTION', 'info');
        } else {
            this.showError('integrityResults', data.error);
        }
    } catch (error) {
        console.error('Error running integrity check:', error);
        this.showError('integrityResults', 'Failed to run integrity check');
    }
};

TradingDashboard.prototype.getIntegrityStatus = async function() {
    try {
        const response = await fetch('/api/data-integrity/status');
        const data = await response.json();

        if (data.success) {
            this.displayIntegrityStatus(data.status);
        } else {
            this.showError('integrityStatus', data.error);
        }
    } catch (error) {
        console.error('Error getting integrity status:', error);
        this.showError('integrityStatus', 'Failed to get integrity status');
    }
};

TradingDashboard.prototype.runComprehensiveIntegrityCheck = async function() {
    try {
        this.showLoading('integrityResults', '🔍 Running comprehensive integrity check for ALL 222 symbols...');

        const response = await fetch('/api/data-integrity/comprehensive-check', { method: 'POST' });
        const data = await response.json();

        if (data.success) {
            this.displayIntegrityResults({
                message: data.message,
                status: 'Comprehensive check started',
                note: 'Check logs for detailed progress'
            });
            this.addLog('INTEGRITY', '🔍 Comprehensive integrity check started for ALL symbols', 'info');
        } else {
            this.showError('integrityResults', data.error);
        }
    } catch (error) {
        console.error('Error running comprehensive integrity check:', error);
        this.showError('integrityResults', 'Failed to start comprehensive integrity check');
    }
};

TradingDashboard.prototype.runPriorityQueueFetch = async function() {
    try {
        this.showLoading('integrityResults', '🎯 Executing PRIORITY QUEUE fetch (GOLD→SILVER→BRONZE→REMAINING)...');

        const response = await fetch('/api/priority-queue/fetch', { method: 'POST' });
        const data = await response.json();

        if (data.success) {
            this.displayPriorityQueueResults({
                message: data.message,
                status: 'Priority queue fetch started',
                execution_order: 'GOLD → SILVER → BRONZE → REMAINING',
                note: 'Check logs for detailed batch execution progress'
            });
            this.addLog('PRIORITY', '🎯 Priority Queue fetch started - GOLD/SILVER batches are CRITICAL', 'warning');
        } else {
            this.showError('integrityResults', data.error);
        }
    } catch (error) {
        console.error('Error running priority queue fetch:', error);
        this.showError('integrityResults', 'Failed to start priority queue fetch');
    }
};

TradingDashboard.prototype.displayPriorityQueueResults = function(results) {
    const container = document.getElementById('integrityResults');
    container.innerHTML = `
        <div class="alert alert-warning">
            <h6><i class="fas fa-trophy me-2"></i>Priority Queue Execution</h6>
            <p><strong>Status:</strong> ${results.status}</p>
            <p><strong>Execution Order:</strong> ${results.execution_order}</p>
            <div class="mt-3">
                <h6>🥇 GOLD BATCH (CRITICAL):</h6>
                <ul class="mb-2">
                    <li>🔴 SELL-READY: Active positions at target prices</li>
                    <li>🟢 BUY-READY: Symbols with FFFFR? pattern (5/6 complete)</li>
                </ul>

                <h6>🥈 SILVER BATCH (HIGH):</h6>
                <ul class="mb-2">
                    <li>🟡 BUY-POTENTIAL: Symbols with FFFF?? pattern (4/6 complete)</li>
                    <li>🟡 SELL-MONITOR: Positions approaching targets (₹600+ profit)</li>
                </ul>

                <h6>🥉 BRONZE BATCH (MEDIUM):</h6>
                <ul class="mb-2">
                    <li>🟤 PATTERN-BUILDING: Symbols with 3+ FALL sequences</li>
                    <li>🟤 POSITION-MONITORING: Active positions with lower profits</li>
                </ul>

                <h6>⚪ REMAINING BATCH (LOW):</h6>
                <ul>
                    <li>Pattern discovery for all other symbols</li>
                </ul>
            </div>
            <div class="mt-3">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    ${results.note}
                </small>
            </div>
        </div>
    `;
};

// Duplicate Methods
TradingDashboard.prototype.scanDuplicates = async function() {
    try {
        this.showLoading('duplicateResults', 'Scanning for duplicates...');

        const response = await fetch('/api/symbols/scan-duplicates');
        const data = await response.json();

        if (data.success) {
            this.displayDuplicateResults(data.duplicates);
            this.addLog('DUPLICATES', `Found ${data.duplicates.total_count} duplicate records`, 'warning');
        } else {
            this.showError('duplicateResults', data.error);
        }
    } catch (error) {
        console.error('Error scanning duplicates:', error);
        this.showError('duplicateResults', 'Failed to scan duplicates');
    }
};

TradingDashboard.prototype.displayDuplicateResults = function(duplicates) {
    const container = document.getElementById('duplicateResults');
    const count = duplicates.total_count;

    if (count === 0) {
        container.innerHTML = `
            <div class="alert alert-success">
                <h6><i class="fas fa-check-circle me-2"></i>No Duplicates Found</h6>
                <p>Your database is clean! No duplicate records were found in Excel, DB1, or DB2.</p>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-database me-1"></i>All databases are optimized and ready for trading
                    </small>
                </div>
            </div>
        `;
    } else {
        let duplicatesList = '';

        // Show duplicates from all sources
        const allDuplicates = duplicates.all_duplicates || [];
        allDuplicates.slice(0, 10).forEach(symbol => {
            let sources = [];
            if (duplicates.excel.includes(symbol)) sources.push('Excel');
            if (duplicates.db1.includes(symbol)) sources.push('DB1');
            if (duplicates.db2.includes(symbol)) sources.push('DB2');

            duplicatesList += `
                <tr>
                    <td><strong>${symbol}</strong></td>
                    <td>${sources.join(', ')}</td>
                    <td><span class="badge bg-warning">${sources.length}</span></td>
                </tr>
            `;
        });

        container.innerHTML = `
            <div class="alert alert-warning">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>Duplicates Found</h6>
                <p>Found <strong>${count}</strong> duplicate symbols across Excel, DB1, and DB2.</p>

                <div class="row mb-3">
                    <div class="col-4 text-center">
                        <div class="metric-value text-warning">${duplicates.excel.length}</div>
                        <div class="metric-label">Excel</div>
                    </div>
                    <div class="col-4 text-center">
                        <div class="metric-value text-warning">${duplicates.db1.length}</div>
                        <div class="metric-label">DB1</div>
                    </div>
                    <div class="col-4 text-center">
                        <div class="metric-value text-warning">${duplicates.db2.length}</div>
                        <div class="metric-label">DB2</div>
                    </div>
                </div>

                <div class="table-responsive mt-3">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Symbol</th>
                                <th>Found In</th>
                                <th>Sources</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${duplicatesList}
                        </tbody>
                    </table>
                </div>

                ${count > 10 ? `<small class="text-muted">Showing first 10 of ${count} duplicates</small>` : ''}

                <div class="mt-3">
                    <button class="btn btn-warning btn-sm" onclick="dashboard.removeDuplicates()">
                        <i class="fas fa-trash me-1"></i>Clean All Duplicates
                    </button>
                </div>
            </div>
        `;
    }
};

TradingDashboard.prototype.removeDuplicates = async function() {
    try {
        if (!confirm('Are you sure you want to remove duplicate records? This action cannot be undone.')) {
            return;
        }

        this.showLoading('duplicateResults', 'Removing duplicates...');

        const response = await fetch('/api/symbols/clean-duplicates', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        });
        const data = await response.json();

        if (data.success) {
            this.displayRemovalResults(data.message);
            this.addLog('DUPLICATES', 'Duplicate records removed successfully', 'success');
        } else {
            this.showError('duplicateResults', data.error);
        }
    } catch (error) {
        console.error('Error removing duplicates:', error);
        this.showError('duplicateResults', 'Failed to remove duplicates');
    }
};

// Symbol Manager Methods
TradingDashboard.prototype.loadSymbolStats = async function() {
    try {
        const response = await fetch('/api/symbols/stats');
        const data = await response.json();

        if (data.success) {
            this.displaySymbolStats(data.stats);
        } else {
            this.showError('symbolStats', data.error);
        }
    } catch (error) {
        console.error('Error loading symbol stats:', error);
        this.showError('symbolStats', 'Failed to load symbol stats');
    }
};

TradingDashboard.prototype.addSymbols = async function() {
    try {
        const symbolsText = document.getElementById('symbolsToAdd').value.trim();
        if (!symbolsText) {
            alert('Please enter symbols to add');
            return;
        }

        const symbols = symbolsText.split('\n').map(s => s.trim()).filter(s => s);

        const response = await fetch('/api/symbols/add', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ symbols })
        });
        const data = await response.json();

        if (data.success) {
            this.displaySymbolManagerResult(data.message, 'success');
            document.getElementById('symbolsToAdd').value = '';
            this.loadSymbolStats(); // Refresh stats
            this.loadMissingTokens(); // Refresh missing tokens list
            this.addLog('SYMBOLS', data.message, 'success');
        } else {
            this.displaySymbolManagerResult(data.error, 'error');
        }
    } catch (error) {
        console.error('Error adding symbols:', error);
        this.displaySymbolManagerResult('Failed to add symbols', 'error');
    }
};

TradingDashboard.prototype.deleteSymbols = async function() {
    try {
        const symbolsText = document.getElementById('symbolsToDelete').value.trim();
        if (!symbolsText) {
            alert('Please enter symbols to delete');
            return;
        }

        const symbols = symbolsText.split('\n').map(s => s.trim()).filter(s => s);

        // Confirmation dialog
        const confirmMessage = `Are you sure you want to permanently delete ${symbols.length} symbols?\n\nSymbols to delete:\n${symbols.join(', ')}\n\nThis will remove them from:\n- Excel file\n- Database\n- Token mappings\n- All connections\n\nThis action cannot be undone!`;

        if (!confirm(confirmMessage)) {
            return;
        }

        const response = await fetch('/api/symbols/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ symbols })
        });

        const data = await response.json();

        if (data.success) {
            this.displaySymbolManagerResult(data.message, 'success');
            document.getElementById('symbolsToDelete').value = '';
            this.loadSymbolStats(); // Refresh stats
            this.loadMissingTokens(); // Refresh missing tokens list
            this.addLog('SYMBOLS', `🗑️ ${data.message}`, 'warning');
        } else {
            this.displaySymbolManagerResult(data.error, 'error');
        }
    } catch (error) {
        console.error('Error deleting symbols:', error);
        this.displaySymbolManagerResult('Failed to delete symbols', 'error');
    }
};

// Helper display methods
TradingDashboard.prototype.displayIntegrityResults = function(results) {
    const container = document.getElementById('integrityResults');
    container.innerHTML = `
        <div class="alert alert-info">
            <h6><i class="fas fa-check-circle me-2"></i>Integrity Check Results</h6>
            <pre>${JSON.stringify(results, null, 2)}</pre>
        </div>
    `;
};

TradingDashboard.prototype.displaySymbolStats = function(stats) {
    const container = document.getElementById('symbolStats');
    container.innerHTML = `
        <div class="row text-center">
            <div class="col-6">
                <div class="metric-value text-primary">${stats.total_symbols || 0}</div>
                <div class="metric-label">Total</div>
            </div>
            <div class="col-6">
                <div class="metric-value text-success">${stats.symbols_with_tokens || 0}</div>
                <div class="metric-label">With Tokens</div>
            </div>
            <div class="col-6">
                <div class="metric-value text-warning">${stats.symbols_without_tokens || 0}</div>
                <div class="metric-label">Missing</div>
            </div>
            <div class="col-6">
                <div class="metric-value text-info">${(stats.coverage_percentage || 0).toFixed(1)}%</div>
                <div class="metric-label">Coverage</div>
            </div>
        </div>
    `;
};

TradingDashboard.prototype.loadMissingTokens = async function() {
    try {
        const response = await fetch('/api/symbols/missing-tokens');
        const data = await response.json();

        if (data.success) {
            this.displayMissingTokens(data);
        } else {
            this.showError('missingTokensSection', data.error);
        }
    } catch (error) {
        console.error('Error loading missing tokens:', error);
        this.showError('missingTokensSection', 'Failed to load missing tokens');
    }
};

TradingDashboard.prototype.displayMissingTokens = function(data) {
    const container = document.getElementById('missingTokensSection');

    if (data.count === 0) {
        container.innerHTML = `
            <div class="text-center text-success">
                <i class="fas fa-check-circle me-2"></i>
                <strong>All symbols have tokens!</strong>
                <p class="mb-0 text-muted">100% coverage achieved</p>
            </div>
        `;
        return;
    }

    container.innerHTML = `
        <div class="mb-3">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="text-warning">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    <strong>${data.count} symbols missing tokens</strong>
                </span>
                <span class="badge bg-warning">${data.coverage_percentage}% coverage</span>
            </div>
        </div>
        <div class="missing-tokens-list" style="max-height: 200px; overflow-y: auto;">
            ${data.missing_tokens.map(symbol => `
                <div class="d-flex justify-content-between align-items-center py-1 border-bottom">
                    <span class="text-monospace">${symbol}</span>
                    <button class="btn btn-outline-primary btn-sm" onclick="dashboard.copySymbolName('${symbol}')" title="Copy symbol name">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            `).join('')}
        </div>
        <div class="mt-2 text-muted small">
            <i class="fas fa-info-circle me-1"></i>
            Click the copy button to copy symbol names for token lookup
        </div>
    `;
};

TradingDashboard.prototype.copySymbolName = function(symbolName) {
    navigator.clipboard.writeText(symbolName).then(() => {
        this.addLog('SYMBOL', `Copied symbol name: ${symbolName}`, 'info');
        // Show temporary success feedback
        const button = event.target.closest('button');
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check text-success"></i>';
        setTimeout(() => {
            button.innerHTML = originalHTML;
        }, 1000);
    }).catch(err => {
        console.error('Failed to copy symbol name:', err);
        this.addLog('SYMBOL', `Failed to copy symbol name: ${symbolName}`, 'error');
    });
};

TradingDashboard.prototype.displaySymbolManagerResult = function(message, type) {
    const container = document.getElementById('symbolManagerResults');
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';

    container.innerHTML = `
        <div class="alert ${alertClass}">
            <i class="${icon} me-2"></i>${message}
        </div>
    `;

    // Auto-hide after 5 seconds
    setTimeout(() => {
        container.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-info-circle me-1"></i>
                Perform actions to see results
            </div>
        `;
    }, 5000);
};

TradingDashboard.prototype.displayRemovalResults = function(message) {
    const container = document.getElementById('duplicateResults');
    container.innerHTML = `
        <div class="alert alert-success">
            <h6><i class="fas fa-check-circle me-2"></i>Duplicates Removed</h6>
            <p>${message}</p>
            <div class="mt-2">
                <small class="text-muted">
                    <i class="fas fa-database me-1"></i>Database cleanup completed successfully
                </small>
            </div>
        </div>
    `;
};

TradingDashboard.prototype.showLoading = function(containerId, message) {
    const container = document.getElementById(containerId);
    container.innerHTML = `
        <div class="text-center">
            <div class="loading-spinner"></div>
            <p class="mt-2">${message}</p>
        </div>
    `;
};

TradingDashboard.prototype.showError = function(containerId, error) {
    const container = document.getElementById(containerId);
    container.innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>${error}
        </div>
    `;
};

// Paper Trading Methods
TradingDashboard.prototype.initializePaperTradingFeatures = function() {
    document.getElementById('downloadPaperTradesBtn').addEventListener('click', () => {
        this.downloadPaperTrades();
    });

    document.getElementById('clearPaperTradesBtn').addEventListener('click', () => {
        this.clearPaperTrades();
    });



    // Load initial data
    this.loadPaperTrades();

    // Auto-refresh paper trades every 10 seconds
    setInterval(() => {
        this.loadPaperTrades();
    }, 10000);

    // Auto-refresh portfolio every 5 seconds
    setInterval(() => {
        this.loadPortfolioStatus();
    }, 5000);
};

TradingDashboard.prototype.loadPaperTrades = async function() {
    try {
        const response = await fetch('/api/paper-trades');
        const data = await response.json();

        if (data.success) {
            this.displayPaperTrades(data.trades);
            this.updatePaperTradingStats(data.trades);
            this.addLog('PAPER', `Loaded ${data.count} paper trades`, 'info');
        } else {
            this.showError('paperTradesTableBody', data.error);
        }
    } catch (error) {
        console.error('Error loading paper trades:', error);
        this.showError('paperTradesTableBody', 'Failed to load paper trades');
    }
};

TradingDashboard.prototype.downloadPaperTrades = function() {
    window.open('/api/paper-trades/download', '_blank');
    this.addLog('PAPER', 'Paper trades CSV download initiated', 'info');
};

TradingDashboard.prototype.clearPaperTrades = async function() {
    try {
        if (!confirm('Are you sure you want to clear all paper trading records? This action cannot be undone.')) {
            return;
        }

        const response = await fetch('/api/paper-trades/clear', { method: 'POST' });
        const data = await response.json();

        if (data.success) {
            this.loadPaperTrades(); // Refresh the display
            this.addLog('PAPER', 'Paper trading records cleared', 'warning');
        } else {
            alert('Failed to clear paper trades: ' + data.error);
        }
    } catch (error) {
        console.error('Error clearing paper trades:', error);
        alert('Failed to clear paper trades');
    }
};

TradingDashboard.prototype.completeResetPaperTrades = async function() {
    try {
        // First confirmation with detailed warning
        const confirmMessage = `🚨 COMPLETE TRADING SYSTEM RESET 🚨

⚠️ DANGER: This will permanently DELETE ALL trading data:

📊 PORTFOLIO DATA:
• Reset vault amount to ₹0.00
• Clear all ${this.getActivePositionsCount()} active positions
• Clear all trading signals and history
• Reset portfolio metrics to zero

💾 DATABASE OPERATIONS:
• Delete all records from trading_positions table
• Delete all records from trading_signals table
• Reset portfolio_status table
• Clear all paper trading records

💰 FINANCIAL IMPACT:
• Total Pot remains: ₹2.22 Cr (unchanged)
• Used Funds reset to: ₹0
• Available Pot reset to: ₹2.22 Cr
• All investments will be "returned" to pot

⚠️ THIS ACTION CANNOT BE UNDONE! ⚠️

Type "RESET" to confirm you understand the consequences:`;

        const userInput = prompt(confirmMessage);
        if (userInput !== 'RESET') {
            this.addLog('SYSTEM', '❌ Reset cancelled - incorrect confirmation', 'info');
            return;
        }

        // Second confirmation with final warning
        const finalConfirm = `🔥 FINAL CONFIRMATION 🔥

You typed "RESET" correctly.

This is your LAST CHANCE to cancel!

Current Status:
• Active Positions: ${this.getActivePositionsCount()}
• Used Funds: ₹${this.getUsedFunds()}
• Vault Amount: ₹${this.getVaultAmount()}

After reset:
• Active Positions: 0
• Used Funds: ₹0
• Vault Amount: ₹0
• Available Pot: ₹2.22 Cr

Click OK to PERMANENTLY DELETE all trading data.
Click Cancel to abort.`;

        if (!confirm(finalConfirm)) {
            this.addLog('SYSTEM', '❌ Reset cancelled at final confirmation', 'info');
            return;
        }

        this.addLog('SYSTEM', '🔄 Starting COMPLETE TRADING SYSTEM RESET...', 'warning');
        this.addLog('SYSTEM', '⚠️ Deleting all trading positions, signals, and portfolio data...', 'warning');

        const response = await fetch('/api/paper-trades/complete-reset', { method: 'POST' });
        const data = await response.json();

        if (data.success) {
            // Refresh all displays
            this.loadPaperTrades();
            this.loadPortfolioStatus();

            this.addLog('SYSTEM', '✅ COMPLETE RESET SUCCESSFUL!', 'success');
            this.addLog('SYSTEM', `✅ ${data.message}`, 'success');
            this.addLog('SYSTEM', '💰 Portfolio reset: ₹2.22 Cr available, ₹0 used', 'success');

            // Show detailed success message
            alert(`✅ COMPLETE RESET SUCCESSFUL!

🔄 All trading data has been permanently deleted:
• Trading positions: CLEARED
• Trading signals: CLEARED
• Vault amount: RESET to ₹0
• Portfolio metrics: RESET

💰 Financial Status:
• Total Pot: ₹2.22 Cr (unchanged)
• Available Pot: ₹2.22 Cr
• Used Funds: ₹0
• Active Positions: 0

🚀 System is ready for fresh trading!`);
        } else {
            alert('❌ Failed to perform complete reset: ' + data.error);
            this.addLog('SYSTEM', '❌ Complete reset failed: ' + data.error, 'error');
        }
    } catch (error) {
        console.error('Error performing complete reset:', error);
        alert('❌ Failed to perform complete reset: ' + error.message);
        this.addLog('SYSTEM', '❌ Complete reset failed: ' + error.message, 'error');
    }
};

// Helper methods for getting current status
TradingDashboard.prototype.getActivePositionsCount = function() {
    const element = document.getElementById('detailedActivePositions');
    return element ? element.textContent || '0' : '0';
};

TradingDashboard.prototype.getUsedFunds = function() {
    const element = document.getElementById('detailedUsedFunds');
    return element ? element.textContent.replace('₹', '').replace(',', '') || '0' : '0';
};

TradingDashboard.prototype.getVaultAmount = function() {
    const element = document.getElementById('detailedVaultAmount');
    return element ? element.textContent.replace('₹', '').replace(',', '') || '0' : '0';
};

TradingDashboard.prototype.displayPaperTrades = function(trades) {
    const tbody = document.getElementById('paperTradesTableBody');

    if (trades.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center text-muted">
                    <i class="fas fa-info-circle me-2"></i>
                    No paper trades found
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = trades.map((trade, index) => `
        <tr>
            <td><strong>${trade['Symbol Name']}</strong></td>
            <td>${trade['Date Time'] || trade['Date'] || 'N/A'}</td>
            <td>₹${parseFloat(trade['Price']).toFixed(2)}</td>
            <td>
                <span class="badge ${trade['Signals'] === 'BUY' ? 'bg-success' : 'bg-warning'}">
                    ${trade['Signals']}
                </span>
            </td>
            <td>
                <small class="text-muted" style="font-family: monospace;">
                    ${trade['API Call Link'].substring(0, 50)}...
                </small>
            </td>
            <td>
                <div class="btn-group btn-group-sm" role="group">
                    <button class="btn btn-outline-warning btn-sm"
                            onclick="dashboard.forceHold('${trade['Symbol Name']}')"
                            title="Force HOLD - Stop any pending actions">
                        <i class="fas fa-pause"></i> HOLD
                    </button>
                    <button class="btn btn-outline-success btn-sm"
                            onclick="dashboard.forceBuy('${trade['Symbol Name']}', ${trade['Price']})"
                            title="Force BUY - Execute immediate paper BUY">
                        <i class="fas fa-arrow-up"></i> BUY
                    </button>
                    <button class="btn btn-outline-danger btn-sm"
                            onclick="dashboard.forceSell('${trade['Symbol Name']}', ${trade['Price']})"
                            title="Force SELL - Execute immediate paper SELL">
                        <i class="fas fa-arrow-down"></i> SELL
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
};

TradingDashboard.prototype.refreshPaperTradingIfVisible = function() {
    // Only refresh if paper trading tab is active
    const paperTradingTab = document.getElementById('paper-trading');
    if (paperTradingTab && paperTradingTab.classList.contains('active')) {
        this.loadPaperTrades();
    }
};

TradingDashboard.prototype.updatePaperTradingStats = function(trades) {
    const totalTrades = trades.length;
    const buySignals = trades.filter(t => t['Signals'] === 'BUY').length;
    const sellSignals = trades.filter(t => t['Signals'] === 'SELL').length;
    const uniqueSymbols = new Set(trades.map(t => t['Symbol Name'])).size;

    document.getElementById('totalPaperTrades').textContent = totalTrades;
    document.getElementById('paperBuySignals').textContent = buySignals;
    document.getElementById('paperSellSignals').textContent = sellSignals;
    document.getElementById('uniqueSymbols').textContent = uniqueSymbols;
};

// Force Control Methods for Paper Trading
TradingDashboard.prototype.forceHold = async function(symbol) {
    try {
        if (!confirm(`Are you sure you want to FORCE HOLD for ${symbol}?\n\nThis will cancel any pending confirmations and prevent automatic trading.`)) {
            return;
        }

        const response = await fetch('/api/force-control', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: 'HOLD',
                symbol: symbol
            })
        });

        const data = await response.json();

        if (data.success) {
            this.addLog('FORCE', `🟡 FORCE HOLD executed for ${symbol}`, 'warning');
            this.loadPaperTrades(); // Refresh display
        } else {
            alert(`Failed to execute FORCE HOLD: ${data.error}`);
        }
    } catch (error) {
        console.error('Error executing force hold:', error);
        alert('Failed to execute FORCE HOLD');
    }
};

TradingDashboard.prototype.forceBuy = async function(symbol, price) {
    try {
        if (!confirm(`Are you sure you want to FORCE BUY ${symbol} at ₹${price}?\n\nThis will execute immediate paper BUY regardless of confirmations.`)) {
            return;
        }

        const response = await fetch('/api/force-control', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: 'BUY',
                symbol: symbol,
                price: parseFloat(price)
            })
        });

        const data = await response.json();

        if (data.success) {
            this.addLog('FORCE', `🟢 FORCE BUY executed for ${symbol} @ ₹${price}`, 'success');
            this.loadPaperTrades(); // Refresh display
            this.loadPortfolioStatus(); // Refresh portfolio
        } else {
            alert(`Failed to execute FORCE BUY: ${data.error}`);
        }
    } catch (error) {
        console.error('Error executing force buy:', error);
        alert('Failed to execute FORCE BUY');
    }
};

TradingDashboard.prototype.forceSell = async function(symbol, price) {
    try {
        if (!confirm(`Are you sure you want to FORCE SELL ${symbol} at ₹${price}?\n\nThis will execute immediate paper SELL regardless of confirmations.`)) {
            return;
        }

        const response = await fetch('/api/force-control', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: 'SELL',
                symbol: symbol,
                price: parseFloat(price)
            })
        });

        const data = await response.json();

        if (data.success) {
            this.addLog('FORCE', `🔴 FORCE SELL executed for ${symbol} @ ₹${price}`, 'danger');
            this.loadPaperTrades(); // Refresh display
            this.loadPortfolioStatus(); // Refresh portfolio
        } else {
            alert(`Failed to execute FORCE SELL: ${data.error}`);
        }
    } catch (error) {
        console.error('Error executing force sell:', error);
        alert('Failed to execute FORCE SELL');
    }
};

// Layer 2 Confirmation Status Methods
TradingDashboard.prototype.loadConfirmationStatus = async function() {
    try {
        const response = await fetch('/api/confirmation-status');
        const data = await response.json();

        if (data.success) {
            this.displayConfirmationStatus(data);
        } else {
            console.error('Error loading confirmation status:', data.error);
        }
    } catch (error) {
        console.error('Error loading confirmation status:', error);
    }
};

TradingDashboard.prototype.displayConfirmationStatus = function(data) {
    // Update metrics
    document.getElementById('pendingBuyConfirmations').textContent = data.pending_buy || 0;
    document.getElementById('pendingSellConfirmations').textContent = data.pending_sell || 0;
    document.getElementById('confirmedTrades').textContent = data.confirmed_today || 0;

    // Update timestamp
    const timestamp = new Date().toLocaleTimeString();
    document.getElementById('confirmationTimestamp').textContent = `Last updated: ${timestamp}`;

    // Display pending confirmations details
    const confirmationDetails = document.getElementById('confirmationDetails');

    if (data.pending_confirmations && data.pending_confirmations.length > 0) {
        confirmationDetails.innerHTML = `
            <div class="table-responsive">
                <table class="table table-sm table-striped">
                    <thead>
                        <tr>
                            <th>Symbol</th>
                            <th>Type</th>
                            <th>Trigger Price</th>
                            <th>Progress</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.pending_confirmations.map(conf => `
                            <tr>
                                <td><strong>${conf.symbol}</strong></td>
                                <td>
                                    <span class="badge ${conf.signal_type === 'BUY' ? 'bg-success' : 'bg-warning'}">
                                        ${conf.signal_type}
                                    </span>
                                </td>
                                <td>₹${conf.trigger_price}</td>
                                <td>
                                    <div class="progress" style="height: 15px;">
                                        <div class="progress-bar" style="width: ${(conf.confirmations_received / 3) * 100}%">
                                            ${conf.confirmations_received}/3
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        ${conf.time_elapsed}
                                    </small>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    } else {
        confirmationDetails.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-info-circle me-1"></i>
                No pending confirmations
            </div>
        `;
    }
};

// Layer 2 Confirmation Status Methods
TradingDashboard.prototype.loadConfirmationStatus = async function() {
    try {
        const response = await fetch('/api/confirmation-status');
        const data = await response.json();

        if (data.success) {
            this.displayConfirmationStatus(data);
        } else {
            console.error('Error loading confirmation status:', data.error);
        }
    } catch (error) {
        console.error('Error loading confirmation status:', error);
    }
};

TradingDashboard.prototype.displayConfirmationStatus = function(data) {
    // Update metrics
    document.getElementById('pendingBuyConfirmations').textContent = data.pending_buy || 0;
    document.getElementById('pendingSellConfirmations').textContent = data.pending_sell || 0;
    document.getElementById('confirmedTrades').textContent = data.confirmed_today || 0;

    // Update timestamp
    const timestamp = new Date().toLocaleTimeString();
    document.getElementById('confirmationTimestamp').textContent = `Last updated: ${timestamp}`;

    // Display pending confirmations details
    const confirmationDetails = document.getElementById('confirmationDetails');

    if (data.pending_confirmations && data.pending_confirmations.length > 0) {
        confirmationDetails.innerHTML = `
            <div class="table-responsive">
                <table class="table table-sm table-striped">
                    <thead>
                        <tr>
                            <th>Symbol</th>
                            <th>Type</th>
                            <th>Trigger Price</th>
                            <th>Progress</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.pending_confirmations.map(conf => `
                            <tr>
                                <td><strong>${conf.symbol}</strong></td>
                                <td>
                                    <span class="badge ${conf.signal_type === 'BUY' ? 'bg-success' : 'bg-warning'}">
                                        ${conf.signal_type}
                                    </span>
                                </td>
                                <td>₹${conf.trigger_price}</td>
                                <td>
                                    <div class="progress" style="height: 15px;">
                                        <div class="progress-bar" style="width: ${(conf.confirmations_received / 3) * 100}%">
                                            ${conf.confirmations_received}/3
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        ${conf.time_elapsed}
                                    </small>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    } else {
        confirmationDetails.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-info-circle me-1"></i>
                No pending confirmations
            </div>
        `;
    }
};

TradingDashboard.prototype.loadPriorityQueueStatus = async function() {
    try {
        const response = await fetch('/api/priority-queue/status');
        const data = await response.json();

        if (data.success) {
            this.displayPriorityBatches(data);
        } else {
            console.error('Error loading priority queue status:', data.error);
        }
    } catch (error) {
        console.error('Error fetching priority queue status:', error);
    }
};

TradingDashboard.prototype.displayPriorityBatches = function(data) {
    const container = document.getElementById('priorityBatches');
    const timestampElement = document.getElementById('priorityTimestamp');

    // Update timestamp
    timestampElement.textContent = `Last updated: ${data.timestamp}`;

    // Create batch display
    const batchOrder = ['GOLD', 'SILVER', 'BRONZE', 'REMAINING'];
    const batchEmojis = {'GOLD': '🥇', 'SILVER': '🥈', 'BRONZE': '🥉', 'REMAINING': '⚪'};
    const batchColors = {'GOLD': 'warning', 'SILVER': 'info', 'BRONZE': 'secondary', 'REMAINING': 'light'};

    let html = '';

    batchOrder.forEach(tier => {
        const batch = data.batches[tier];
        const emoji = batchEmojis[tier];
        const color = batchColors[tier];

        html += `
            <div class="col-md-3 mb-3">
                <div class="card border-${color}">
                    <div class="card-header bg-${color} text-dark">
                        <h6 class="mb-0">${emoji} ${tier}</h6>
                        <small>${batch.count} symbols</small>
                    </div>
                    <div class="card-body p-2" style="max-height: 200px; overflow-y: auto;">
        `;

        if (batch.count === 0) {
            html += '<small class="text-muted">No symbols</small>';
        } else {
            // Show first 10 symbols
            const symbolsToShow = batch.symbols.slice(0, 10);
            symbolsToShow.forEach(symbol => {
                let symbolInfo = `<div class="small mb-1"><strong>${symbol.symbol}</strong>`;

                if (symbol.position_status) {
                    const profitColor = symbol.profit_amount >= 0 ? 'text-success' : 'text-danger';
                    symbolInfo += `<br><span class="text-muted">${symbol.position_status}</span>`;
                    symbolInfo += `<br><span class="${profitColor}">₹${symbol.profit_amount.toFixed(0)}</span>`;
                } else {
                    symbolInfo += `<br><span class="text-muted">${symbol.pattern_status}</span>`;
                }

                if (symbol.last_price) {
                    symbolInfo += `<br><span class="text-info">₹${symbol.last_price.toFixed(2)}</span>`;
                }

                symbolInfo += '</div>';
                html += symbolInfo;
            });

            if (batch.count > 10) {
                html += `<small class="text-muted">... and ${batch.count - 10} more</small>`;
            }
        }

        html += `
                    </div>
                </div>
            </div>
        `;
    });

    // Add summary
    html += `
        <div class="col-12">
            <div class="alert alert-info">
                <strong>📊 Summary:</strong> ${data.total_symbols}/${data.expected_symbols} symbols analyzed
                ${data.total_symbols !== data.expected_symbols ?
                    `<span class="text-warning">(${data.expected_symbols - data.total_symbols} missing)</span>` :
                    '<span class="text-success">✅ All symbols included</span>'
                }
            </div>
        </div>
    `;

    container.innerHTML = html;
};

TradingDashboard.prototype.stopRealtimeFetcher = async function() {
    try {
        const response = await fetch('/api/realtime-fetcher/stop', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            this.showAlert('success', '🛑 Realtime fetcher stopped successfully');
            console.log('Realtime fetcher stopped:', data.message);
        } else {
            this.showAlert('danger', `Failed to stop realtime fetcher: ${data.error || 'Unknown error'}`);
        }
    } catch (error) {
        console.error('Error stopping realtime fetcher:', error);
        this.showAlert('danger', 'Error stopping realtime fetcher');
    }
};









// Initialize dashboard when page loads
let dashboard;
document.addEventListener('DOMContentLoaded', () => {
    dashboard = new TradingDashboard();
    window.dashboard = dashboard; // Make it globally accessible
});
