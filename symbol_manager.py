"""
Symbol Management System
Add, delete, and manage symbols in the trading system
"""
import pandas as pd
import os
import logging
from typing import List, Dict, Optional
from symbol_processor import SymbolProcessor

class SymbolManager:
    def __init__(self, excel_file: str = "paper_trade.xlsx"):
        self.excel_file = excel_file
        self.symbol_processor = SymbolProcessor()
        self.logger = self._setup_logging()
        
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/symbol_manager.log'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    def load_symbols_df(self) -> pd.DataFrame:
        """Load symbols from Excel file"""
        try:
            df = pd.read_excel(self.excel_file)
            self.logger.info(f"Loaded {len(df)} symbols from {self.excel_file}")
            return df
        except Exception as e:
            self.logger.error(f"Error loading Excel file: {e}")
            raise
    
    def save_symbols_df(self, df: pd.DataFrame) -> bool:
        """Save symbols DataFrame to Excel file"""
        try:
            # Create backup first
            backup_file = f"{self.excel_file}.backup"
            if os.path.exists(self.excel_file):
                import shutil
                shutil.copy2(self.excel_file, backup_file)
                self.logger.info(f"Created backup: {backup_file}")
            
            # Save new file
            df.to_excel(self.excel_file, index=False)
            self.logger.info(f"Saved {len(df)} symbols to {self.excel_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving Excel file: {e}")
            return False
    
    def get_current_symbols(self) -> List[str]:
        """Get list of current symbols"""
        df = self.load_symbols_df()
        return df['Symbol'].tolist()
    
    def add_symbols(self, symbols: List[str], company_names: Optional[List[str]] = None) -> bool:
        """Add new symbols to the system"""
        try:
            df = self.load_symbols_df()
            
            # Check for duplicates
            existing_symbols = set(df['Symbol'].str.upper())
            new_symbols = []
            duplicate_symbols = []
            
            for i, symbol in enumerate(symbols):
                symbol = symbol.upper().strip()
                if symbol in existing_symbols:
                    duplicate_symbols.append(symbol)
                else:
                    new_row = {
                        'Symbol': symbol,
                        'Company Name': company_names[i] if company_names and i < len(company_names) else '',
                        'Industry': '',
                        'Series': 'EQ',
                        'ISIN Code': ''
                    }
                    new_symbols.append(new_row)
            
            if duplicate_symbols:
                self.logger.warning(f"Duplicate symbols found (skipped): {duplicate_symbols}")
            
            if new_symbols:
                # Add new symbols
                new_df = pd.concat([df, pd.DataFrame(new_symbols)], ignore_index=True)

                if self.save_symbols_df(new_df):
                    # Auto-generate tokens for new symbols
                    for symbol_data in new_symbols:
                        symbol = symbol_data['Symbol']
                        token = self.symbol_processor.add_symbol_with_auto_token(symbol)
                        self.logger.info(f"Generated token {token} for symbol {symbol}")

                    self.logger.info(f"Added {len(new_symbols)} new symbols with auto-generated tokens: {[s['Symbol'] for s in new_symbols]}")
                    return True
            else:
                self.logger.info("No new symbols to add")
                return True
                
        except Exception as e:
            self.logger.error(f"Error adding symbols: {e}")
            return False
    
    def delete_symbols(self, symbols: List[str]) -> bool:
        """Delete symbols completely from the system (Excel + Tokens + DB1 + DB2)"""
        try:
            self.logger.info(f"🗑️ STARTING COMPLETE SYMBOL DELETION: {symbols}")

            df = self.load_symbols_df()

            # Convert to uppercase for comparison
            symbols_to_delete = [s.upper().strip() for s in symbols]

            # Verify which symbols exist
            existing_symbols = set(df['Symbol'].str.upper())
            found_symbols = [s for s in symbols_to_delete if s in existing_symbols]
            not_found_symbols = [s for s in symbols_to_delete if s not in existing_symbols]

            if not_found_symbols:
                self.logger.warning(f"⚠️ Symbols not found in Excel: {not_found_symbols}")

            if not found_symbols:
                self.logger.info(f"📋 Symbols to delete: {found_symbols}")

                # Step 1: Remove from Excel file
                initial_count = len(df)
                df_filtered = df[~df['Symbol'].str.upper().isin(symbols_to_delete)]
                deleted_count = initial_count - len(df_filtered)

                if self.save_symbols_df(df_filtered):
                    self.logger.info(f"✅ EXCEL: Removed {deleted_count} symbols from Excel file")

                    # Step 2: Remove from token file
                    self.logger.info(f"🔑 TOKENS: Removing tokens for {len(found_symbols)} symbols...")
                    self._remove_tokens(symbols_to_delete)

                    # Step 3: Remove from both databases
                    self.logger.info(f"🗄️ DATABASES: Removing from DB1 and DB2...")
                    self._remove_from_databases(symbols_to_delete)

                    self.logger.info(f"🎉 DELETION COMPLETE: Successfully removed {found_symbols} from all systems")
                    return True
                else:
                    self.logger.error(f"❌ Failed to save Excel file after deletion")
                    return False
            else:
                self.logger.info("ℹ️ No symbols found to delete")
                return True

        except Exception as e:
            self.logger.error(f"❌ Error deleting symbols: {e}")
            return False
    
    def _remove_tokens(self, symbols: List[str]):
        """Remove tokens for deleted symbols"""
        try:
            token_file = "Data/angelone_tokens.txt"
            if not os.path.exists(token_file):
                self.logger.warning(f"⚠️ Token file not found: {token_file}")
                return

            # Read existing tokens
            with open(token_file, 'r') as f:
                lines = f.readlines()

            initial_token_count = len([line for line in lines if line.strip() and ':' in line])

            # Filter out deleted symbols
            symbols_to_delete = set(s.upper() for s in symbols)
            filtered_lines = []
            removed_tokens = []

            for line in lines:
                line_stripped = line.strip()
                if line_stripped and ':' in line_stripped:
                    symbol = line_stripped.split(':', 1)[0].strip().upper()
                    if symbol not in symbols_to_delete:
                        filtered_lines.append(line)
                    else:
                        removed_tokens.append(line_stripped)
                else:
                    # Keep empty lines and comments
                    filtered_lines.append(line)

            # Write back filtered tokens
            with open(token_file, 'w') as f:
                f.writelines(filtered_lines)

            final_token_count = len([line for line in filtered_lines if line.strip() and ':' in line])
            tokens_removed = initial_token_count - final_token_count

            if removed_tokens:
                self.logger.info(f"✅ TOKENS: Removed {tokens_removed} token mappings:")
                for token in removed_tokens:
                    self.logger.info(f"   🔑 {token}")
            else:
                self.logger.info(f"ℹ️ TOKENS: No tokens found to remove for {symbols}")

        except Exception as e:
            self.logger.error(f"❌ Error removing tokens: {e}")

    def _remove_from_databases(self, symbols: List[str]):
        """Remove symbols from both DB1 and DB2 completely"""
        try:
            import sqlite3

            # Remove from DB1 (market data)
            db1_removed = 0
            try:
                conn1 = sqlite3.connect('Data/trading_data.db')
                cursor1 = conn1.cursor()

                # Get all tables in DB1
                cursor1.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor1.fetchall()]

                for symbol in symbols:
                    symbol_removed = 0
                    for table in tables:
                        try:
                            # Check if table has symbol column
                            cursor1.execute(f"PRAGMA table_info({table})")
                            columns = [col[1] for col in cursor1.fetchall()]

                            if 'symbol' in columns:
                                # Count records before deletion
                                cursor1.execute(f'SELECT COUNT(*) FROM {table} WHERE symbol = ?', (symbol,))
                                count_before = cursor1.fetchone()[0]

                                # Delete records
                                cursor1.execute(f'DELETE FROM {table} WHERE symbol = ?', (symbol,))
                                deleted = cursor1.rowcount

                                if deleted > 0:
                                    symbol_removed += deleted
                                    self.logger.info(f"Removed {deleted} records of {symbol} from DB1.{table}")

                        except Exception as e:
                            self.logger.error(f"Error removing {symbol} from DB1.{table}: {e}")
                            continue

                    db1_removed += symbol_removed
                    if symbol_removed > 0:
                        self.logger.info(f"Total removed for {symbol} from DB1: {symbol_removed} records")

                conn1.commit()
                conn1.close()
                self.logger.info(f"✅ DB1 Cleanup Complete: Removed {db1_removed} total records")

            except Exception as e:
                self.logger.error(f"Error removing from DB1: {e}")

            # Remove from DB2 (trading operations)
            db2_removed = 0
            try:
                conn2 = sqlite3.connect('Data/trading_operations.db')
                cursor2 = conn2.cursor()

                # Get all tables in DB2
                cursor2.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor2.fetchall()]

                for symbol in symbols:
                    symbol_removed = 0
                    for table in tables:
                        try:
                            # Check if table has symbol column
                            cursor2.execute(f"PRAGMA table_info({table})")
                            columns = [col[1] for col in cursor2.fetchall()]

                            if 'symbol' in columns:
                                # Count records before deletion
                                cursor2.execute(f'SELECT COUNT(*) FROM {table} WHERE symbol = ?', (symbol,))
                                count_before = cursor2.fetchone()[0]

                                # Delete records
                                cursor2.execute(f'DELETE FROM {table} WHERE symbol = ?', (symbol,))
                                deleted = cursor2.rowcount

                                if deleted > 0:
                                    symbol_removed += deleted
                                    self.logger.info(f"Removed {deleted} records of {symbol} from DB2.{table}")

                        except Exception as e:
                            self.logger.error(f"Error removing {symbol} from DB2.{table}: {e}")
                            continue

                    db2_removed += symbol_removed
                    if symbol_removed > 0:
                        self.logger.info(f"Total removed for {symbol} from DB2: {symbol_removed} records")

                conn2.commit()
                conn2.close()
                self.logger.info(f"✅ DB2 Cleanup Complete: Removed {db2_removed} total records")

            except Exception as e:
                self.logger.error(f"Error removing from DB2: {e}")

            # Summary
            total_removed = db1_removed + db2_removed
            self.logger.info(f"🗑️ COMPLETE DATABASE CLEANUP: Removed {total_removed} total records (DB1: {db1_removed}, DB2: {db2_removed})")

        except Exception as e:
            self.logger.error(f"Error removing from databases: {e}")

    def get_all_symbols(self) -> List[str]:
        """Get all symbols from Excel file"""
        try:
            df = self.load_symbols_df()
            return df['Symbol'].tolist()
        except Exception as e:
            self.logger.error(f"Error getting all symbols: {e}")
            return []

    def get_symbols_without_tokens(self) -> List[str]:
        """Get symbols that don't have tokens"""
        try:
            all_symbols = self.get_all_symbols()
            validation = self.symbol_processor.validate_symbols()
            return validation.get('invalid', [])
        except Exception as e:
            self.logger.error(f"Error getting symbols without tokens: {e}")
            return []

    def get_symbols_with_tokens(self) -> List[tuple]:
        """Get all symbols with their corresponding tokens"""
        try:
            return self.symbol_processor.get_all_symbols_with_tokens()
        except Exception as e:
            self.logger.error(f"Error getting symbols with tokens: {e}")
            return []

    def update_symbol_info(self, symbol: str, company_name: str = None, industry: str = None) -> bool:
        """Update symbol information"""
        try:
            df = self.load_symbols_df()
            symbol = symbol.upper().strip()
            
            # Find symbol
            mask = df['Symbol'].str.upper() == symbol
            if not mask.any():
                self.logger.error(f"Symbol {symbol} not found")
                return False
            
            # Update fields
            if company_name:
                df.loc[mask, 'Company Name'] = company_name
            if industry:
                df.loc[mask, 'Industry'] = industry
            
            if self.save_symbols_df(df):
                self.logger.info(f"Updated symbol {symbol}")
                return True
                
        except Exception as e:
            self.logger.error(f"Error updating symbol: {e}")
            return False
    
    def search_symbols(self, query: str) -> List[Dict]:
        """Search symbols by name or company"""
        try:
            df = self.load_symbols_df()
            query = query.upper()
            
            # Search in symbol and company name
            mask = (df['Symbol'].str.upper().str.contains(query, na=False) | 
                   df['Company Name'].str.upper().str.contains(query, na=False))
            
            results = df[mask].to_dict('records')
            self.logger.info(f"Found {len(results)} symbols matching '{query}'")
            return results
            
        except Exception as e:
            self.logger.error(f"Error searching symbols: {e}")
            return []
    
    def get_symbol_stats(self) -> Dict:
        """Get symbol statistics"""
        try:
            df = self.load_symbols_df()
            validation = self.symbol_processor.validate_symbols()
            
            stats = {
                'total_symbols': len(df),
                'symbols_with_tokens': validation['valid_count'],
                'symbols_without_tokens': validation['invalid_count'],
                'coverage_percentage': (validation['valid_count'] / len(df)) * 100,
                'missing_symbols': validation['invalid']
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Error getting stats: {e}")
            return {}
    
    def clean_duplicate_symbols(self) -> bool:
        """Remove duplicate symbols from Excel, DB1, and DB2"""
        try:
            # Clean Excel file
            df = self.load_symbols_df()
            initial_count = len(df)

            # Remove duplicates based on Symbol column
            df_cleaned = df.drop_duplicates(subset=['Symbol'], keep='first')
            excel_duplicate_count = initial_count - len(df_cleaned)

            if excel_duplicate_count > 0:
                if self.save_symbols_df(df_cleaned):
                    self.logger.info(f"Removed {excel_duplicate_count} duplicate symbols from Excel")
            else:
                self.logger.info("No duplicate symbols found in Excel")

            # Clean databases
            db1_duplicates = self._clean_database_duplicates('Data/trading_data.db', 'DB1')
            db2_duplicates = self._clean_database_duplicates('Data/trading_operations.db', 'DB2')

            total_cleaned = excel_duplicate_count + db1_duplicates + db2_duplicates

            if total_cleaned > 0:
                self.logger.info(f"Total duplicates cleaned: {total_cleaned} (Excel: {excel_duplicate_count}, DB1: {db1_duplicates}, DB2: {db2_duplicates})")

            return True

        except Exception as e:
            self.logger.error(f"Error cleaning duplicates: {e}")
            return False

    def _clean_database_duplicates(self, db_path: str, db_name: str) -> int:
        """Clean duplicate symbols from a specific database"""
        try:
            import sqlite3

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            total_cleaned = 0

            # Get all tables in the database
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]

            for table in tables:
                try:
                    # Check if table has a symbol column
                    cursor.execute(f"PRAGMA table_info({table})")
                    columns = [col[1] for col in cursor.fetchall()]

                    if 'symbol' in columns:
                        # Count duplicates before cleaning
                        cursor.execute(f'SELECT symbol, COUNT(*) FROM {table} GROUP BY symbol HAVING COUNT(*) > 1')
                        duplicates = cursor.fetchall()

                        if duplicates:
                            # Remove duplicates, keeping the latest record
                            for symbol, count in duplicates:
                                if 'timestamp' in columns or 'created_at' in columns:
                                    # Keep the latest record
                                    time_col = 'timestamp' if 'timestamp' in columns else 'created_at'
                                    cursor.execute(f'''
                                    DELETE FROM {table}
                                    WHERE symbol = ? AND {time_col} NOT IN (
                                        SELECT MAX({time_col}) FROM {table} WHERE symbol = ?
                                    )
                                    ''', (symbol, symbol))
                                else:
                                    # Keep the first record (by rowid)
                                    cursor.execute(f'''
                                    DELETE FROM {table}
                                    WHERE symbol = ? AND rowid NOT IN (
                                        SELECT MIN(rowid) FROM {table} WHERE symbol = ?
                                    )
                                    ''', (symbol, symbol))

                                cleaned = cursor.rowcount
                                total_cleaned += cleaned
                                self.logger.info(f"Cleaned {cleaned} duplicates of {symbol} from {db_name}.{table}")

                except Exception as e:
                    self.logger.error(f"Error cleaning duplicates from {db_name}.{table}: {e}")
                    continue

            conn.commit()
            conn.close()

            return total_cleaned

        except Exception as e:
            self.logger.error(f"Error cleaning duplicates from {db_name}: {e}")
            return 0
    
    def export_symbols(self, filename: str = "symbols_export.csv") -> bool:
        """Export symbols to CSV"""
        try:
            df = self.load_symbols_df()
            df.to_csv(filename, index=False)
            self.logger.info(f"Exported {len(df)} symbols to {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error exporting symbols: {e}")
            return False
    
    def import_symbols_from_csv(self, filename: str, replace: bool = False) -> bool:
        """Import symbols from CSV"""
        try:
            new_df = pd.read_csv(filename)
            
            if replace:
                # Replace all symbols
                if self.save_symbols_df(new_df):
                    self.logger.info(f"Replaced all symbols with {len(new_df)} symbols from {filename}")
                    return True
            else:
                # Add to existing symbols
                existing_df = self.load_symbols_df()
                combined_df = pd.concat([existing_df, new_df], ignore_index=True)
                combined_df = combined_df.drop_duplicates(subset=['Symbol'], keep='first')
                
                if self.save_symbols_df(combined_df):
                    self.logger.info(f"Added symbols from {filename}, total now: {len(combined_df)}")
                    return True
                    
        except Exception as e:
            self.logger.error(f"Error importing symbols: {e}")
            return False

def main():
    """Main function for symbol management"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Symbol Management System')
    parser.add_argument('--add', nargs='+', help='Add symbols')
    parser.add_argument('--delete', nargs='+', help='Delete symbols')
    parser.add_argument('--search', help='Search symbols')
    parser.add_argument('--stats', action='store_true', help='Show statistics')
    parser.add_argument('--clean', action='store_true', help='Clean duplicates')
    parser.add_argument('--export', help='Export to CSV file')
    parser.add_argument('--import', dest='import_file', help='Import from CSV file')
    
    args = parser.parse_args()
    
    manager = SymbolManager()
    
    if args.add:
        success = manager.add_symbols(args.add)
        print(f"Add symbols: {'✅ Success' if success else '❌ Failed'}")
    
    elif args.delete:
        success = manager.delete_symbols(args.delete)
        print(f"Delete symbols: {'✅ Success' if success else '❌ Failed'}")
    
    elif args.search:
        results = manager.search_symbols(args.search)
        print(f"Search results for '{args.search}':")
        for result in results:
            print(f"  {result['Symbol']} - {result.get('Company Name', 'N/A')}")
    
    elif args.stats:
        stats = manager.get_symbol_stats()
        print("📊 SYMBOL STATISTICS:")
        print(f"  Total symbols: {stats.get('total_symbols', 0)}")
        print(f"  With tokens: {stats.get('symbols_with_tokens', 0)}")
        print(f"  Without tokens: {stats.get('symbols_without_tokens', 0)}")
        print(f"  Coverage: {stats.get('coverage_percentage', 0):.1f}%")
        if stats.get('missing_symbols'):
            print(f"  Missing: {stats['missing_symbols']}")
    
    elif args.clean:
        success = manager.clean_duplicate_symbols()
        print(f"Clean duplicates: {'✅ Success' if success else '❌ Failed'}")
    
    elif args.export:
        success = manager.export_symbols(args.export)
        print(f"Export symbols: {'✅ Success' if success else '❌ Failed'}")
    
    elif args.import_file:
        success = manager.import_symbols_from_csv(args.import_file)
        print(f"Import symbols: {'✅ Success' if success else '❌ Failed'}")
    
    else:
        # Interactive mode
        print("🔧 SYMBOL MANAGEMENT SYSTEM")
        print("=" * 40)
        
        stats = manager.get_symbol_stats()
        print(f"Current symbols: {stats.get('total_symbols', 0)}")
        print(f"Coverage: {stats.get('coverage_percentage', 0):.1f}%")
        print()
        
        while True:
            print("Options:")
            print("1. Add symbols")
            print("2. Delete symbols")
            print("3. Search symbols")
            print("4. Show statistics")
            print("5. Clean duplicates")
            print("6. Export symbols")
            print("0. Exit")
            
            choice = input("\nSelect option: ").strip()
            
            if choice == "1":
                symbols = input("Enter symbols (space-separated): ").strip().split()
                if symbols:
                    success = manager.add_symbols(symbols)
                    print(f"Add symbols: {'✅ Success' if success else '❌ Failed'}")
            
            elif choice == "2":
                symbols = input("Enter symbols to delete (space-separated): ").strip().split()
                if symbols:
                    success = manager.delete_symbols(symbols)
                    print(f"Delete symbols: {'✅ Success' if success else '❌ Failed'}")
            
            elif choice == "3":
                query = input("Enter search query: ").strip()
                if query:
                    results = manager.search_symbols(query)
                    print(f"Found {len(results)} results:")
                    for result in results[:10]:  # Show first 10
                        print(f"  {result['Symbol']} - {result.get('Company Name', 'N/A')}")
            
            elif choice == "4":
                stats = manager.get_symbol_stats()
                print("📊 SYMBOL STATISTICS:")
                print(f"  Total symbols: {stats.get('total_symbols', 0)}")
                print(f"  With tokens: {stats.get('symbols_with_tokens', 0)}")
                print(f"  Without tokens: {stats.get('symbols_without_tokens', 0)}")
                print(f"  Coverage: {stats.get('coverage_percentage', 0):.1f}%")
            
            elif choice == "5":
                success = manager.clean_duplicate_symbols()
                print(f"Clean duplicates: {'✅ Success' if success else '❌ Failed'}")
            
            elif choice == "6":
                filename = input("Enter filename (default: symbols_export.csv): ").strip()
                if not filename:
                    filename = "symbols_export.csv"
                success = manager.export_symbols(filename)
                print(f"Export symbols: {'✅ Success' if success else '❌ Failed'}")
            
            elif choice == "0":
                print("Goodbye!")
                break
            
            else:
                print("Invalid choice. Please select 0-6.")

if __name__ == "__main__":
    main()
