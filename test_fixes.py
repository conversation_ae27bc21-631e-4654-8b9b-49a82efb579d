#!/usr/bin/env python3
"""
Test script to verify the fixes for symbol processor removal and database issues
"""

import os
import sys
import sqlite3
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_database_creation():
    """Test that the trading_data table can be created"""
    try:
        # Create Data directory if it doesn't exist
        os.makedirs('Data', exist_ok=True)

        # Test SQLite database creation
        conn = sqlite3.connect('Data/trading_data.db')
        cursor = conn.cursor()

        # Create trading_data table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS trading_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            token TEXT NOT NULL,
            exchange TEXT DEFAULT 'NSE',
            timestamp TEXT NOT NULL,
            open_price REAL NOT NULL,
            high_price REAL NOT NULL,
            low_price REAL NOT NULL,
            close_price REAL NOT NULL,
            volume INTEGER NOT NULL,
            interval_type TEXT DEFAULT 'FIFTEEN_MINUTE',
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, timestamp)
        )
        ''')

        # Test inserting a sample record
        cursor.execute('''
        INSERT OR IGNORE INTO trading_data
        (symbol, token, timestamp, open_price, high_price, low_price, close_price, volume)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', ('TEST', '12345', '2025-01-01 09:15:00', 100.0, 105.0, 99.0, 102.0, 1000))

        conn.commit()

        # Test querying the data - this is the exact query that was failing
        cursor.execute('''
        SELECT close_price, timestamp FROM trading_data
        WHERE symbol = ?
        ORDER BY timestamp DESC
        LIMIT 7
        ''', ('TEST',))

        results = cursor.fetchall()

        # Test querying count
        cursor.execute('SELECT COUNT(*) FROM trading_data')
        count = cursor.fetchone()[0]

        conn.close()

        logger.info(f"✅ Database test passed. Records in trading_data: {count}")
        logger.info(f"✅ Query test passed. Found {len(results)} records for TEST symbol")
        return True

    except Exception as e:
        logger.error(f"❌ Database test failed: {e}")
        return False

def test_symbol_manager_import():
    """Test that symbol_manager can be imported and used"""
    try:
        from symbol_manager import SymbolManager
        
        # Test creating instance
        manager = SymbolManager()
        
        # Test getting all symbols (should not crash)
        symbols = manager.get_all_symbols()
        logger.info(f"✅ Symbol manager test passed. Found {len(symbols)} symbols")
        
        # Test getting symbols with tokens
        symbols_with_tokens = manager.get_symbols_with_tokens()
        logger.info(f"✅ Symbols with tokens: {len(symbols_with_tokens)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Symbol manager test failed: {e}")
        return False

def test_flask_imports():
    """Test that Flask app can import without symbol_processor issues"""
    try:
        # Test importing the main components
        from trading_engine import TradingEngine
        from realtime_data_fetcher import RealtimeDataFetcher
        from data_integrity_checker import DataIntegrityChecker
        from duplicate_remover import DuplicateRemover
        from symbol_manager import SymbolManager
        
        logger.info("✅ All Flask app imports successful")
        return True
        
    except Exception as e:
        logger.error(f"❌ Flask imports test failed: {e}")
        return False

def main():
    """Run all tests"""
    logger.info("🧪 Running fix verification tests...")
    logger.info("=" * 50)
    
    tests = [
        ("Database Creation", test_database_creation),
        ("Symbol Manager", test_symbol_manager_import),
        ("Flask Imports", test_flask_imports)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"Running {test_name} test...")
        if test_func():
            passed += 1
        logger.info("-" * 30)
    
    logger.info("=" * 50)
    logger.info(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! The fixes are working correctly.")
        return True
    else:
        logger.error(f"❌ {total - passed} tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
