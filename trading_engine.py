"""
Real-time Trading Engine
Implements 4-FALL + 2-RISE pattern detection with ₹800 profit target
"""
import sqlite3
import pandas as pd
import datetime
import time
import threading
import logging
import csv
import os
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from symbol_processor import SymbolProcessor
from realtime_data_fetcher import RealtimeDataFetcher

@dataclass
class TradingPosition:
    """Represents an active trading position"""
    symbol: str
    buy_price: float
    shares_quantity: float
    investment: float
    target_value: float
    target_price: float
    buy_time: datetime.datetime
    status: str = "ACTIVE"  # ACTIVE, SOLD

@dataclass
class TradingState:
    """Represents trading state for a symbol"""
    symbol: str
    hidden_starting_point: float
    last_6_prices: List[float]
    trend_sequence: List[str]  # ["FALL", "RISE", etc.]
    position: Optional[TradingPosition]
    can_observe: bool
    last_update: datetime.datetime

class TradingEngine:
    """Core trading engine implementing the 4-FALL + 2-RISE strategy with ₹800 profit target"""
    
    def __init__(self):
        # Database paths - Clean separation
        self.market_db_path = "Data/trading_data.db"      # DB1: 15-minute market data
        self.trading_db_path = "Data/trading_operations.db"  # DB2: Layer 2 + paper trading

        self.logger = self._setup_logging()
        self.symbol_manager = SymbolManager()
        self.realtime_fetcher = RealtimeDataFetcher()

        # Initialize database manager
        from database_setup import DatabaseManager
        self.db_manager = DatabaseManager()

        # Trading parameters
        self.investment_per_symbol = 100000  # ₹1,00,000
        self.profit_target = 800  # ₹800 profit
        self.total_pot = 0
        self.vault_amount = 0

        # Trading state
        self.trading_states: Dict[str, TradingState] = {}
        self.active_positions: Dict[str, TradingPosition] = {}
        self.completed_trades: List[Dict] = []
        self.pending_signals: List[Dict] = []  # Store signals awaiting manual confirmation

        # Trend storage for efficient pattern detection (NEW APPROACH)
        self.symbol_trends: Dict[str, List[str]] = {}  # Store trends for each symbol

        # Control flags
        self.is_running = False
        self.trading_thread = None
        
        # Initialize database
        self._init_trading_tables()
        self._load_existing_positions()

        # Calculate initial pot value
        self._calculate_initial_pot()

        # Initialize paper trading CSV file
        self.paper_trade_csv = "Data/csv_files/paper_trade.csv"
        self._init_paper_trade_csv()

        # Initialize confirmation engine (will be set up later to avoid circular imports)
        self.confirmation_engine = None

        # Initialize DB1-DB2 communication system
        try:
            from db1_db2_communicator import get_communicator
            from db1_signal_generator import DB1_SignalGenerator
            from db2_trade_executor import DB2_TradeExecutor

            self.communicator = get_communicator()
            self.db1_generator = DB1_SignalGenerator()
            self.db2_executor = DB2_TradeExecutor()

            self.logger.info("✅ DB1-DB2 Communication System initialized")
            self.logger.info("🚀 DUAL-DATABASE ARCHITECTURE READY")
            self.logger.info("   📊 DB1: Signal generation and position monitoring")
            self.logger.info("   💰 DB2: Trade confirmation and execution")
            self.logger.info("   ⚡ Communication: Millisecond-level queues")
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize DB1-DB2 system: {e}")
            self.communicator = None
            self.db1_generator = None
            self.db2_executor = None
        
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for trading engine"""
        logger = logging.getLogger('trading_engine')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger

    def initialize_confirmation_engine(self):
        """Initialize the confirmation engine (legacy support)"""
        try:
            self.logger.info("🔄 Initializing Confirmation Engine...")
            from confirmation_engine import ConfirmationEngine
            self.confirmation_engine = ConfirmationEngine(self)
            self.logger.info("✅ Confirmation Engine initialized successfully")
            self.logger.info(f"🎯 Confirmation Engine object: {self.confirmation_engine}")
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize confirmation engine: {e}")
            import traceback
            self.logger.error(f"❌ Traceback: {traceback.format_exc()}")

    def start_dual_database_system(self):
        """
        Start the dual-database trading system
        DB1: Signal generation and position monitoring
        DB2: Trade confirmation and execution
        """
        try:
            if not self.communicator or not self.db1_generator or not self.db2_executor:
                self.logger.error("❌ DB1-DB2 system not initialized. Cannot start dual-database system.")
                return False

            self.logger.info("🚀 STARTING DUAL-DATABASE TRADING SYSTEM")
            self.logger.info("=" * 60)

            # Start DB1 signal generation loop in separate thread
            db1_thread = threading.Thread(
                target=self.db1_generator.run_signal_generation_loop,
                name="DB1-SignalGenerator",
                daemon=True
            )
            db1_thread.start()
            self.logger.info("✅ DB1 Signal Generator started (15-minute intervals)")

            # Start DB2 trade execution loop in separate thread
            db2_thread = threading.Thread(
                target=self.db2_executor.run_trade_execution_loop,
                name="DB2-TradeExecutor",
                daemon=True
            )
            db2_thread.start()
            self.logger.info("✅ DB2 Trade Executor started (continuous)")

            self.logger.info("=" * 60)
            self.logger.info("🎯 DUAL-DATABASE SYSTEM OPERATIONAL")
            self.logger.info("📊 DB1: Analyzing patterns and generating signals")
            self.logger.info("💰 DB2: Confirming signals and executing trades")
            self.logger.info("⚡ Communication: Millisecond-level signal transmission")
            self.logger.info("🔄 Rolling Windows: R+R and F+F confirmations active")

            return True

        except Exception as e:
            self.logger.error(f"❌ Error starting dual-database system: {e}")
            return False

    def get_dual_system_status(self) -> Dict[str, any]:
        """Get status of the dual-database system"""
        try:
            if not self.communicator:
                return {'status': 'NOT_INITIALIZED', 'error': 'DB1-DB2 system not initialized'}

            db1_status = self.db1_generator.get_status() if self.db1_generator else {}
            db2_status = self.db2_executor.get_status() if self.db2_executor else {}

            return {
                'status': 'OPERATIONAL',
                'db1_signal_generator': db1_status,
                'db2_trade_executor': db2_status,
                'communication': {
                    'queue_status': self.communicator.get_queue_status(),
                    'performance_stats': self.communicator.get_performance_stats()
                },
                'last_update': datetime.datetime.now().isoformat()
            }

        except Exception as e:
            return {'status': 'ERROR', 'error': str(e)}

    def get_dual_system_performance(self):
        """Get performance metrics of the dual-database system"""
        try:
            performance = {
                'db1_metrics': {
                    'patterns_analyzed': getattr(self.db1_generator, 'patterns_analyzed', 0) if hasattr(self, 'db1_generator') else 0,
                    'signals_generated': getattr(self.db1_generator, 'signals_generated', 0) if hasattr(self, 'db1_generator') else 0,
                    'avg_analysis_time': getattr(self.db1_generator, 'avg_analysis_time', 0) if hasattr(self, 'db1_generator') else 0,
                    'last_run': getattr(self.db1_generator, 'last_run', None) if hasattr(self, 'db1_generator') else None
                },
                'db2_metrics': {
                    'confirmations_processed': getattr(self.db2_executor, 'confirmations_processed', 0) if hasattr(self, 'db2_executor') else 0,
                    'trades_executed': getattr(self.db2_executor, 'trades_executed', 0) if hasattr(self, 'db2_executor') else 0,
                    'avg_execution_time': getattr(self.db2_executor, 'avg_execution_time', 0) if hasattr(self, 'db2_executor') else 0,
                    'success_rate': getattr(self.db2_executor, 'success_rate', 0) if hasattr(self, 'db2_executor') else 0
                },
                'communication_metrics': {
                    'signals_transmitted': getattr(self.communicator, 'signals_transmitted', 0) if hasattr(self, 'communicator') else 0,
                    'avg_transmission_time_ms': getattr(self.communicator, 'avg_transmission_time', 0) if hasattr(self, 'communicator') else 0,
                    'queue_size': getattr(self.communicator, 'queue_size', 0) if hasattr(self, 'communicator') else 0,
                    'transmission_success_rate': getattr(self.communicator, 'transmission_success_rate', 100) if hasattr(self, 'communicator') else 100
                },
                'overall_performance': {
                    'uptime_seconds': getattr(self, 'system_uptime', 0),
                    'total_profit': self.vault_amount,
                    'active_positions': len(self.active_positions),
                    'system_efficiency': 'HIGH' if all([
                        hasattr(self, 'db1_generator') and self.db1_generator is not None,
                        hasattr(self, 'db2_executor') and self.db2_executor is not None,
                        hasattr(self, 'communicator') and self.communicator is not None
                    ]) else 'MEDIUM'
                }
            }

            return performance

        except Exception as e:
            self.logger.error(f"Error getting dual system performance: {e}")
            return {
                'error': str(e),
                'performance_status': 'ERROR'
            }

    def connect_to_trading_api(self) -> bool:
        """Connect to Angel One API for order execution"""
        try:
            if self.api_connected and self.smart_api:
                return True

            credentials = self.config.api_credentials

            # Generate TOTP
            totp = pyotp.TOTP(credentials["totp_secret"])
            totp_code = totp.now()

            # Initialize SmartConnect
            self.smart_api = SmartConnect(api_key=credentials["api_key"])

            # Login
            data = self.smart_api.generateSession(
                clientCode=credentials["username"],
                password=credentials["password"],
                totp=totp_code
            )

            if data and data.get('status'):
                self.api_connected = True
                self.logger.info("✅ Connected to Angel One API for real-time trading")
                return True
            else:
                self.logger.error(f"❌ Angel One API connection failed: {data}")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error connecting to Angel One API: {e}")
            return False

    def _format_indian_currency(self, amount: float) -> str:
        """Format currency in Indian style (Cr, L, K)"""
        if amount == 0:
            return '₹0'

        abs_amount = abs(amount)
        is_negative = amount < 0
        prefix = '-₹' if is_negative else '₹'

        if abs_amount >= 10000000:  # 1 crore and above
            crores = abs_amount / 10000000
            if crores >= 100:
                return f"{prefix}{crores:.0f} Cr"
            elif crores >= 10:
                return f"{prefix}{crores:.1f} Cr"
            else:
                return f"{prefix}{crores:.2f} Cr"
        elif abs_amount >= 100000:  # 1 lakh and above
            lakhs = abs_amount / 100000
            if lakhs >= 10:
                return f"{prefix}{lakhs:.0f} L"
            else:
                return f"{prefix}{lakhs:.1f} L"
        elif abs_amount >= 1000:  # 1 thousand and above
            thousands = abs_amount / 1000
            if thousands >= 10:
                return f"{prefix}{thousands:.0f} K"
            else:
                return f"{prefix}{thousands:.1f} K"
        else:
            # Less than 1000, show full amount
            return f"{prefix}{abs_amount:,.0f}"

    def _init_paper_trade_csv(self):
        """Initialize paper trading CSV file with headers"""
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.paper_trade_csv), exist_ok=True)

            # Check if file exists, if not create with headers
            if not os.path.exists(self.paper_trade_csv):
                headers = ['Symbol Name', 'Date Time', 'Price', 'API Call Link', 'Signals']
                with open(self.paper_trade_csv, 'w', newline='', encoding='utf-8') as file:
                    writer = csv.writer(file)
                    writer.writerow(headers)
                self.logger.info(f"📝 Created paper trading CSV file: {self.paper_trade_csv}")
            else:
                self.logger.info(f"📝 Paper trading CSV file exists: {self.paper_trade_csv}")

        except Exception as e:
            self.logger.error(f"Error initializing paper trading CSV: {e}")

    def _log_paper_trade(self, symbol: str, price: float, signal: str, api_call_link: str = ""):
        """Log trading signal to paper trading CSV file and execute paper trade logic"""
        try:
            current_date = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # Log signal to database for trading logs
            self._log_trading_signal(symbol, signal, price, current_date)

            if signal == "BUY":
                # Execute BUY logic
                return self._execute_paper_buy(symbol, price, current_date, api_call_link)
            elif signal == "SELL":
                # Execute SELL logic
                return self._execute_paper_sell(symbol, price, current_date, api_call_link)

        except Exception as e:
            self.logger.error(f"Error in paper trade for {symbol}: {e}")
            return False

    def _log_trading_signal(self, symbol: str, signal_type: str, price: float, timestamp: str):
        """Log trading signal to database for trading logs"""
        try:
            conn = sqlite3.connect(self.trading_db_path)
            cursor = conn.cursor()

            # Create pattern sequence description
            if signal_type == "BUY":
                pattern_sequence = "4-FALL+2-RISE"
            else:
                pattern_sequence = "PROFIT_TARGET_₹800" if symbol in self.active_positions else "SELL_SIGNAL"

            cursor.execute("""
                INSERT INTO trading_signals (symbol, signal_type, price, timestamp, pattern_sequence, executed)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (symbol, signal_type, price, timestamp, pattern_sequence, True))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Error logging trading signal to database: {e}")

    def _execute_paper_buy(self, symbol: str, price: float, current_date: str, api_call_link: str = ""):
        """Execute paper BUY trade with proper pot management"""
        try:
            # Check if we already have a position in this symbol
            if symbol in self.active_positions:
                self.logger.warning(f"Already have active position in {symbol}, skipping BUY")
                return False

            # Check available funds
            available_funds = self.get_available_funds()
            if available_funds < self.investment_per_symbol:
                self.logger.warning(f"Insufficient funds for {symbol}: ₹{available_funds/100000:.1f}L available, need ₹{self.investment_per_symbol/100000:.1f}L")
                return False

            # DETAILED SHARE CALCULATION
            max_shares_possible = int(self.investment_per_symbol / price)
            quantity = max_shares_possible
            actual_investment = quantity * price
            remaining_cash = self.investment_per_symbol - actual_investment

            # DETAILED TARGET PRICE CALCULATION
            profit_per_share = self.profit_target / quantity
            target_price = price + profit_per_share
            expected_sell_value = quantity * target_price
            expected_total_profit = expected_sell_value - actual_investment

            # Create API call link
            if not api_call_link:
                api_call_link = f"smart_api.placeOrder(symbol='{symbol}', transactiontype='BUY', quantity={quantity}, price={price}, ordertype='MARKET', producttype='INTRADAY')"

            # Log to CSV file
            with open(self.paper_trade_csv, 'a', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow([symbol, current_date, price, api_call_link, "BUY"])

            # Add to active positions (reduce available pot)
            self.active_positions[symbol] = {
                'symbol': symbol,
                'buy_price': price,
                'quantity': quantity,
                'investment_amount': actual_investment,
                'target_price': target_price,
                'buy_time': current_date,
                'status': 'ACTIVE',
                'expected_profit': expected_total_profit
            }

            # PERSIST TO DATABASE - Store active position in database
            self._store_active_position_in_database(symbol, price, quantity, actual_investment, target_price, current_date)

            # Update pot: reduce by investment amount
            remaining_pot = self.get_available_funds()

            # DETAILED BUY LOGS
            self.logger.info(f"")
            self.logger.info(f"🟢 ═══════════════ BUY TRADE EXECUTED ═══════════════")
            self.logger.info(f"📊 SYMBOL: {symbol}")
            self.logger.info(f"💰 SHARE CALCULATION:")
            self.logger.info(f"   • Available Investment: ₹{self.investment_per_symbol:,}")
            self.logger.info(f"   • Share Price: ₹{price:.2f}")
            self.logger.info(f"   • Max Shares Possible: {max_shares_possible:,}")
            self.logger.info(f"   • Shares Bought: {quantity:,}")
            self.logger.info(f"   • Actual Investment: ₹{actual_investment:,.2f}")
            self.logger.info(f"   • Remaining Cash: ₹{remaining_cash:.2f}")
            self.logger.info(f"")
            self.logger.info(f"🎯 TARGET CALCULATION:")
            self.logger.info(f"   • Target Profit: ₹{self.profit_target}")
            self.logger.info(f"   • Profit Per Share: ₹{profit_per_share:.2f}")
            self.logger.info(f"   • Target Sell Price: ₹{target_price:.2f}")
            self.logger.info(f"   • Expected Sell Value: ₹{expected_sell_value:,.2f}")
            self.logger.info(f"   • Expected Total Profit: ₹{expected_total_profit:.2f}")
            self.logger.info(f"")
            self.logger.info(f"💰 POT STATUS:")
            self.logger.info(f"   • Total Pot: ₹{self.total_pot/100000:.1f}L")
            self.logger.info(f"   • Used for this trade: ₹{actual_investment/100000:.1f}L")
            self.logger.info(f"   • Remaining Available: ₹{remaining_pot/100000:.1f}L")
            self.logger.info(f"   • Active Positions: {len(self.active_positions)}")
            self.logger.info(f"🟢 ═══════════════════════════════════════════════════")
            self.logger.info(f"")

            # BROADCAST BUY TRADE TO FRONTEND
            self._broadcast_log("TRADE", f"🟢 ✅ BUY TRADE EXECUTED: {symbol}", "success")
            self._broadcast_log("TRADE", f"📊 Symbol: {symbol} | Price: ₹{price:.2f}")
            self._broadcast_log("TRADE", f"💰 Shares: {quantity:,} | Investment: ₹{actual_investment:,.2f}")
            self._broadcast_log("TRADE", f"🎯 Target Price: ₹{target_price:.2f} | Expected Profit: ₹{expected_total_profit:.2f}")
            self._broadcast_log("TRADE", f"💰 Pot Used: ₹{actual_investment/100000:.1f}L | Remaining: ₹{remaining_pot/100000:.1f}L")
            self._broadcast_log("TRADE", f"📈 Active Positions: {len(self.active_positions)}")

            # Emit WebSocket signal for real-time updates
            self._emit_trading_signal(symbol, price, "BUY", {
                'quantity': quantity,
                'investment': actual_investment,
                'target_price': target_price,
                'remaining_pot': remaining_pot
            })

            # Emit portfolio update for real-time frontend refresh
            self._emit_portfolio_update()

            return True

        except Exception as e:
            self.logger.error(f"Error executing paper BUY for {symbol}: {e}")
            return False

    def _execute_paper_sell(self, symbol: str, price: float, current_date: str, api_call_link: str = ""):
        """Execute paper SELL trade with proper pot management"""
        try:
            # Check if we have an active position
            if symbol not in self.active_positions:
                self.logger.warning(f"No active position found for {symbol}, skipping SELL")
                return False

            position = self.active_positions[symbol]
            quantity = position['quantity']
            buy_price = position['buy_price']
            investment = position['investment_amount']

            # Calculate profit/loss
            sell_value = quantity * price
            profit = sell_value - investment

            # Create API call link
            if not api_call_link:
                api_call_link = f"smart_api.placeOrder(symbol='{symbol}', transactiontype='SELL', quantity={quantity}, price={price}, ordertype='MARKET', producttype='INTRADAY')"

            # Log to CSV file
            with open(self.paper_trade_csv, 'a', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow([symbol, current_date, price, api_call_link, "SELL"])

            # Remove from active positions (free up pot + add profit to vault)
            del self.active_positions[symbol]

            # PERSIST TO DATABASE - Update position status to SOLD
            self._remove_active_position_from_database(symbol, price, current_date, profit)

            # Add profit to vault
            self.vault_amount += profit

            # Update vault amount in database
            self._update_vault_in_database()

            # Log the trade
            self.completed_trades.append({
                'symbol': symbol,
                'buy_price': buy_price,
                'sell_price': price,
                'quantity': quantity,
                'investment': investment,
                'profit': profit,
                'buy_time': position['buy_time'],
                'sell_time': current_date
            })

            available_pot = self.get_available_funds()

            # DETAILED SELL LOGS
            self.logger.info(f"")
            self.logger.info(f"🔴 ═══════════════ SELL TRADE EXECUTED ═══════════════")
            self.logger.info(f"📊 SYMBOL: {symbol}")
            self.logger.info(f"💰 POSITION DETAILS:")
            self.logger.info(f"   • Shares Owned: {quantity:,}")
            self.logger.info(f"   • Buy Price: ₹{buy_price:.2f}")
            self.logger.info(f"   • Sell Price: ₹{price:.2f}")
            self.logger.info(f"   • Original Investment: ₹{investment:,.2f}")
            self.logger.info(f"")
            self.logger.info(f"💵 SELL CALCULATION:")
            self.logger.info(f"   • Sell Value: {quantity:,} × ₹{price:.2f} = ₹{sell_value:,.2f}")
            self.logger.info(f"   • Original Cost: ₹{investment:,.2f}")
            self.logger.info(f"   • Profit/Loss: ₹{sell_value:,.2f} - ₹{investment:,.2f} = ₹{profit:,.2f}")
            self.logger.info(f"   • Profit %: {(profit/investment)*100:.2f}%")
            self.logger.info(f"")
            self.logger.info(f"💰 POT STATUS AFTER SELL:")
            self.logger.info(f"   • Investment Returned to Pot: ₹{investment/100000:.1f}L")
            self.logger.info(f"   • Profit Added to Vault: ₹{profit:.2f}")
            self.logger.info(f"   • Available Pot: ₹{available_pot/100000:.1f}L")
            self.logger.info(f"   • Total Vault: ₹{self.vault_amount:.2f}")
            self.logger.info(f"   • Active Positions Remaining: {len(self.active_positions)}")
            self.logger.info(f"🔴 ═══════════════════════════════════════════════════")
            self.logger.info(f"")

            # BROADCAST SELL TRADE TO FRONTEND
            profit_status = "🟢 PROFIT" if profit > 0 else "🔴 LOSS" if profit < 0 else "⚪ BREAK-EVEN"
            self._broadcast_log("TRADE", f"🔴 ✅ SELL TRADE EXECUTED: {symbol}", "success")
            self._broadcast_log("TRADE", f"📊 Symbol: {symbol} | Buy: ₹{buy_price:.2f} → Sell: ₹{price:.2f}")
            self._broadcast_log("TRADE", f"💰 Shares: {quantity:,} | Sell Value: ₹{sell_value:,.2f}")
            self._broadcast_log("TRADE", f"{profit_status}: ₹{profit:,.2f} ({(profit/investment)*100:.2f}%)")
            self._broadcast_log("TRADE", f"💰 Investment Returned: ₹{investment/100000:.1f}L | Vault: ₹{self.vault_amount:.2f}")
            self._broadcast_log("TRADE", f"📈 Available Pot: ₹{available_pot/100000:.1f}L | Active Positions: {len(self.active_positions)}")

            # Emit WebSocket signal for real-time updates
            self._emit_trading_signal(symbol, price, "SELL", {
                'quantity': quantity,
                'profit': profit,
                'sell_value': sell_value,
                'available_pot': available_pot,
                'vault_amount': self.vault_amount
            })

            # Emit portfolio update for real-time frontend refresh
            self._emit_portfolio_update()

            return True

        except Exception as e:
            self.logger.error(f"Error executing paper SELL for {symbol}: {e}")
            return False

    def _update_vault_in_database(self):
        """Update vault amount in database for persistence"""
        try:
            conn = sqlite3.connect(self.trading_db_path)
            cursor = conn.cursor()

            # Update or insert portfolio status
            cursor.execute('''
            INSERT OR REPLACE INTO portfolio_status
            (id, total_pot, vault_amount, active_positions, completed_trades, total_profit, timestamp)
            VALUES (
                (SELECT id FROM portfolio_status ORDER BY timestamp DESC LIMIT 1),
                ?, ?, ?, ?, ?, ?
            )
            ''', (
                self.total_pot,
                self.vault_amount,
                len(self.active_positions),
                len(self.completed_trades),
                self.vault_amount,  # total_profit is same as vault_amount
                datetime.datetime.now()
            ))

            conn.commit()
            conn.close()

            self.logger.debug(f"💾 Updated vault amount in database: ₹{self.vault_amount:.2f}")

        except Exception as e:
            self.logger.error(f"Error updating vault in database: {e}")

    def _store_active_position_in_database(self, symbol: str, price: float, quantity: int, investment: float, target_price: float, buy_time: str):
        """Store active position in database for persistence"""
        try:
            conn = sqlite3.connect(self.trading_db_path)
            cursor = conn.cursor()

            # Insert active position into trading_positions table
            cursor.execute('''
            INSERT INTO trading_positions
            (symbol, buy_price, shares_quantity, investment, target_value, target_price, buy_time, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                symbol,
                price,
                quantity,
                investment,
                quantity * target_price,  # target_value
                target_price,
                buy_time,
                'ACTIVE'
            ))

            conn.commit()
            conn.close()

            self.logger.debug(f"💾 Stored active position in database: {symbol}")

        except Exception as e:
            self.logger.error(f"Error storing active position in database: {e}")

    def _remove_active_position_from_database(self, symbol: str, sell_price: float, sell_time: str, profit: float):
        """Remove active position from database and mark as sold"""
        try:
            conn = sqlite3.connect(self.trading_db_path)
            cursor = conn.cursor()

            # Update position status to SOLD
            cursor.execute('''
            UPDATE trading_positions
            SET status = 'SOLD', sell_price = ?, sell_time = ?, actual_profit = ?
            WHERE symbol = ? AND status = 'ACTIVE'
            ''', (sell_price, sell_time, profit, symbol))

            conn.commit()
            conn.close()

            self.logger.debug(f"💾 Updated position in database: {symbol} -> SOLD")

        except Exception as e:
            self.logger.error(f"Error updating position in database: {e}")

    def _emit_trading_signal(self, symbol: str, price: float, signal_type: str, extra_data: dict = None):
        """Emit trading signal via WebSocket for real-time frontend updates"""
        try:
            # Import here to avoid circular imports
            import flask_app

            if hasattr(flask_app, 'socketio'):
                signal_data = {
                    'signal': {
                        'symbol': symbol,
                        'price': price,
                        'signal_type': signal_type,
                        'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        'extra': extra_data or {}
                    }
                }

                flask_app.socketio.emit('trading_signal', signal_data)
                self.logger.debug(f"📡 Emitted {signal_type} signal for {symbol} via WebSocket")

        except Exception as e:
            self.logger.error(f"Error emitting trading signal: {e}")

    def _emit_portfolio_update(self):
        """Emit portfolio status update via WebSocket for real-time frontend updates"""
        try:
            # Import here to avoid circular imports
            import flask_app

            if hasattr(flask_app, 'socketio'):
                # Calculate current portfolio status
                used_funds = sum(pos.get('investment_amount', 0) for pos in self.active_positions.values())
                available_pot = self.total_pot - used_funds

                portfolio_data = {
                    'total_pot': self.total_pot,
                    'available_pot': available_pot,
                    'used_funds': used_funds,
                    'vault_amount': self.vault_amount,
                    'active_positions': len(self.active_positions),
                    'completed_trades': len(self.completed_trades),
                    'total_profit': self.vault_amount,
                    'last_updated': datetime.datetime.now().isoformat()
                }

                flask_app.socketio.emit('portfolio_update', {
                    'portfolio': portfolio_data,
                    'timestamp': datetime.datetime.now().isoformat()
                })

                self.logger.debug(f"📡 Emitted portfolio update via WebSocket")

        except Exception as e:
            self.logger.error(f"Error emitting portfolio update: {e}")

    def _broadcast_log(self, category: str, message: str, log_type: str = 'info'):
        """Broadcast log message to frontend via WebSocket"""
        try:
            # Import here to avoid circular imports
            import flask_app

            if hasattr(flask_app, 'broadcast_log'):
                flask_app.broadcast_log(category, message, log_type)
            else:
                self.logger.warning(f"❌ flask_app.broadcast_log not found!")

        except Exception as e:
            # Fallback to regular logging if WebSocket fails
            self.logger.error(f"❌ WebSocket log broadcast failed: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")

    def _init_trading_tables(self):
        """Initialize trading-related database tables"""
        conn = sqlite3.connect(self.market_db_path)
        cursor = conn.cursor()

        # Create trading_data table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS trading_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            token TEXT NOT NULL,
            exchange TEXT DEFAULT 'NSE',
            timestamp TEXT NOT NULL,
            open_price REAL NOT NULL,
            high_price REAL NOT NULL,
            low_price REAL NOT NULL,
            close_price REAL NOT NULL,
            volume INTEGER NOT NULL,
            interval_type TEXT DEFAULT 'FIFTEEN_MINUTE',
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, timestamp)
        )
        ''')

        # Add trading columns to existing table
        try:
            cursor.execute('ALTER TABLE trading_data ADD COLUMN trend_direction TEXT')
            cursor.execute('ALTER TABLE trading_data ADD COLUMN buy_signal BOOLEAN DEFAULT FALSE')
            cursor.execute('ALTER TABLE trading_data ADD COLUMN sell_signal BOOLEAN DEFAULT FALSE')
        except sqlite3.OperationalError:
            pass  # Columns already exist
        
        # Create trading positions table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS trading_positions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            buy_price REAL NOT NULL,
            shares_quantity REAL NOT NULL,
            investment REAL NOT NULL,
            target_value REAL NOT NULL,
            target_price REAL NOT NULL,
            buy_time DATETIME NOT NULL,
            sell_time DATETIME,
            sell_price REAL,
            actual_profit REAL,
            status TEXT DEFAULT 'ACTIVE',
            order_id TEXT,
            sell_order_id TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Add order_id columns if they don't exist
        try:
            cursor.execute('ALTER TABLE trading_positions ADD COLUMN order_id TEXT')
            cursor.execute('ALTER TABLE trading_positions ADD COLUMN sell_order_id TEXT')
        except sqlite3.OperationalError:
            pass  # Columns already exist
        
        # Create trading signals table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS trading_signals (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            signal_type TEXT NOT NULL,  -- BUY, SELL
            price REAL NOT NULL,
            timestamp DATETIME NOT NULL,
            pattern_sequence TEXT,  -- JSON of last 6 trends
            executed BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Create portfolio tracking table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS portfolio_status (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            total_pot REAL NOT NULL,
            vault_amount REAL NOT NULL,
            active_positions INTEGER NOT NULL,
            completed_trades INTEGER NOT NULL,
            total_profit REAL NOT NULL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        conn.commit()
        conn.close()
        
    def _load_existing_positions(self):
        """Load existing active positions from database"""
        conn = sqlite3.connect(self.trading_db_path)
        cursor = conn.cursor()

        cursor.execute('''
        SELECT symbol, buy_price, shares_quantity, investment, target_value,
               target_price, buy_time, status
        FROM trading_positions
        WHERE status = 'ACTIVE'
        ''')

        for row in cursor.fetchall():
            symbol, buy_price, shares_qty, investment, target_val, target_price, buy_time, status = row

            # Store as dictionary (consistent with _execute_paper_buy format)
            self.active_positions[symbol] = {
                'symbol': symbol,
                'buy_price': buy_price,
                'quantity': shares_qty,
                'investment_amount': investment,
                'target_price': target_price,
                'buy_time': buy_time,
                'status': status,
                'expected_profit': target_val - investment  # Calculate expected profit
            }

        conn.close()
        self.logger.info(f"Loaded {len(self.active_positions)} active positions from database")

    def _calculate_initial_pot(self):
        """Calculate initial pot value based on symbols with valid tokens"""
        try:
            # Get actual trading symbols (only those with valid tokens)
            stats = self.symbol_manager.get_symbol_stats()
            symbols_with_tokens = stats['symbols_with_tokens']
            symbol_count = symbols_with_tokens

            # Calculate total pot: number of trading symbols × investment per symbol
            self.total_pot = symbol_count * self.investment_per_symbol

            # Ensure trading tables exist before querying
            self._init_trading_tables()

            # Load vault amount from database if exists
            conn = sqlite3.connect(self.trading_db_path)
            cursor = conn.cursor()

            # Try to query with timestamp column, fallback if column doesn't exist
            try:
                cursor.execute('''
                SELECT vault_amount, total_profit
                FROM portfolio_status
                ORDER BY timestamp DESC
                LIMIT 1
                ''')
            except sqlite3.OperationalError as e:
                if "no such column: timestamp" in str(e):
                    # Try with id column instead
                    cursor.execute('''
                    SELECT vault_amount, total_profit
                    FROM portfolio_status
                    ORDER BY id DESC
                    LIMIT 1
                    ''')
                else:
                    raise e

            result = cursor.fetchone()
            if result:
                # Load existing vault amount from database
                self.vault_amount = float(result[0]) if result[0] is not None else 0.0
                self.logger.info(f"Loaded vault amount from database: ₹{self.vault_amount:.2f}")
            else:
                # No previous data, start with 0
                self.vault_amount = 0.0
                self.logger.info("No previous vault data found, starting with ₹0.00")

                # Insert initial portfolio status
                try:
                    cursor.execute('''
                    INSERT INTO portfolio_status (vault_amount, total_profit, timestamp)
                    VALUES (?, ?, ?)
                    ''', (0.0, 0.0, datetime.datetime.now()))
                except sqlite3.OperationalError as e:
                    if "no such column: timestamp" in str(e):
                        # Insert without timestamp column
                        cursor.execute('''
                        INSERT INTO portfolio_status (vault_amount, total_profit)
                        VALUES (?, ?)
                        ''', (0.0, 0.0))
                    else:
                        raise e
                conn.commit()

            conn.close()

            self.logger.info(f"Portfolio initialized: {symbol_count} trading symbols (with valid tokens)")
            self.logger.info(f"Total pot: {self._format_indian_currency(self.total_pot)} ({symbol_count} × {self._format_indian_currency(self.investment_per_symbol)})")
            self.logger.info(f"Vault amount: {self._format_indian_currency(self.vault_amount)}")

        except Exception as e:
            self.logger.error(f"Error calculating initial pot: {e}")
            # Fallback to default calculation - use actual trading symbols (222)
            stats = self.symbol_manager.get_symbol_stats()
            self.total_pot = stats['symbols_with_tokens'] * self.investment_per_symbol
            self.vault_amount = 0
    
    def initialize_trading_states(self):
        """Initialize trading states for all symbols"""
        stats = self.symbol_manager.get_symbol_stats()
        self.total_pot = stats['symbols_with_tokens'] * self.investment_per_symbol

        # Connect to market database (DB1) for trading_data table
        conn = sqlite3.connect(self.market_db_path)
        cursor = conn.cursor()

        # Ensure trading_data table exists
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS trading_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            token TEXT NOT NULL,
            exchange TEXT DEFAULT 'NSE',
            timestamp TEXT NOT NULL,
            open_price REAL NOT NULL,
            high_price REAL NOT NULL,
            low_price REAL NOT NULL,
            close_price REAL NOT NULL,
            volume INTEGER NOT NULL,
            interval_type TEXT DEFAULT 'FIFTEEN_MINUTE',
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, timestamp)
        )
        ''')
        conn.commit()

        for symbol in symbols_with_tokens:
            # Get latest 6 prices for this symbol
            query = '''
            SELECT close_price, timestamp FROM trading_data 
            WHERE symbol = ? 
            ORDER BY timestamp DESC 
            LIMIT 7
            '''
            
            df = pd.read_sql_query(query, conn, params=(symbol,))
            
            if len(df) >= 2:
                # Reverse to get chronological order
                prices = df['close_price'].tolist()[::-1]
                
                # Hidden starting point is one step back from first observation
                hidden_point = prices[0] if len(prices) > 1 else prices[0]
                last_6_prices = prices[1:7] if len(prices) > 6 else prices[1:]
                
                # Calculate trend sequence
                trend_sequence = []
                prev_price = hidden_point
                for price in last_6_prices:
                    trend = "RISE" if price > prev_price else "FALL"
                    trend_sequence.append(trend)
                    prev_price = price
                
                # Check if symbol has active position
                can_observe = symbol not in self.active_positions
                
                trading_state = TradingState(
                    symbol=symbol,
                    hidden_starting_point=hidden_point,
                    last_6_prices=last_6_prices,
                    trend_sequence=trend_sequence,
                    position=self.active_positions.get(symbol),
                    can_observe=can_observe,
                    last_update=datetime.datetime.now()
                )
                
                self.trading_states[symbol] = trading_state
        
        conn.close()
        self.logger.info(f"Initialized trading states for {len(self.trading_states)} symbols")
        self.logger.info(f"Total pot: {self._format_indian_currency(self.total_pot)}")
    




    def detect_4fall_1rise_pattern_with_drop(self, latest_data: List[Dict]) -> bool:
        """Detect 4-FALL + 1-RISE pattern with 0.5% total fall condition"""
        try:
            if len(latest_data) < 6:  # Need 6 data points for 5 trends
                return False

            # Extract prices in chronological order (reverse the list)
            prices = [data['close_price'] for data in reversed(latest_data)]

            # Calculate trends from the 6 data points (5 trends)
            trends = []
            for i in range(1, len(prices)):
                diff = prices[i] - prices[i-1]
                if diff > 0:
                    trends.append("RISE")
                elif diff < 0:
                    trends.append("FALL")
                else:
                    trends.append("FLAT")

            # Check for 4-FALL + 1-RISE pattern (exactly 5 trends)
            if len(trends) == 5:
                expected_pattern = ["FALL", "FALL", "FALL", "FALL", "RISE"]
                pattern_match = trends == expected_pattern

                if pattern_match:
                    # Check 0.5% fall condition
                    start_price = prices[0]  # First price
                    lowest_price = min(prices[:-1])  # Lowest price before the final rise

                    # Calculate percentage drop
                    percentage_drop = ((start_price - lowest_price) / start_price) * 100

                    if percentage_drop >= 0.5:
                        self.logger.info(f"✅ FFFFR Pattern + 0.5% Drop: {percentage_drop:.2f}% fall detected")
                        return True
                    else:
                        self.logger.info(f"❌ FFFFR Pattern found but insufficient drop: {percentage_drop:.2f}% (need ≥0.5%)")
                        return False

            return False

        except Exception as e:
            self.logger.error(f"Error in detect_4fall_1rise_pattern_with_drop: {e}")
            return False

    def _send_signal_to_db2(self, symbol: str, price: float, signal_type: str, pattern: str):
        """Send trading signal to DB2 (confirmation_engine) for 2-minute confirmation"""
        try:
            # Import confirmation engine
            from confirmation_engine import ConfirmationEngine

            # Initialize confirmation engine if not already done
            if not hasattr(self, 'confirmation_engine'):
                self.confirmation_engine = ConfirmationEngine()

            # Add BUY confirmation to DB2
            confirmation_id = self.confirmation_engine.add_buy_confirmation(symbol, price)

            if confirmation_id:
                self.logger.info(f"✅ Signal sent to DB2: {symbol} {signal_type} @ ₹{price:.2f} (ID: {confirmation_id})")
                self.logger.info(f"🔄 DB2 will confirm with 2-minute RISE-RISE pattern")
            else:
                self.logger.error(f"❌ Failed to send signal to DB2 for {symbol}")

        except Exception as e:
            self.logger.error(f"❌ Error sending signal to DB2: {e}")

    def check_4fall_2rise_pattern(self, symbol: str, new_price: float, previous_price: float) -> bool:
        """Check 4-FALL + 2-RISE pattern using stored trends (EFFICIENT APPROACH)"""
        try:
            # Calculate new trend
            if new_price > previous_price:
                new_trend = "RISE"
            elif new_price < previous_price:
                new_trend = "FALL"
            else:
                new_trend = "FLAT"

            # Get stored trends for this symbol
            if symbol not in self.symbol_trends:
                self.symbol_trends[symbol] = []

            # Add new trend and keep only last 6 trends
            self.symbol_trends[symbol].append(new_trend)
            self.symbol_trends[symbol] = self.symbol_trends[symbol][-6:]  # Keep last 6 trends

            # Check for 4-FALL + 2-RISE pattern
            if len(self.symbol_trends[symbol]) >= 6:
                recent_trends = self.symbol_trends[symbol][-6:]  # Last 6 trends
                expected_pattern = ["FALL", "FALL", "FALL", "FALL", "RISE", "RISE"]

                if recent_trends == expected_pattern:
                    self.logger.info(f"")
                    self.logger.info(f"🎯 ═══════════ 4-FALL+2-RISE PATTERN DETECTED ═══════════")
                    self.logger.info(f"📊 {symbol}: Stored Trend Sequence: {' → '.join(recent_trends)}")
                    self.logger.info(f"📈 New Price: ₹{new_price:.2f} (Previous: ₹{previous_price:.2f}) = {new_trend}")
                    self.logger.info(f"✅ BUY SIGNAL TRIGGERED!")
                    self.logger.info(f"═══════════════════════════════════════════════════════")
                    self.logger.info(f"")
                    return True

            # Log current trend status for debugging
            if len(self.symbol_trends[symbol]) >= 4:
                self.logger.debug(f"📊 {symbol}: Trends: {self.symbol_trends[symbol]} | New: {new_trend}")

            return False

        except Exception as e:
            self.logger.error(f"Error in check_4fall_2rise_pattern for {symbol}: {e}")
            return False



    def simple_sell_strategy(self, position, current_price: float) -> tuple:
        """
        Simple sell strategy: SELL when profit reaches ₹800
        Returns: (should_sell, reason, target_used)
        """
        try:
            # Handle both object and dictionary position formats
            if hasattr(position, 'shares_quantity'):
                # Position is an object
                shares_quantity = position.shares_quantity
                investment = position.investment
            else:
                # Position is a dictionary (from database)
                shares_quantity = position.get('shares_quantity', 97)  # Default to 97 shares
                investment = position.get('investment', shares_quantity * position.get('buy_price', current_price))

            profit = (shares_quantity * current_price) - investment
            profit_target = 800  # Fixed profit target

            # UPDATED LOGIC: When profit reaches ₹800, start 2-minute confirmation
            # Current price becomes base point for 2 consecutive FALL confirmation
            if profit >= profit_target:
                return True, f"PROFIT_TARGET_REACHED: ₹{profit:.0f} >= ₹{profit_target} - Starting SELL confirmation from ₹{current_price:.2f}", profit_target
            else:
                return False, f"Below target: ₹{profit:.0f} < ₹{profit_target}", profit_target

        except Exception as e:
            self.logger.error(f"Error in simple_sell_strategy: {e}")
            return False, f"Error: {str(e)}", 800

    def get_latest_symbol_data(self, symbol: str, limit: int = 7) -> List[Dict]:
        """Get latest data for a symbol from SQL database"""
        try:
            session = self.db_manager.get_session()

            # Import here to avoid circular imports
            from database_setup import TradingData

            # Query latest data for the symbol
            results = session.query(TradingData).filter(
                TradingData.symbol == symbol
            ).order_by(TradingData.timestamp.desc()).limit(limit).all()

            session.close()

            if not results:
                return []

            # Convert to list of dictionaries
            data = []
            for row in results:
                data.append({
                    'symbol': row.symbol,
                    'timestamp': row.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                    'open_price': float(row.open_price),
                    'high_price': float(row.high_price),
                    'low_price': float(row.low_price),
                    'close_price': float(row.close_price),
                    'volume': int(row.volume)
                })

            return data

        except Exception as e:
            self.logger.error(f"Error getting latest data for {symbol}: {e}")
            return []
    
    def calculate_target_price(self, buy_price: float, investment: float) -> Tuple[float, float]:
        """Calculate target price for ₹800 profit"""
        shares_quantity = investment / buy_price
        target_value = investment + self.profit_target
        target_price = target_value / shares_quantity
        return shares_quantity, target_price

    def place_buy_order(self, symbol: str, token: str, quantity: int) -> Dict:
        """Place real BUY order via Angel One API"""
        try:
            if not self.api_connected:
                if not self.connect_to_trading_api():
                    return {"success": False, "error": "API not connected"}

            order_params = {
                "variety": "NORMAL",
                "tradingsymbol": symbol,
                "symboltoken": token,
                "transactiontype": "BUY",
                "exchange": "NSE",
                "ordertype": "MARKET",  # Market order for immediate execution
                "producttype": "INTRADAY",  # Intraday trading
                "duration": "DAY",
                "quantity": str(quantity)
            }

            self.logger.info(f"🔄 Placing BUY order: {symbol} | Qty: {quantity} | Type: MARKET")

            response = self.smart_api.placeOrder(order_params)

            if response and response.get('status'):
                order_id = response.get('data', {}).get('orderid')
                self.logger.info(f"✅ BUY order placed successfully: {symbol} | Order ID: {order_id}")
                return {
                    "success": True,
                    "order_id": order_id,
                    "symbol": symbol,
                    "quantity": quantity,
                    "order_type": "BUY",
                    "response": response
                }
            else:
                error_msg = response.get('message', 'Unknown error')
                self.logger.error(f"❌ BUY order failed: {symbol} | Error: {error_msg}")
                return {"success": False, "error": error_msg}

        except Exception as e:
            self.logger.error(f"❌ Exception placing BUY order for {symbol}: {e}")
            return {"success": False, "error": str(e)}

    def place_sell_order(self, symbol: str, token: str, quantity: int) -> Dict:
        """Place real SELL order via Angel One API"""
        try:
            if not self.api_connected:
                if not self.connect_to_trading_api():
                    return {"success": False, "error": "API not connected"}

            order_params = {
                "variety": "NORMAL",
                "tradingsymbol": symbol,
                "symboltoken": token,
                "transactiontype": "SELL",
                "exchange": "NSE",
                "ordertype": "MARKET",  # Market order for immediate execution
                "producttype": "INTRADAY",  # Intraday trading
                "duration": "DAY",
                "quantity": str(quantity)
            }

            self.logger.info(f"🔄 Placing SELL order: {symbol} | Qty: {quantity} | Type: MARKET")

            response = self.smart_api.placeOrder(order_params)

            if response and response.get('status'):
                order_id = response.get('data', {}).get('orderid')
                self.logger.info(f"✅ SELL order placed successfully: {symbol} | Order ID: {order_id}")
                return {
                    "success": True,
                    "order_id": order_id,
                    "symbol": symbol,
                    "quantity": quantity,
                    "order_type": "SELL",
                    "response": response
                }
            else:
                error_msg = response.get('message', 'Unknown error')
                self.logger.error(f"❌ SELL order failed: {symbol} | Error: {error_msg}")
                return {"success": False, "error": error_msg}

        except Exception as e:
            self.logger.error(f"❌ Exception placing SELL order for {symbol}: {e}")
            return {"success": False, "error": str(e)}

    def execute_buy_signal(self, symbol: str, current_price: float) -> bool:
        """Execute BUY signal - Log to paper trading CSV"""
        try:
            shares_qty, target_price = self.calculate_target_price(current_price, self.investment_per_symbol)

            # Calculate quantity (round down to avoid fractional shares)
            quantity = int(shares_qty)
            actual_investment = quantity * current_price

            # Log to paper trading CSV file
            api_call_link = f"smart_api.placeOrder(symbol='{symbol}', transactiontype='BUY', quantity={quantity}, price={current_price}, ordertype='MARKET', producttype='INTRADAY')"
            self._log_paper_trade(symbol, current_price, "BUY", api_call_link)

            # Create position record for tracking
            position = TradingPosition(
                symbol=symbol,
                buy_price=current_price,
                shares_quantity=quantity,
                investment=actual_investment,
                target_value=actual_investment + self.profit_target,
                target_price=target_price,
                buy_time=datetime.datetime.now()
            )

            # Store in database for tracking
            conn = sqlite3.connect(self.trading_db_path)
            cursor = conn.cursor()

            cursor.execute('''
            INSERT INTO trading_positions
            (symbol, buy_price, shares_quantity, investment, target_value, target_price, buy_time, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (symbol, current_price, quantity, actual_investment,
                  position.target_value, target_price, position.buy_time, "PAPER_ACTIVE"))

            conn.commit()
            conn.close()

            # Update state
            self.active_positions[symbol] = position
            self.trading_states[symbol].position = position
            self.trading_states[symbol].can_observe = False

            self.logger.info(f"📝 PAPER BUY SIGNAL: {symbol} @ ₹{current_price:.2f} | Qty: {quantity} | Target: ₹{target_price:.2f} | Investment: {self._format_indian_currency(actual_investment)}")
            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to execute paper BUY for {symbol}: {e}")
            return False
    
    def execute_sell_signal(self, symbol: str, current_price: float) -> bool:
        """Execute SELL signal - Log to paper trading CSV"""
        try:
            position = self.active_positions[symbol]
            actual_profit = (current_price * position.shares_quantity) - position.investment

            # Log to paper trading CSV file
            api_call_link = f"smart_api.placeOrder(symbol='{symbol}', transactiontype='SELL', quantity={int(position.shares_quantity)}, price={current_price}, ordertype='MARKET', producttype='INTRADAY')"
            self._log_paper_trade(symbol, current_price, "SELL", api_call_link)

            # Update database
            conn = sqlite3.connect(self.trading_db_path)
            cursor = conn.cursor()

            cursor.execute('''
            UPDATE trading_positions
            SET sell_time = ?, sell_price = ?, actual_profit = ?, status = 'PAPER_SOLD'
            WHERE symbol = ? AND status = 'PAPER_ACTIVE'
            ''', (datetime.datetime.now(), current_price, actual_profit, symbol))

            conn.commit()
            conn.close()

            # Update state
            del self.active_positions[symbol]
            self.trading_states[symbol].position = None
            self.trading_states[symbol].can_observe = True

            # Update vault
            self.vault_amount += actual_profit

            self.completed_trades.append({
                'symbol': symbol,
                'buy_price': position.buy_price,
                'sell_price': current_price,
                'profit': actual_profit,
                'sell_time': datetime.datetime.now()
            })

            self.logger.info(f"📝 PAPER SELL SIGNAL: {symbol} @ ₹{current_price:.2f} | Profit: {self._format_indian_currency(actual_profit)}")
            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to execute paper SELL for {symbol}: {e}")
            return False

    def store_pending_signal(self, symbol: str, signal_type: str, price: float, reason: str):
        """Store trading signal for manual confirmation"""
        try:
            signal = {
                'symbol': symbol,
                'signal_type': signal_type,
                'price': price,
                'reason': reason,
                'timestamp': datetime.datetime.now(),
                'status': 'PENDING'
            }

            # Remove any existing pending signal for this symbol
            self.pending_signals = [s for s in self.pending_signals if s['symbol'] != symbol]

            # Add new signal
            self.pending_signals.append(signal)

            self.logger.info(f"📋 PENDING SIGNAL STORED: {signal_type} {symbol} @ ₹{price:.2f}")
            self.logger.info(f"👤 Manual confirmation required via dashboard")

        except Exception as e:
            self.logger.error(f"Error storing pending signal: {e}")

    def start_trading(self):
        """Start the trading engine"""
        try:
            if self.is_running:
                self.logger.warning("Trading engine is already running")
                return False

            self.is_running = True
            self.logger.info("Trading engine started")

            # Initialize trading states for all symbols
            self.initialize_trading_states()

            # Start trading in a separate thread
            def trading_loop():
                while self.is_running:
                    try:
                        self.process_trading_cycle()
                        time.sleep(60)  # Wait 1 minute between cycles
                    except Exception as e:
                        self.logger.error(f"Error in trading loop: {e}")
                        time.sleep(30)  # Wait 30 seconds on error

            self.trading_thread = threading.Thread(target=trading_loop, daemon=True)
            self.trading_thread.start()

            return True

        except Exception as e:
            self.logger.error(f"Failed to start trading engine: {e}")
            self.is_running = False
            return False

    def stop_trading(self):
        """Stop the trading engine"""
        try:
            if not self.is_running:
                self.logger.warning("Trading engine is not running")
                return False

            self.is_running = False
            self.logger.info("Trading engine stopped")

            # Wait for trading thread to finish
            if self.trading_thread and self.trading_thread.is_alive():
                self.trading_thread.join(timeout=5)

            return True

        except Exception as e:
            self.logger.error(f"Failed to stop trading engine: {e}")
            return False

    def process_trading_cycle(self):
        """Process one trading cycle for all symbols"""
        try:
            # Get latest data for all symbols
            current_time = datetime.datetime.now()

            # Only trade during market hours
            if not self.is_market_hours(current_time):
                return

            self.logger.info("Processing trading cycle...")

            # Process each symbol
            for symbol, state in self.trading_states.items():
                try:
                    # Get latest price for symbol
                    latest_price = self.get_latest_price(symbol)
                    if latest_price is None:
                        continue

                    # Update trend sequence
                    self.update_trend_sequence(symbol, latest_price)

                    # Check for trading signals
                    if state.can_observe:
                        # Get previous price for trend calculation
                        previous_price = self.get_previous_price(symbol)
                        if previous_price is not None:
                            # Check for BUY signal using efficient trend storage
                            if self.check_4fall_2rise_pattern(symbol, latest_price, previous_price):
                                self.logger.info(f"4-FALL+2-RISE pattern detected for {symbol}")

                                # Add to confirmation engine - NO FALLBACK TO DIRECT EXECUTION
                                if self.confirmation_engine:
                                    self.logger.info(f"🔄 LAYER 2: Adding {symbol} to BUY confirmation engine")
                                    self.confirmation_engine.add_buy_signal_for_confirmation(symbol, latest_price)
                                else:
                                    # NO DIRECT EXECUTION - Confirmation engine is required for proper flow
                                    self.logger.error(f"❌ CONFIRMATION ENGINE NOT AVAILABLE - BUY signal for {symbol} SKIPPED")
                                    self.logger.error(f"❌ All signals must go through DB1→DB2→Paper Trading flow")

                                # Store signal in database
                                self.store_trading_signal(symbol, 'BUY', latest_price, [])

                    else:
                        # Check for SELL signal using simple strategy (₹800 profit target)
                        position = state.position
                        if position:
                            # Use simple sell strategy - just check if profit >= ₹800
                            should_sell, reason, target_used = self.simple_sell_strategy(
                                position, latest_price
                            )

                            if should_sell:
                                self.logger.info(f"🔴 SELL SIGNAL: {symbol} @ ₹{latest_price:.2f} | Reason: {reason}")

                                # Add to confirmation engine - NO FALLBACK TO DIRECT EXECUTION
                                if self.confirmation_engine:
                                    self.logger.info(f"🔄 LAYER 2: Adding {symbol} to SELL confirmation engine")
                                    self.confirmation_engine.add_sell_signal_for_confirmation(symbol, latest_price)
                                else:
                                    # NO DIRECT EXECUTION - Confirmation engine is required for proper flow
                                    self.logger.error(f"❌ CONFIRMATION ENGINE NOT AVAILABLE - SELL signal for {symbol} SKIPPED")
                                    self.logger.error(f"❌ All signals must go through DB1→DB2→Paper Trading flow")

                                # Store signal in database
                                self.store_trading_signal(symbol, 'SELL', latest_price, [])
                            else:
                                # Log why we're holding
                                self.logger.debug(f"📊 {symbol}: {reason}")

                except Exception as e:
                    self.logger.error(f"Error processing {symbol}: {e}")
                    continue

            # Update portfolio status
            self.update_portfolio_status()

        except Exception as e:
            self.logger.error(f"Error in trading cycle: {e}")

    def is_market_hours(self, current_time: datetime.datetime) -> bool:
        """Check if current time is within market hours"""
        # Market hours: 9:15 AM to 3:30 PM, Monday to Friday
        if current_time.weekday() >= 5:  # Saturday = 5, Sunday = 6
            return False

        market_open = current_time.replace(hour=9, minute=15, second=0, microsecond=0)
        market_close = current_time.replace(hour=15, minute=30, second=0, microsecond=0)

        return market_open <= current_time <= market_close

    def get_latest_price(self, symbol: str) -> float:
        """Get latest price for a symbol from database"""
        try:
            conn = sqlite3.connect(self.market_db_path)  # Use DB1 for market data
            cursor = conn.cursor()

            cursor.execute('''
            SELECT close_price FROM trading_data
            WHERE symbol = ?
            ORDER BY timestamp DESC
            LIMIT 1
            ''', (symbol,))

            result = cursor.fetchone()
            conn.close()

            return result[0] if result else None

        except Exception as e:
            self.logger.error(f"Error getting latest price for {symbol}: {e}")
            return None

    def get_previous_price(self, symbol: str) -> float:
        """Get previous price for a symbol from database (for trend calculation)"""
        try:
            conn = sqlite3.connect(self.market_db_path)  # Use DB1 for market data
            cursor = conn.cursor()

            cursor.execute('''
            SELECT close_price FROM trading_data
            WHERE symbol = ?
            ORDER BY timestamp DESC
            LIMIT 2
            ''', (symbol,))

            results = cursor.fetchall()
            conn.close()

            # Return the second most recent price (previous price)
            return results[1][0] if len(results) >= 2 else None

        except Exception as e:
            self.logger.error(f"Error getting previous price for {symbol}: {e}")
            return None

    def update_trend_sequence(self, symbol: str, new_price: float):
        """Update trend sequence for a symbol"""
        try:
            state = self.trading_states[symbol]

            # Get last price
            last_price = state.last_6_prices[-1] if state.last_6_prices else state.hidden_starting_point

            # Determine trend
            trend = "RISE" if new_price > last_price else "FALL"

            # Update price list (keep only last 6)
            state.last_6_prices.append(new_price)
            if len(state.last_6_prices) > 6:
                state.last_6_prices.pop(0)

            # Update trend sequence (keep only last 6)
            state.trend_sequence.append(trend)
            if len(state.trend_sequence) > 6:
                state.trend_sequence.pop(0)

            state.last_update = datetime.datetime.now()

        except Exception as e:
            self.logger.error(f"Error updating trend sequence for {symbol}: {e}")

    def store_trading_signal(self, symbol: str, signal_type: str, price: float, trend_sequence: List[str]):
        """Store trading signal in database"""
        try:
            conn = sqlite3.connect(self.trading_db_path)
            cursor = conn.cursor()

            cursor.execute('''
            INSERT INTO trading_signals
            (symbol, signal_type, price, timestamp, pattern_sequence)
            VALUES (?, ?, ?, ?, ?)
            ''', (symbol, signal_type, price, datetime.datetime.now(), str(trend_sequence)))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Error storing trading signal: {e}")

    def update_portfolio_status(self):
        """Update portfolio status in database"""
        try:
            conn = sqlite3.connect(self.trading_db_path)
            cursor = conn.cursor()

            total_profit = sum(trade['profit'] for trade in self.completed_trades)

            cursor.execute('''
            INSERT INTO portfolio_status
            (total_pot, vault_amount, active_positions, completed_trades, total_profit)
            VALUES (?, ?, ?, ?, ?)
            ''', (self.total_pot, self.vault_amount, len(self.active_positions),
                  len(self.completed_trades), total_profit))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Error updating portfolio status: {e}")

    def run_trading_loop(self):
        """Run high-speed real-time trading loop with millisecond processing"""
        try:
            self.logger.info("🎯 Starting HIGH-SPEED real-time trading loop...")

            # Initialize trading states for all symbols
            self.initialize_trading_states()

            # Set running state
            self.is_running = True

            # PURE EVENT-DRIVEN SYSTEM - NO INDEPENDENT TRADING LOOP
            # Trading only happens when triggered by data fetch events
            self.logger.info("🎯 Trading engine is now PURE EVENT-DRIVEN")
            self.logger.info("⚡ Trading will ONLY happen immediately after data fetch")
            self.logger.info("🔄 No independent trading cycles - waiting for data fetch triggers...")

            # Just keep the engine alive and wait for events
            while self.is_running:
                try:
                    # Only minimal background monitoring - NO TRADING
                    time.sleep(3600)  # Check every hour just to stay alive
                    if self.is_market_hours(datetime.datetime.now()):
                        self.logger.info("📊 Trading engine alive - waiting for data fetch events...")
                    else:
                        self.logger.info("🌙 Market closed - trading engine in standby mode")

                except KeyboardInterrupt:
                    self.logger.info("🛑 Trading engine stopped by user")
                    break

                except Exception as e:
                    self.logger.error(f"Error in trading loop: {e}")
                    time.sleep(1)  # Wait 1 second on error, then continue

        except Exception as e:
            self.logger.error(f"Error in run_trading_loop: {e}")
            self.is_running = False

    def process_background_monitoring(self):
        """Background monitoring for system health and position management"""
        try:
            # Log current status
            available_funds = self.get_available_funds()
            self.logger.info(f"📊 Background Check: {len(self.active_positions)} positions | ₹{available_funds/100000:.1f}L available")

            # Check for any stale positions that need attention
            self.check_position_health()

        except Exception as e:
            self.logger.error(f"Error in background monitoring: {e}")

    def check_position_health(self):
        """Check health of active positions"""
        try:
            current_time = datetime.datetime.now()
            stale_positions = 0

            for symbol, position in self.active_positions.items():
                position_time = position.get('timestamp', current_time)
                age_hours = (current_time - position_time).total_seconds() / 3600

                if age_hours > 24:  # Position older than 24 hours
                    stale_positions += 1

            if stale_positions > 0:
                self.logger.warning(f"⚠️ {stale_positions} positions are older than 24 hours")

        except Exception as e:
            self.logger.error(f"Error checking position health: {e}")

    def process_realtime_trading_cycle(self):
        """Process trading decisions in real-time with parallel processing"""
        import threading
        from concurrent.futures import ThreadPoolExecutor, as_completed

        try:
            # Get symbols that have new data since last check
            symbols_with_new_data = self.get_symbols_with_new_data()

            if not symbols_with_new_data:
                return  # No new data, skip processing

            self.logger.info(f"🔄 Processing {len(symbols_with_new_data)} symbols with new data...")

            # Process symbols in parallel for maximum speed
            with ThreadPoolExecutor(max_workers=10) as executor:
                # Submit all symbol processing tasks
                future_to_symbol = {
                    executor.submit(self.process_symbol_realtime, symbol): symbol
                    for symbol in symbols_with_new_data
                }

                # Process results as they complete (real-time)
                for future in as_completed(future_to_symbol):
                    symbol = future_to_symbol[future]
                    try:
                        result = future.result()
                        if result:
                            # Signal generated - log immediately
                            self.logger.info(f"⚡ REAL-TIME SIGNAL: {result['action']} {symbol} @ ₹{result['price']}")
                    except Exception as e:
                        self.logger.error(f"Error processing {symbol}: {e}")

        except Exception as e:
            self.logger.error(f"Error in realtime trading cycle: {e}")

    def get_symbols_with_new_data(self):
        """Get symbols that have new data since last check"""
        try:
            # Check database for symbols with data newer than last processed time
            from database_setup import DatabaseManager
            db = DatabaseManager()
            session = db.get_session()

            # Get current time minus 2 minutes (to catch recent data)
            cutoff_time = datetime.datetime.now() - datetime.timedelta(minutes=2)

            # Query for symbols with recent data
            from database_setup import TradingData
            recent_symbols = session.query(TradingData.symbol).filter(
                TradingData.timestamp >= cutoff_time
            ).distinct().all()

            session.close()

            return [symbol[0] for symbol in recent_symbols]

        except Exception as e:
            self.logger.error(f"Error getting symbols with new data: {e}")
            return []

    def process_symbol_realtime(self, symbol: str):
        """Process a single symbol for trading decisions in real-time"""
        try:
            # Get latest data for this symbol
            latest_data = self.get_latest_symbol_data(symbol, limit=7)  # Get last 7 intervals

            if len(latest_data) < 7:
                return None  # Not enough data for pattern analysis

            # Check for 4-FALL + 1-RISE pattern with 0.5% drop condition
            if self.detect_4fall_1rise_pattern_with_drop(latest_data):
                # Pattern detected - send signal to DB2 for confirmation
                latest_price = latest_data[0]['close_price']

                self.logger.info(f"🎯 FFFFR + 0.5% DROP DETECTED: {symbol} @ ₹{latest_price:.2f}")
                self.logger.info(f"📤 SENDING TO DB2 for 2-minute confirmation...")

                # Send signal to DB2 (confirmation_engine) instead of direct execution
                self._send_signal_to_db2(symbol, latest_price, 'BUY', 'FFFFR_0.5%_DROP')

                return {
                    'symbol': symbol,
                    'signal_type': 'BUY',
                    'price': latest_price,
                    'pattern': 'FFFFR_0.5%_DROP',
                    'status': 'SENT_TO_DB2'
                }

        except Exception as e:
            self.logger.error(f"Error processing symbol {symbol}: {e}")
            return None

    def get_available_funds(self):
        """Get currently available funds for trading"""
        try:
            # Calculate used funds from active positions
            used_funds = sum(pos.get('investment_amount', 0) for pos in self.active_positions.values())
            available = self.total_pot - used_funds

            # Log detailed fund calculation
            self.logger.debug(f"💰 Fund Calculation: Total={self.total_pot/100000:.1f}L, Used={used_funds/100000:.1f}L, Available={available/100000:.1f}L, Vault={self.vault_amount:.2f}")

            return max(0, available)  # Ensure non-negative (don't add vault to available - keep separate)
        except Exception as e:
            self.logger.error(f"Error calculating available funds: {e}")
            return 0

    def update_available_funds(self, amount_used: float):
        """Update available funds after making a trade"""
        try:
            # This will be reflected in get_available_funds() calculation
            self.logger.info(f"💰 Funds used: ₹{amount_used/100000:.1f}L | Remaining: ₹{self.get_available_funds()/100000:.1f}L")
        except Exception as e:
            self.logger.error(f"Error updating available funds: {e}")

    def process_immediate_analysis(self):
        """Process immediate trading analysis after data fetch (EVENT-DRIVEN)"""
        try:
            self.logger.info("⚡ STARTING IMMEDIATE TRADING ANALYSIS...")

            # 1. CRITICAL DATA VALIDATION: Check if all 222 symbols have at least 6 data points
            self.logger.info("🔍 STEP 1: Data Validation - Checking 222 symbols have sufficient data...")
            data_validation = self.validate_symbol_data_completeness()

            if not data_validation['all_symbols_ready']:
                self.logger.warning(f"⚠️ DATA INCOMPLETE: {data_validation['symbols_with_insufficient_data']} symbols lack 6+ data points")
                self.logger.warning(f"⚠️ Ready symbols: {data_validation['symbols_ready']}/222")
                self.logger.info("🔄 Proceeding with available symbols...")
            else:
                self.logger.info("✅ DATA VALIDATION PASSED: All 222 symbols have 6+ data points")

            # 2. Get symbols with fresh data (last 5 minutes)
            self.logger.info("🔍 STEP 2: Identifying symbols with fresh data...")
            fresh_symbols = self.get_symbols_with_fresh_data()

            if not fresh_symbols:
                self.logger.info("📊 No fresh data available for analysis")
                return

            self.logger.info(f"🔍 STEP 3: Analyzing {len(fresh_symbols)} symbols with fresh data...")

            # Process each symbol individually for real-time signals
            signals_generated = 0

            for symbol in fresh_symbols:
                try:
                    # Analyze this symbol immediately
                    signal = self.analyze_symbol_for_signal(symbol)
                    if signal:
                        signals_generated += 1
                        self.logger.info(f"🎯 SIGNAL GENERATED: {signal['action']} {symbol} @ ₹{signal['price']}")

                        # Update available funds immediately
                        self.update_portfolio_after_signal(signal)

                except Exception as e:
                    self.logger.error(f"Error analyzing {symbol}: {e}")

            if signals_generated > 0:
                self.logger.info(f"✅ Generated {signals_generated} trading signals")
                self.logger.info(f"💰 Available funds: ₹{self.get_available_funds()/100000:.1f}L")
            else:
                self.logger.info("📊 No trading signals generated from fresh data")

        except Exception as e:
            self.logger.error(f"Error in immediate analysis: {e}")

    def validate_symbol_data_completeness(self):
        """Validate that all 222 symbols have at least 6 data points for trading analysis"""
        try:
            import sqlite3
            conn = sqlite3.connect(self.market_db_path)
            cursor = conn.cursor()

            # Get all symbols from Excel file
            all_symbols = self.symbol_manager.get_all_symbols()

            symbols_ready = 0
            symbols_with_insufficient_data = 0
            insufficient_symbols = []

            for symbol in all_symbols:
                # Count data points for this symbol
                cursor.execute("""
                    SELECT COUNT(*) FROM trading_data
                    WHERE symbol = ?
                    ORDER BY timestamp DESC
                """, (symbol,))

                count = cursor.fetchone()[0]

                if count >= 6:
                    symbols_ready += 1
                else:
                    symbols_with_insufficient_data += 1
                    insufficient_symbols.append(f"{symbol}({count})")

            conn.close()

            all_symbols_ready = symbols_with_insufficient_data == 0

            if not all_symbols_ready:
                self.logger.warning(f"⚠️ Symbols with insufficient data: {', '.join(insufficient_symbols[:10])}{'...' if len(insufficient_symbols) > 10 else ''}")

            return {
                'all_symbols_ready': all_symbols_ready,
                'symbols_ready': symbols_ready,
                'symbols_with_insufficient_data': symbols_with_insufficient_data,
                'total_symbols': len(all_symbols),
                'insufficient_symbols': insufficient_symbols
            }

        except Exception as e:
            self.logger.error(f"Error validating symbol data completeness: {e}")
            return {
                'all_symbols_ready': False,
                'symbols_ready': 0,
                'symbols_with_insufficient_data': 222,
                'total_symbols': 222,
                'insufficient_symbols': []
            }

    def get_symbols_with_fresh_data(self):
        """Get symbols that have data updated in the last 5 minutes"""
        try:
            from database_setup import DatabaseManager, TradingData
            db = DatabaseManager()
            session = db.get_session()

            # Get data from last 5 minutes
            cutoff_time = datetime.datetime.now() - datetime.timedelta(minutes=5)

            fresh_symbols = session.query(TradingData.symbol).filter(
                TradingData.timestamp >= cutoff_time
            ).distinct().all()

            session.close()

            return [symbol[0] for symbol in fresh_symbols]

        except Exception as e:
            self.logger.error(f"Error getting fresh symbols: {e}")
            return []

    def analyze_symbol_for_signal(self, symbol: str):
        """Analyze a single symbol for trading signals with detailed logging"""
        try:
            # Get latest 6 intervals for pattern analysis (4-FALL + 1-RISE STRATEGY)
            latest_data = self.get_latest_symbol_data(symbol, limit=6)

            if len(latest_data) < 6:
                self.logger.info(f"📊 {symbol}: Insufficient data - only {len(latest_data)} intervals available (need 6)")
                return None

            # DETAILED ANALYSIS LOGGING - Show last 6 data points and price differences
            self._log_detailed_symbol_analysis_4plus1(symbol, latest_data)

            # Check for 4-FALL + 1-RISE pattern with 0.5% drop (NEW STRATEGY)
            if self.detect_4fall_1rise_pattern_with_drop(latest_data):
                latest_price = latest_data[0]['close_price']

                # Generate BUY signal and send to DB2
                self.logger.info(f"🟢 {symbol}: FFFFR + 0.5% DROP PATTERN DETECTED! Sending to DB2 at ₹{latest_price:.2f}")

                # Send to DB2 for confirmation instead of direct execution
                self._send_signal_to_db2(symbol, latest_price, 'BUY', 'FFFFR_0.5%_DROP')

                return {
                    'symbol': symbol,
                    'signal_type': 'BUY',
                    'price': latest_price,
                    'pattern': 'FFFFR_0.5%_DROP',
                    'status': 'SENT_TO_DB2'
                }
            else:
                # Log why pattern was not detected
                self._log_pattern_rejection_reason_4plus1(symbol, latest_data)

        except Exception as e:
            self.logger.error(f"❌ Error analyzing {symbol}: {e}")
            return None

    def _log_detailed_symbol_analysis_4plus1(self, symbol: str, latest_data: List[Dict]):
        """Log detailed analysis of symbol's last 6 data points (4-FALL + 1-RISE STRATEGY)"""
        try:
            # Extract prices and calculate differences
            prices = [data['close_price'] for data in reversed(latest_data)]  # Chronological order
            price_diffs = []
            trends = []

            for i in range(1, len(prices)):
                diff = prices[i] - prices[i-1]
                price_diffs.append(diff)
                if diff > 0:
                    trends.append("RISE")
                elif diff < 0:
                    trends.append("FALL")
                else:
                    trends.append("FLAT")

            # Calculate percentage drop for 0.5% condition
            start_price = prices[0]
            lowest_price = min(prices[:-1]) if len(prices) > 1 else start_price
            percentage_drop = ((start_price - lowest_price) / start_price) * 100

            # Log detailed analysis
            self.logger.info(f"📊 {symbol}: Last 6 Data Points Analysis (4-FALL + 1-RISE + 0.5% DROP Strategy)")
            self.logger.info(f"   Prices: {[f'₹{p:.2f}' for p in prices]}")
            self.logger.info(f"   Diffs:  {[f'{d:+.2f}' for d in price_diffs]}")
            self.logger.info(f"   Trends: {trends}")
            self.logger.info(f"   Pattern: {''.join([t[0] for t in trends])} (need: FFFFR)")
            self.logger.info(f"   Drop: {percentage_drop:.2f}% (need: ≥0.5%)")

            # Check if pattern matches 4-FALL + 1-RISE (exactly 5 trends)
            if len(trends) == 5:
                expected_pattern = ["FALL", "FALL", "FALL", "FALL", "RISE"]
                pattern_match = trends == expected_pattern
                drop_sufficient = percentage_drop >= 0.5

                if pattern_match and drop_sufficient:
                    self.logger.info(f"✅ {symbol}: FFFFR + 0.5% DROP PATTERN FOUND!")
                elif pattern_match:
                    self.logger.info(f"⚠️ {symbol}: FFFFR pattern found but insufficient drop: {percentage_drop:.2f}%")
                else:
                    self.logger.info(f"❌ {symbol}: No FFFFR pattern - {trends}")
            else:
                self.logger.info(f"❌ {symbol}: Need 5 trends, got {len(trends)}")

        except Exception as e:
            self.logger.error(f"Error in detailed analysis for {symbol}: {e}")

    def _log_pattern_rejection_reason_4plus1(self, symbol: str, latest_data: List[Dict]):
        """Log why the 4-FALL + 1-RISE + 0.5% DROP pattern was rejected"""
        try:
            prices = [data['close_price'] for data in reversed(latest_data)]
            trends = []

            for i in range(1, len(prices)):
                diff = prices[i] - prices[i-1]
                if diff > 0:
                    trends.append("RISE")
                elif diff < 0:
                    trends.append("FALL")
                else:
                    trends.append("FLAT")

            self.logger.info(f"❌ {symbol}: No 4-FALL+2-RISE pattern found")
            self.logger.info(f"   Expected: [FALL, FALL, FALL, FALL, RISE, RISE]")
            self.logger.info(f"   Actual:   {trends}")

        except Exception as e:
            self.logger.error(f"Error logging rejection reason for {symbol}: {e}")

    def _log_detailed_symbol_analysis(self, symbol: str, latest_data: List[Dict]):
        """Log detailed analysis of symbol's last 6 data points"""
        try:
            # Add 1-second delay for readability
            time.sleep(1)

            # Log to console
            self.logger.info(f"")
            self.logger.info(f"📊 ═══════════════ ANALYZING {symbol} ═══════════════")
            self.logger.info(f"📈 LAST 6 DATA POINTS (Most Recent First):")

            # Also broadcast to frontend
            self._broadcast_log('ANALYSIS', f"📊 ANALYZING {symbol} - Last 6 Data Points", 'info')

            for i, data in enumerate(latest_data):
                timestamp = data['timestamp']
                price = data['close_price']

                # Calculate change from previous point
                if i < len(latest_data) - 1:
                    prev_price = latest_data[i + 1]['close_price']
                    change = price - prev_price
                    change_pct = (change / prev_price) * 100
                    trend = "📈 RISE" if change > 0 else "📉 FALL"
                    sign = "+" if change >= 0 else ""
                    log_msg = f"   {i+1}. {timestamp}: ₹{price:.2f} ({trend} {sign}{change:.2f} / {sign}{change_pct:.2f}%)"
                    self.logger.info(log_msg)
                    # Broadcast each data point to frontend
                    self._broadcast_log('ANALYSIS', f"{symbol} #{i+1}: ₹{price:.2f} ({trend} {sign}{change:.2f})", 'info')
                else:
                    log_msg = f"   {i+1}. {timestamp}: ₹{price:.2f} (Starting Point)"
                    self.logger.info(log_msg)
                    self._broadcast_log('ANALYSIS', f"{symbol} #{i+1}: ₹{price:.2f} (Starting Point)", 'info')

            self.logger.info(f"📊 ═══════════════════════════════════════════════════")

        except Exception as e:
            self.logger.error(f"Error logging detailed analysis for {symbol}: {e}")

    def _log_pattern_rejection_reason(self, symbol: str, latest_data: List[Dict]):
        """Log why the 4-FALL+2-RISE pattern was not detected"""
        try:
            # Analyze the trend sequence
            trends = []
            for i in range(len(latest_data) - 1):
                current_price = latest_data[i]['close_price']
                prev_price = latest_data[i + 1]['close_price']
                trend = "RISE" if current_price > prev_price else "FALL"
                trends.append(trend)

            # Expected pattern: [RISE, FALL, FALL, FALL, FALL, FALL] (most recent first)
            expected_pattern = ["RISE", "FALL", "FALL", "FALL", "FALL", "FALL"]
            actual_pattern = trends

            # Log to console
            self.logger.info(f"❌ {symbol}: Pattern NOT matched")
            self.logger.info(f"   Expected: {' → '.join(expected_pattern)}")
            self.logger.info(f"   Actual:   {' → '.join(actual_pattern)}")

            # Broadcast to frontend
            self._broadcast_log('PATTERN', f"❌ {symbol}: Pattern NOT matched", 'warning')
            self._broadcast_log('PATTERN', f"Expected: {' → '.join(expected_pattern)}", 'info')
            self._broadcast_log('PATTERN', f"Actual: {' → '.join(actual_pattern)}", 'info')

            # Find first mismatch
            for i, (expected, actual) in enumerate(zip(expected_pattern, actual_pattern)):
                if expected != actual:
                    mismatch_msg = f"❌ Mismatch at position {i+1}: Expected {expected}, got {actual}"
                    self.logger.info(f"   {mismatch_msg}")
                    self._broadcast_log('PATTERN', f"{symbol}: {mismatch_msg}", 'warning')
                    break

            self.logger.info(f"")

        except Exception as e:
            self.logger.error(f"Error logging pattern rejection for {symbol}: {e}")

    def get_available_funds(self):
        """Get currently available funds for trading (REAL-TIME CALCULATION)"""
        try:
            # Calculate used funds from active positions
            used_funds = sum(pos.get('investment_amount', 0) for pos in self.active_positions.values())
            available = self.total_pot - used_funds

            # Log current fund status for transparency
            active_count = len(self.active_positions)
            if active_count > 0:
                self.logger.info(f"💰 Fund Status: {active_count} active positions using ₹{used_funds/100000:.1f}L")
                self.logger.info(f"💰 Available: ₹{available/100000:.1f}L of ₹{self.total_pot/100000:.1f}L total")

            return max(0, available)
        except Exception as e:
            self.logger.error(f"Error calculating available funds: {e}")
            return 0

    def update_portfolio_after_signal(self, signal):
        """Update portfolio after generating a signal"""
        try:
            # Add to active positions for fund tracking
            self.active_positions[signal['symbol']] = {
                'symbol': signal['symbol'],
                'action': signal['action'],
                'price': signal['price'],
                'quantity': signal['quantity'],
                'investment_amount': signal['investment_amount'],
                'timestamp': datetime.datetime.now()
            }

            self.logger.info(f"💰 Portfolio updated: {signal['symbol']} position added")

        except Exception as e:
            self.logger.error(f"Error updating portfolio: {e}")

    def get_trading_status(self) -> Dict:
        """Get current trading status"""
        return {
            'is_running': self.is_running,
            'total_pot': self.total_pot,
            'vault_amount': self.vault_amount,
            'available_funds': self.get_available_funds(),
            'active_positions': len(self.active_positions),
            'completed_trades': len(self.completed_trades),
            'symbols_tracked': len(self.trading_states)
        }
